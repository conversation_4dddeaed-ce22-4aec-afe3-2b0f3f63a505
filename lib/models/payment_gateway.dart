 import 'package:flutter/material.dart';

/// Model representing a payment gateway configuration from server
class PaymentGateway {
  final String id;
  final String name;
  final String displayName;
  final String subtitle;
  final bool isEnabled;
  final String? iconUrl;
  final String? iconName;
  final String? colorHex;
  final int? priority;
  final Map<String, dynamic>? metadata;

  PaymentGateway({
    required this.id,
    required this.name,
    required this.displayName,
    required this.subtitle,
    this.isEnabled = true,
    this.iconUrl,
    this.iconName,
    this.colorHex,
    this.priority,
    this.metadata,
  });

  factory PaymentGateway.fromJson(Map<String, dynamic> json) {
    return PaymentGateway(
      id: json['id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      displayName: json['display_name']?.toString() ?? json['name']?.toString() ?? '',
      subtitle: json['subtitle']?.toString() ?? '',
      isEnabled: json['is_enabled'] ?? json['enabled'] ?? true,
      iconUrl: json['icon_url']?.toString(),
      iconName: json['icon_name']?.toString(),
      colorHex: json['color']?.toString() ?? json['color_hex']?.toString(),
      priority: json['priority'] ?? 0,
      metadata: json['metadata'] is Map<String, dynamic> ? json['metadata'] : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'display_name': displayName,
      'subtitle': subtitle,
      'is_enabled': isEnabled,
      'icon_url': iconUrl,
      'icon_name': iconName,
      'color': colorHex,
      'priority': priority,
      'metadata': metadata,
    };
  }

  /// Get the icon for this payment gateway
  IconData getIcon() {
    // Map icon names to Flutter icons
    switch (iconName?.toLowerCase()) {
      case 'phone_android':
      case 'phonepe':
        return Icons.phone_android;
      case 'payment':
      case 'payu':
        return Icons.payment;
      case 'account_balance_wallet':
      case 'cashfree':
        return Icons.account_balance_wallet;
      case 'credit_card':
        return Icons.credit_card;
      case 'account_balance':
        return Icons.account_balance;
      default:
        return Icons.payment; // Default icon
    }
  }

  /// Get the color for this payment gateway
  Color getColor() {
    if (colorHex != null && colorHex!.isNotEmpty) {
      try {
        // Remove # if present and ensure it's a valid hex color
        String hex = colorHex!.replaceAll('#', '');
        if (hex.length == 6) {
          hex = 'FF$hex'; // Add alpha channel
        }
        return Color(int.parse(hex, radix: 16));
      } catch (e) {
        // Fall back to default colors if parsing fails
      }
    }

    // Default colors based on gateway name
    switch (name.toLowerCase()) {
      case 'phonepe':
        return const Color(0xFF5F259F);
      case 'payu':
        return const Color(0xFF00A651);
      case 'cashfree':
        return const Color(0xFF0066CC);
      default:
        return const Color(0xFF4776E6); // Default blue
    }
  }

  /// Create a default PhonePe gateway (fallback)
  static PaymentGateway defaultPhonePe() {
    return PaymentGateway(
      id: 'phonepe',
      name: 'phonepe',
      displayName: 'PhonePe',
      subtitle: 'Fast & secure payments',
      isEnabled: true,
      iconName: 'phone_android',
      colorHex: '#5F259F',
      priority: 1,
    );
  }

  /// Create a default PayU gateway (fallback)
  static PaymentGateway defaultPayU() {
    return PaymentGateway(
      id: 'payu',
      name: 'payu',
      displayName: 'PayU',
      subtitle: 'Secure online payments',
      isEnabled: true,
      iconName: 'payment',
      colorHex: '#00A651',
      priority: 2,
    );
  }

  /// Create a default Cashfree gateway (fallback)
  static PaymentGateway defaultCashfree() {
    return PaymentGateway(
      id: 'cashfree',
      name: 'cashfree',
      displayName: 'Cashfree',
      subtitle: 'Fast & reliable payments',
      isEnabled: false, // Disabled by default as it was commented out
      iconName: 'account_balance_wallet',
      colorHex: '#0066CC',
      priority: 3,
    );
  }

  /// Get default payment gateways (fallback when server doesn't provide them)
  static List<PaymentGateway> getDefaultGateways() {
    return [
      defaultPhonePe(),
      defaultPayU(),
      // defaultCashfree(), // Commented out as it was disabled in original code
    ];
  }

  @override
  String toString() {
    return 'PaymentGateway(id: $id, name: $name, displayName: $displayName, isEnabled: $isEnabled)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PaymentGateway && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}