import 'package:intl/intl.dart';

/// Model for charging transaction charger information
class ChargingTransactionCharger {
  final String uid;
  final String chargerName;

  ChargingTransactionCharger({
    required this.uid,
    required this.chargerName,
  });

  factory ChargingTransactionCharger.fromJson(Map<String, dynamic> json) {
    return ChargingTransactionCharger(
      uid: json['uid'] ?? '',
      chargerName: json['charger_name'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'charger_name': chargerName,
    };
  }
}

/// Model for charging transaction location information
class ChargingTransactionLocation {
  final String uid;
  final String address;

  ChargingTransactionLocation({
    required this.uid,
    required this.address,
  });

  factory ChargingTransactionLocation.fromJson(Map<String, dynamic> json) {
    return ChargingTransactionLocation(
      uid: json['uid'] ?? '',
      address: json['address'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'address': address,
    };
  }
}

/// Model for individual charging transaction
class ChargingTransaction {
  final int id;
  final String chargerUid;
  final String invoiceNumber;
  final String connectorId;
  final String chargerSerialNumber;
  final double evChargingRate;
  final String bookingId;
  final String? vehicleId;
  final String status;
  final DateTime createdAt;
  final String locationUid;
  final ChargingTransactionCharger charger;
  final ChargingTransactionLocation location;

  ChargingTransaction({
    required this.id,
    required this.chargerUid,
    required this.invoiceNumber,
    required this.connectorId,
    required this.chargerSerialNumber,
    required this.evChargingRate,
    required this.bookingId,
    this.vehicleId,
    required this.status,
    required this.createdAt,
    required this.locationUid,
    required this.charger,
    required this.location,
  });

  factory ChargingTransaction.fromJson(Map<String, dynamic> json) {
    return ChargingTransaction(
      id: json['id'] ?? 0,
      chargerUid: json['charger_uid'] ?? '',
      invoiceNumber: json['invoice_number'] ?? '',
      connectorId: json['connector_id'] ?? '',
      chargerSerialNumber: json['charger_serial_number'] ?? '',
      evChargingRate: (json['ev_charging_rate'] ?? 0).toDouble(),
      bookingId: json['booking_id'] ?? '',
      vehicleId: json['vehicle_id'],
      status: json['status'] ?? '',
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      locationUid: json['location_uid'] ?? '',
      charger: ChargingTransactionCharger.fromJson(json['charger'] ?? {}),
      location: ChargingTransactionLocation.fromJson(json['location'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'charger_uid': chargerUid,
      'invoice_number': invoiceNumber,
      'connector_id': connectorId,
      'charger_serial_number': chargerSerialNumber,
      'ev_charging_rate': evChargingRate,
      'booking_id': bookingId,
      'vehicle_id': vehicleId,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'location_uid': locationUid,
      'charger': charger.toJson(),
      'location': location.toJson(),
    };
  }

  /// Get formatted date string for display
  String get formattedDate {
    return DateFormat('MMM dd, yyyy').format(createdAt);
  }

  /// Get formatted time string for display
  String get formattedTime {
    return DateFormat('hh:mm a').format(createdAt);
  }

  /// Get formatted date and time string for display
  String get formattedDateTime {
    return DateFormat('MMM dd, yyyy • hh:mm a').format(createdAt);
  }

  /// Get status color based on transaction status
  String get statusColor {
    switch (status.toUpperCase()) {
      case 'COMPLETED':
        return '#22C55E'; // Green
      case 'REJECTED':
        return '#EF4444'; // Red
      case 'PENDING':
        return '#F59E0B'; // Orange
      case 'IN_PROGRESS':
        return '#3B82F6'; // Blue
      default:
        return '#6B7280'; // Gray
    }
  }

  /// Get user-friendly status text
  String get statusText {
    switch (status.toUpperCase()) {
      case 'COMPLETED':
        return 'Completed';
      case 'REJECTED':
        return 'Failed';
      case 'PENDING':
        return 'Pending';
      case 'IN_PROGRESS':
        return 'In Progress';
      default:
        return status;
    }
  }

  /// Get charging rate display text
  String get chargingRateText {
    return '${evChargingRate.toStringAsFixed(0)} kW';
  }

  /// Get connector display text
  String get connectorText {
    return 'Connector $connectorId';
  }

  /// Check if transaction is successful
  bool get isSuccessful {
    return status.toUpperCase() == 'COMPLETED';
  }

  /// Check if transaction is failed
  bool get isFailed {
    return status.toUpperCase() == 'REJECTED';
  }

  /// Check if transaction is pending
  bool get isPending {
    return status.toUpperCase() == 'PENDING';
  }

  /// Check if transaction is in progress
  bool get isInProgress {
    return status.toUpperCase() == 'IN_PROGRESS';
  }
}

/// Model for charging transactions API response
class ChargingTransactionsResponse {
  final List<ChargingTransaction> transactions;
  final bool success;

  ChargingTransactionsResponse({
    required this.transactions,
    required this.success,
  });

  factory ChargingTransactionsResponse.fromJson(Map<String, dynamic> json) {
    final List<dynamic> transactionsJson = json['data'] ?? [];
    final List<ChargingTransaction> transactions = transactionsJson
        .map((transactionJson) => ChargingTransaction.fromJson(transactionJson))
        .toList();

    return ChargingTransactionsResponse(
      transactions: transactions,
      success: json['success'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': transactions.map((transaction) => transaction.toJson()).toList(),
      'success': success,
    };
  }

  /// Get transactions sorted by date (newest first)
  List<ChargingTransaction> get sortedByDateDesc {
    final sortedList = List<ChargingTransaction>.from(transactions);
    sortedList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return sortedList;
  }

  /// Get transactions sorted by date (oldest first)
  List<ChargingTransaction> get sortedByDateAsc {
    final sortedList = List<ChargingTransaction>.from(transactions);
    sortedList.sort((a, b) => a.createdAt.compareTo(b.createdAt));
    return sortedList;
  }

  /// Filter transactions by status
  List<ChargingTransaction> filterByStatus(String status) {
    return transactions.where((transaction) => 
        transaction.status.toUpperCase() == status.toUpperCase()).toList();
  }

  /// Filter transactions by date range
  List<ChargingTransaction> filterByDateRange(DateTime startDate, DateTime endDate) {
    return transactions.where((transaction) => 
        transaction.createdAt.isAfter(startDate) && 
        transaction.createdAt.isBefore(endDate)).toList();
  }

  /// Get successful transactions count
  int get successfulCount {
    return transactions.where((transaction) => transaction.isSuccessful).length;
  }

  /// Get failed transactions count
  int get failedCount {
    return transactions.where((transaction) => transaction.isFailed).length;
  }

  /// Get pending transactions count
  int get pendingCount {
    return transactions.where((transaction) => transaction.isPending).length;
  }
}
