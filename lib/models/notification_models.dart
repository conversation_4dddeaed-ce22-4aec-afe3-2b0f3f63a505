/// Notification models and types for the EcoPlug app
/// Defines the structure and types of notifications used throughout the application
library;

/// Enum defining different types of notifications
enum NotificationType {
  /// General app notifications and updates
  general,
  
  /// Real-time charging session notifications
  charging,
  
  /// Scheduled reminders and alerts
  reminder,
  
  /// Promotional content and offers
  promotion,
  
  /// System maintenance and important updates
  maintenance,
}

/// Model for notification actions (buttons that appear in notifications)
class NotificationAction {
  /// Unique identifier for the action
  final String id;
  
  /// Display text for the action button
  final String title;
  
  /// Optional icon for the action (Android resource name)
  final String? icon;
  
  /// Whether tapping this action should open the app UI
  final bool showsUserInterface;
  
  /// Whether this action allows generated replies (Android)
  final bool allowGeneratedReplies;
  
  /// Whether this is a destructive action (iOS)
  final bool destructive;
  
  /// Input fields for this action (for reply actions)
  final List<NotificationActionInput>? inputs;

  const NotificationAction({
    required this.id,
    required this.title,
    this.icon,
    this.showsUserInterface = true,
    this.allowGeneratedReplies = false,
    this.destructive = false,
    this.inputs,
  });

  /// Create a stop charging action
  static const NotificationAction stopCharging = NotificationAction(
    id: 'stop_charging',
    title: 'Stop Charging',
    icon: 'ic_stop',
    showsUserInterface: true,
    destructive: true,
  );

  /// Create a view details action
  static const NotificationAction viewDetails = NotificationAction(
    id: 'view_details',
    title: 'View Details',
    icon: 'ic_info',
    showsUserInterface: true,
  );

  /// Create a dismiss action
  static const NotificationAction dismiss = NotificationAction(
    id: 'dismiss',
    title: 'Dismiss',
    icon: 'ic_close',
    showsUserInterface: false,
  );

  /// Create a reply action (for future use)
  static const NotificationAction reply = NotificationAction(
    id: 'reply',
    title: 'Reply',
    icon: 'ic_reply',
    showsUserInterface: false,
    allowGeneratedReplies: true,
    inputs: [
      NotificationActionInput(
        label: 'Reply',
        allowFreeFormInput: true,
      ),
    ],
  );
}

/// Model for notification action inputs (for reply functionality)
class NotificationActionInput {
  /// Label for the input field
  final String label;
  
  /// Whether free-form text input is allowed
  final bool allowFreeFormInput;
  
  /// Predefined choices for the input
  final List<String>? choices;
  
  /// Allowed MIME types for attachments
  final Set<String>? allowedMimeTypes;

  const NotificationActionInput({
    required this.label,
    this.allowFreeFormInput = true,
    this.choices,
    this.allowedMimeTypes,
  });
}

/// Model for charging session notification data
class ChargingNotificationData {
  /// Whether charging is currently active
  final bool isCharging;
  
  /// Current charge percentage (0.0 to 1.0)
  final double chargePercentage;
  
  /// Current power output (e.g., "22 kW")
  final String currentPower;
  
  /// Energy delivered so far (e.g., "15.5 kWh")
  final String energyDelivered;
  
  /// Current price/cost (e.g., "₹125.50")
  final String currentPrice;
  
  /// CO2 saved (e.g., "2.5 kg")
  final String co2Saved;
  
  /// Charging timer/duration (e.g., "45 min")
  final String chargingTimer;
  
  /// Station name or identifier
  final String? stationName;
  
  /// Connector ID
  final String? connectorId;
  
  /// Transaction ID
  final String? transactionId;

  const ChargingNotificationData({
    required this.isCharging,
    required this.chargePercentage,
    required this.currentPower,
    required this.energyDelivered,
    required this.currentPrice,
    required this.co2Saved,
    required this.chargingTimer,
    this.stationName,
    this.connectorId,
    this.transactionId,
  });

  /// Create a copy with updated values
  ChargingNotificationData copyWith({
    bool? isCharging,
    double? chargePercentage,
    String? currentPower,
    String? energyDelivered,
    String? currentPrice,
    String? co2Saved,
    String? chargingTimer,
    String? stationName,
    String? connectorId,
    String? transactionId,
  }) {
    return ChargingNotificationData(
      isCharging: isCharging ?? this.isCharging,
      chargePercentage: chargePercentage ?? this.chargePercentage,
      currentPower: currentPower ?? this.currentPower,
      energyDelivered: energyDelivered ?? this.energyDelivered,
      currentPrice: currentPrice ?? this.currentPrice,
      co2Saved: co2Saved ?? this.co2Saved,
      chargingTimer: chargingTimer ?? this.chargingTimer,
      stationName: stationName ?? this.stationName,
      connectorId: connectorId ?? this.connectorId,
      transactionId: transactionId ?? this.transactionId,
    );
  }

  /// Convert to JSON for payload storage
  Map<String, dynamic> toJson() {
    return {
      'isCharging': isCharging,
      'chargePercentage': chargePercentage,
      'currentPower': currentPower,
      'energyDelivered': energyDelivered,
      'currentPrice': currentPrice,
      'co2Saved': co2Saved,
      'chargingTimer': chargingTimer,
      'stationName': stationName,
      'connectorId': connectorId,
      'transactionId': transactionId,
    };
  }

  /// Create from JSON payload
  factory ChargingNotificationData.fromJson(Map<String, dynamic> json) {
    return ChargingNotificationData(
      isCharging: json['isCharging'] ?? false,
      chargePercentage: (json['chargePercentage'] ?? 0.0).toDouble(),
      currentPower: json['currentPower'] ?? '0 kW',
      energyDelivered: json['energyDelivered'] ?? '0 kWh',
      currentPrice: json['currentPrice'] ?? '₹0',
      co2Saved: json['co2Saved'] ?? '0 kg',
      chargingTimer: json['chargingTimer'] ?? '0 min',
      stationName: json['stationName'],
      connectorId: json['connectorId'],
      transactionId: json['transactionId'],
    );
  }
}

/// Model for scheduled notification data
class ScheduledNotificationData {
  /// Unique identifier for the scheduled notification
  final String id;
  
  /// When the notification should be shown
  final DateTime scheduledTime;
  
  /// Notification type
  final NotificationType type;
  
  /// Notification title
  final String title;
  
  /// Notification body
  final String body;
  
  /// Optional payload data
  final String? payload;
  
  /// Whether this is a repeating notification
  final bool repeating;
  
  /// Repeat interval (if repeating)
  final Duration? repeatInterval;

  const ScheduledNotificationData({
    required this.id,
    required this.scheduledTime,
    required this.type,
    required this.title,
    required this.body,
    this.payload,
    this.repeating = false,
    this.repeatInterval,
  });
}

/// Model for notification settings/preferences
class NotificationSettings {
  /// Whether notifications are enabled globally
  final bool enabled;
  
  /// Settings for each notification type
  final Map<NotificationType, bool> typeSettings;
  
  /// Whether to show notifications when app is in foreground
  final bool showInForeground;
  
  /// Whether to use sound for notifications
  final bool useSound;
  
  /// Whether to use vibration for notifications
  final bool useVibration;
  
  /// Quiet hours start time (24-hour format)
  final int? quietHoursStart;
  
  /// Quiet hours end time (24-hour format)
  final int? quietHoursEnd;

  const NotificationSettings({
    this.enabled = true,
    this.typeSettings = const {
      NotificationType.general: true,
      NotificationType.charging: true,
      NotificationType.reminder: true,
      NotificationType.promotion: true,
      NotificationType.maintenance: true,
    },
    this.showInForeground = true,
    this.useSound = true,
    this.useVibration = true,
    this.quietHoursStart,
    this.quietHoursEnd,
  });

  /// Check if a specific notification type is enabled
  bool isTypeEnabled(NotificationType type) {
    return enabled && (typeSettings[type] ?? true);
  }

  /// Check if we're currently in quiet hours
  bool get isInQuietHours {
    if (quietHoursStart == null || quietHoursEnd == null) return false;
    
    final now = DateTime.now();
    final currentHour = now.hour;
    
    if (quietHoursStart! <= quietHoursEnd!) {
      // Same day quiet hours (e.g., 22:00 to 06:00 next day)
      return currentHour >= quietHoursStart! && currentHour < quietHoursEnd!;
    } else {
      // Overnight quiet hours (e.g., 22:00 to 06:00 next day)
      return currentHour >= quietHoursStart! || currentHour < quietHoursEnd!;
    }
  }
}
