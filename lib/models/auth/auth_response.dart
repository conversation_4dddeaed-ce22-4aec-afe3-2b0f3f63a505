// Auth response model for API integration

class AuthResponse {
  String? message;
  User? user;
  bool? success;

  AuthResponse({this.message, this.user, this.success});

  AuthResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    user = json['user'] != null ? User.fromJson(json['user']) : null;
    success = json['success'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['message'] = message;
    if (user != null) {
      data['user'] = user!.toJson();
    }
    data['success'] = success;
    return data;
  }
}

class User {
  int? id;
  String? uid;
  String? mobileNumber;
  String? name;
  String? email;
  String? domain;
  String? token;

  User({
    this.id,
    this.uid,
    this.mobileNumber,
    this.name,
    this.email,
    this.domain,
    this.token,
  });

  User.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    uid = json['uid'];
    mobileNumber = json['mobile_number'];
    name = json['name'];
    email = json['email'];
    domain = json['domain'];
    token = json['token'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['uid'] = uid;
    data['mobile_number'] = mobileNumber;
    data['name'] = name;
    data['email'] = email;
    data['domain'] = domain;
    data['token'] = token;
    return data;
  }
}
