import 'package:flutter/foundation.dart';

/// Model for OTP verification request that formats data correctly for the API
class OtpVerificationRequest {
  final String mobileNumber;
  final String otp;
  final String domain;

  OtpVerificationRequest({
    required this.mobileNumber,
    required this.otp,
    this.domain = 'eeil.online',
  });

  /// Convert to JSON with proper formatting for the API
  Map<String, dynamic> toJson() {
    // Format the mobile number (remove +91 prefix if present)
    final formattedNumber = mobileNumber.startsWith('+91')
        ? mobileNumber.substring(3)
        : mobileNumber;
    
    // Convert to integer if possible
    int? mobileNo;
    int? reqOtp;
    
    try {
      mobileNo = int.parse(formattedNumber);
      reqOtp = int.parse(otp);
    } catch (e) {
      debugPrint('Error parsing mobile number or OTP to integer: $e');
      // If parsing fails, we'll use the string values
    }
    
    return {
      'mobile_no': mobileNo ?? formattedNumber,
      'req_otp': reqOtp ?? otp,
      'domain': domain,
    };
  }
}
