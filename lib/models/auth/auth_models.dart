import 'package:flutter/foundation.dart';

/// Login request model
class LoginRequest {
  final String phoneNumber;

  LoginRequest({required this.phoneNumber});

  Map<String, dynamic> toJson() {
    // Remove +91 prefix if present to avoid database column length issues
    final formattedNumber =
        phoneNumber.startsWith('+91') && phoneNumber.length > 3
            ? phoneNumber.substring(3)
            : phoneNumber;

    // Try to convert to integer
    int? mobileNo;
    try {
      mobileNo = int.parse(formattedNumber);
    } catch (e) {
      debugPrint('Error parsing phone number to integer: $e');
      // If parsing fails, we'll use the string value
    }

    return {
      'mobile_no': mobileNo ?? formattedNumber,
      'domain': 'eeil.online',
    };
  }
}

/// Login response model
class LoginResponse {
  final String requestId;
  final String message;

  LoginResponse({
    required this.requestId,
    required this.message,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    // For OTP send response, there might not be a 'data' field
    // In this case, we'll use an empty requestId
    return LoginResponse(
      requestId: json['data'] != null ? (json['data']['requestId'] ?? '') : '',
      message: json['message'] ?? '',
    );
  }
}

/// OTP verification request model
class VerifyOtpRequest {
  final String requestId; // This will be the phone number
  final String otp;

  VerifyOtpRequest({
    required this.requestId,
    required this.otp,
  });

  Map<String, dynamic> toJson() {
    // Remove +91 prefix if present to avoid database column length issues
    final formattedNumber =
        requestId.startsWith('+91') ? requestId.substring(3) : requestId;

    // Convert to integer if possible
    int? mobileNo;
    int? reqOtp;

    try {
      mobileNo = int.parse(formattedNumber);
      reqOtp = int.parse(otp);
    } catch (e) {
      debugPrint('Error parsing mobile number or OTP to integer: $e');
      // If parsing fails, we'll use the string values
    }

    return {
      'mobile_no': mobileNo ?? formattedNumber,
      'req_otp': reqOtp ?? otp,
      'domain': 'eeil.online',
    };
  }
}

/// OTP verification response model
class VerifyOtpResponse {
  final String token;
  final String refreshToken;
  final bool isNewUser;
  final UserInfo? userInfo;

  VerifyOtpResponse({
    required this.token,
    required this.refreshToken,
    required this.isNewUser,
    this.userInfo,
  });

  factory VerifyOtpResponse.fromJson(Map<String, dynamic> json) {
    final userData = json['user'] ?? {};
    return VerifyOtpResponse(
      token: userData['token'] ?? '',
      refreshToken: '', // Not provided in the API
      isNewUser: false, // Not provided in the API
      userInfo: UserInfo(
        id: userData['id']?.toString() ?? '',
        name: userData['name'] ?? '',
        phoneNumber: userData['mobile_number'] ?? '',
        email: userData['email'] ?? '',
      ),
    );
  }
}

/// User info model
class UserInfo {
  final String id;
  final String name;
  final String phoneNumber;
  final String email;

  UserInfo({
    required this.id,
    required this.name,
    required this.phoneNumber,
    required this.email,
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) {
    return UserInfo(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      phoneNumber: json['phoneNumber'] ?? '',
      email: json['email'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phoneNumber': phoneNumber,
      'email': email,
    };
  }
}
