import 'package:flutter/foundation.dart';

/// Login request model that matches the Postman snippet format
class LoginRequestFixed {
  final dynamic mobileNo;

  LoginRequestFixed({
    required String phoneNumber,
  }) : mobileNo = _formatPhoneNumber(phoneNumber);

  /// Format the phone number as an integer if possible
  static dynamic _formatPhoneNumber(String phoneNumber) {
    // Remove +91 prefix if present
    final formattedNumber = phoneNumber.startsWith('+91')
        ? phoneNumber.substring(3)
        : phoneNumber;
    
    // Try to convert to integer
    try {
      return int.parse(formattedNumber);
    } catch (e) {
      debugPrint('Error parsing phone number to integer: $e');
      // Return as string if parsing fails
      return formattedNumber;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'mobile_no': mobileNo,
    };
  }
}
