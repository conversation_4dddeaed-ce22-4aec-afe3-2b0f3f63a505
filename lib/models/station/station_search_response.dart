class StationSearchResponse {
  final String? message;
  final bool success;
  final List<SearchStationData>? data;

  StationSearchResponse({
    this.message,
    required this.success,
    this.data,
  });

  factory StationSearchResponse.fromJson(Map<String, dynamic> json) {
    return StationSearchResponse(
      message: json['message'],
      success: json['success'] ?? false,
      data: json['data'] != null
          ? List<SearchStationData>.from(
              json['data'].map((x) => SearchStationData.fromJson(x)))
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'success': success,
      'data': data?.map((x) => x.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'StationSearchResponse(message: $message, success: $success, data: ${data?.length} stations)';
  }
}

class SearchStationData {
  final double? longitude;
  final double? latitude;
  final int? stationId;
  final String? uid;
  final String? name;
  final String? address;
  final String? city;
  final List<ConnectorType>? types;
  final String? status;

  SearchStationData({
    this.longitude,
    this.latitude,
    this.stationId,
    this.uid,
    this.name,
    this.address,
    this.city,
    this.types,
    this.status,
  });

  factory SearchStationData.fromJson(Map<String, dynamic> json) {
    return SearchStationData(
      longitude: json['longitude'] != null
          ? double.parse(json['longitude'].toString())
          : null,
      latitude: json['latitude'] != null
          ? double.parse(json['latitude'].toString())
          : null,
      stationId: json['station_id'],
      uid: json['uid'],
      name: json['name'],
      address: json['address'],
      city: json['city'],
      types: json['types'] != null ? _parseConnectorTypes(json['types']) : null,
      status: json['status'],
    );
  }

  // Helper method to parse connector types from both Map and List formats
  static List<ConnectorType> _parseConnectorTypes(dynamic types) {
    List<ConnectorType> result = [];

    if (types is List) {
      // Handle list format
      result = types
          .whereType<Map<String, dynamic>>()
          .map((e) => ConnectorType.fromJson(e))
          .toList();
    } else if (types is Map) {
      // Handle map format (e.g., {"0": {"name": "CCS2", "icon": "url"}, ...})
      result = types.values
          .whereType<Map<String, dynamic>>()
          .map((e) => ConnectorType.fromJson(e))
          .toList();
    }

    return result;
  }

  Map<String, dynamic> toJson() {
    return {
      'longitude': longitude,
      'latitude': latitude,
      'station_id': stationId,
      'uid': uid,
      'name': name,
      'address': address,
      'city': city,
      'types': types?.map((x) => x.toJson()).toList(),
      'status': status,
    };
  }

  // Helper method to get connector types as a list of names
  List<String> getConnectorTypeNames() {
    if (types == null || types!.isEmpty) {
      return ['Various'];
    }
    return types!.map((type) => type.name ?? 'Unknown').toList();
  }

  // Helper method to get connector types as a comma-separated string
  String getConnectorTypesString() {
    if (types == null || types!.isEmpty) {
      return 'Various';
    }
    return types!.map((type) => type.name ?? 'Unknown').join(', ');
  }

  @override
  String toString() {
    return 'SearchStationData(name: $name, address: $address, status: $status)';
  }
}

class ConnectorType {
  final String? name;
  final String? icon;

  ConnectorType({
    this.name,
    this.icon,
  });

  factory ConnectorType.fromJson(Map<String, dynamic> json) {
    return ConnectorType(
      name: json['name'],
      icon: json['icon'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'icon': icon,
    };
  }

  @override
  String toString() {
    return 'ConnectorType(name: $name)';
  }
}
