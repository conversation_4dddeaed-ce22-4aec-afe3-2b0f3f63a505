

/// Station marker model for map display
class StationMarker {
  final String id;
  final String name;
  final double latitude;
  final double longitude;
  final String status; // 'available', 'busy', 'offline'
  final int availableConnectors;
  final int totalConnectors;

  StationMarker({
    required this.id,
    required this.name,
    required this.latitude,
    required this.longitude,
    required this.status,
    required this.availableConnectors,
    required this.totalConnectors,
  });

  factory StationMarker.fromJson(Map<String, dynamic> json) {
    return StationMarker(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      latitude: (json['latitude'] as num?)?.toDouble() ?? 0.0,
      longitude: (json['longitude'] as num?)?.toDouble() ?? 0.0,
      status: json['status'] ?? 'offline',
      availableConnectors: json['availableConnectors'] ?? 0,
      totalConnectors: json['totalConnectors'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'latitude': latitude,
      'longitude': longitude,
      'status': status,
      'availableConnectors': availableConnectors,
      'totalConnectors': totalConnectors,
    };
  }
}

/// Station detail model
class StationDetail {
  final String? id;
  final String? name;
  final String? address;
  final double? latitude;
  final double? longitude;
  final String? status;
  final double? rating;
  final int? reviewCount;
  final String? operatingHours;
  final String? imageUrl;
  final List<Connector> connectors;
  final List<Amenity> amenities;
  final bool isBookmarked;
  final String? uid;

  StationDetail({
    required this.id,
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.status,
    required this.rating,
    required this.reviewCount,
    required this.operatingHours,
    required this.imageUrl,
    required this.connectors,
    required this.amenities,
    required this.isBookmarked,
    this.uid,
  });

  factory StationDetail.fromJson(Map<String, dynamic> json) {
    return StationDetail(
      id: json['id'] as String?,
      name: json['name'] as String?,
      address: json['address'] as String?,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      status: json['status'] as String?,
      rating: (json['rating'] as num?)?.toDouble(),
      reviewCount: json['reviewCount'] as int?,
      operatingHours: json['operatingHours'] as String?,
      imageUrl: json['imageUrl'] as String?,
      connectors: (json['connectors'] as List<dynamic>?)
              ?.map((e) => Connector.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      amenities: (json['amenities'] as List<dynamic>?)
              ?.map((e) => Amenity.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      isBookmarked: json['isBookmarked'] as bool? ?? false,
      uid: json['uid'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'status': status,
      'rating': rating,
      'reviewCount': reviewCount,
      'operatingHours': operatingHours,
      'imageUrl': imageUrl,
      'connectors': connectors.map((e) => e.toJson()).toList(),
      'amenities': amenities.map((e) => e.toJson()).toList(),
      'isBookmarked': isBookmarked,
      'uid': uid,
    };
  }
}

/// Connector detail model
class Connector {
  final int? soc;
  final String? evsesUid;
  final String? status;
  final String? type;
  final String? powerOutput;
  final int? maxPower;
  final double? pricePerKwh;
  final String? imageUrl;
  final String? availabilityStatus;

  Connector({
    this.soc,
    this.evsesUid,
    this.status,
    this.type,
    this.powerOutput,
    this.maxPower,
    this.pricePerKwh,
    this.imageUrl,
    this.availabilityStatus,
  });

  factory Connector.fromJson(Map<String, dynamic> json) {
    return Connector(
      soc: json['soc'],
      evsesUid: json['evsesUid'],
      status: json['status'],
      type: json['connector_type'],
      powerOutput: json['power_output'],
      maxPower: json['max_power'],
      pricePerKwh: (json['price_per_kwh'] as num?)?.toDouble(),
      imageUrl: json['connector_image'],
      availabilityStatus: json['availability_status'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'soc': soc,
      'evsesUid': evsesUid,
      'status': status,
      'connector_type': type,
      'power_output': powerOutput,
      'max_power': maxPower,
      'price_per_kwh': pricePerKwh,
      'connector_image': imageUrl,
      'availability_status': availabilityStatus,
    };
  }
}

/// Amenity model
class Amenity {
  final String name;
  final String icon;

  Amenity({
    required this.name,
    required this.icon,
  });

  factory Amenity.fromJson(Map<String, dynamic> json) {
    return Amenity(
      name: json['name'] ?? '',
      icon: json['icon'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'icon': icon,
    };
  }
}

/// Review model
class Review {
  final String id;
  final String userId;
  final String userName;
  final double rating;
  final String comment;
  final DateTime timestamp;

  Review({
    required this.id,
    required this.userId,
    required this.userName,
    required this.rating,
    required this.comment,
    required this.timestamp,
  });

  factory Review.fromJson(Map<String, dynamic> json) {
    return Review(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      userName: json['userName'] ?? '',
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      comment: json['comment'] ?? '',
      timestamp: json['timestamp'] != null
          ? DateTime.parse(json['timestamp'])
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'rating': rating,
      'comment': comment,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}
