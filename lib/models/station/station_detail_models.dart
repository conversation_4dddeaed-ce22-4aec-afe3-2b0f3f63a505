import 'package:google_maps_flutter/google_maps_flutter.dart';

class StationDetail {
  final String uid;
  final String name;
  final String address;
  final LatLng coordinates;
  final String status;
  final List<ConnectorInfo> connectors;
  final List<String> amenities;
  final String operatingHours;
  final List<String>? images; // URLs of station images
  final double? rating; // Average rating
  final int? totalReviews;
  final String? operatorName;
  final LocationDetails? locationDetails;

  StationDetail({
    required this.uid,
    required this.name,
    required this.address,
    required this.coordinates,
    required this.status,
    required this.connectors,
    required this.amenities,
    required this.operatingHours,
    this.images,
    this.rating,
    this.totalReviews,
    this.operatorName,
    this.locationDetails,
  });

  factory StationDetail.fromJson(Map<String, dynamic> json) {
    // CRITICAL: Validate required fields - throw exceptions for missing data
    // This prevents showing default/fallback data when API fails

    if (json['uid'] == null || (json['uid'] as String).isEmpty) {
      throw FormatException(
          'CRITICAL: Station UID is missing from API response');
    }

    if (json['name'] == null || (json['name'] as String).isEmpty) {
      throw FormatException(
          'CRITICAL: Station name is missing from API response');
    }

    if (json['address'] == null || (json['address'] as String).isEmpty) {
      throw FormatException(
          'CRITICAL: Station address is missing from API response');
    }

    // Validate coordinates
    final coordinates = json['coordinates'] as Map<String, dynamic>?;
    if (coordinates == null ||
        coordinates['latitude'] == null ||
        coordinates['longitude'] == null) {
      throw FormatException(
          'CRITICAL: Station coordinates are missing from API response');
    }

    final latitude = (coordinates['latitude'] as num?)?.toDouble();
    final longitude = (coordinates['longitude'] as num?)?.toDouble();

    if (latitude == null ||
        longitude == null ||
        latitude == 0.0 ||
        longitude == 0.0) {
      throw FormatException(
          'CRITICAL: Invalid station coordinates from API response');
    }

    // Validate status
    if (json['status'] == null || (json['status'] as String).isEmpty) {
      throw FormatException(
          'CRITICAL: Station status is missing from API response');
    }

    return StationDetail(
      uid: json['uid'] as String,
      name: json['name'] as String,
      address: json['address'] as String,
      coordinates: LatLng(latitude, longitude),
      status: json['status'] as String,
      connectors: (json['connectors'] as List<dynamic>?)
              ?.map((c) => ConnectorInfo.fromJson(c as Map<String, dynamic>))
              .toList() ??
          [],
      amenities: (json['amenities'] as List<dynamic>?)
              ?.map((a) => a.toString())
              .toList() ??
          [],
      operatingHours: json['operating_hours'] as String? ?? '',
      images: (json['images'] as List<dynamic>?)
          ?.map((img) => img.toString())
          .toList(),
      rating: (json['rating'] as num?)?.toDouble(),
      totalReviews: json['total_reviews'] as int?,
      operatorName: json['operator_name'] as String?,
      locationDetails: json['location_details'] != null
          ? LocationDetails.fromJson(
              json['location_details'] as Map<String, dynamic>)
          : null,
    );
  }
}

class ConnectorInfo {
  final String id;
  final String type;
  final String status;
  final double? powerKw;
  final String? priceText; // e.g., "$0.45/kWh" or "Free"

  // Computed properties - ONLY REAL API DATA
  double get power => powerKw ?? 0.0; // Return 0.0 only when API provides null
  int get voltage =>
      0; // No voltage calculation - only use real API data when available
  int get amperage =>
      0; // No default amperage - only use real API data when available

  ConnectorInfo({
    required this.id,
    required this.type,
    required this.status,
    this.powerKw,
    this.priceText,
  });

  factory ConnectorInfo.fromJson(Map<String, dynamic> json) {
    return ConnectorInfo(
      id: json['id'] as String? ?? '',
      type: json['type'] as String? ?? '',
      status: json['status'] as String? ?? '',
      powerKw: (json['power_kw'] as num?)?.toDouble() ??
          (json['power'] as num?)?.toDouble(),
      priceText: json['price_text'] as String?,
    );
  }
}

class LocationDetails {
  final String? city;
  final String? state;
  final String? postalCode;
  final String? country;

  LocationDetails({
    this.city,
    this.state,
    this.postalCode,
    this.country,
  });

  factory LocationDetails.fromJson(Map<String, dynamic> json) {
    return LocationDetails(
      city: json['city'] as String?,
      state: json['state'] as String?,
      postalCode: json['postal_code'] as String?,
      country: json['country'] as String?,
    );
  }

  @override
  String toString() {
    return [city, state, postalCode, country]
        .where((s) => s != null && s.isNotEmpty)
        .join(', ');
  }
}
