// Marker API response models - markers do not contain UIDs
import 'package:flutter/foundation.dart';

/// Response model for the station markers API
class StationMarkerResponse {
  final List<StationMarkerData> data;
  final String message;
  final String status;

  StationMarkerResponse({
    required this.data,
    required this.message,
    required this.status,
  });

  factory StationMarkerResponse.fromJson(Map<String, dynamic> json) {
    return StationMarkerResponse(
      data: json['data'] != null
          ? List<StationMarkerData>.from(
              json['data'].map((x) => StationMarkerData.fromJson(x)))
          : [],
      message: json['message'] ?? 'No message provided',
      status: json['status'] ?? 'error',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data.map((x) => x.toJson()).toList(),
      'message': message,
      'status': status,
    };
  }

  @override
  String toString() {
    return 'StationMarkerResponse(data: ${data.length} items, message: $message, status: $status)';
  }
}

/// Model for station marker data
class StationMarkerData {
  final String id;
  final String name;
  final double latitude;
  final double longitude;
  final String? address;
  final String status;
  final String? connectorType;
  final String? mapPinUrl;
  final String? focusedMapPinUrl;
  final double? distance;
  // Note: Marker API does not contain UIDs - only coordinates and map pin URLs
  // UIDs should only be obtained from nearest station, search, or paginate APIs

  StationMarkerData({
    required this.id,
    required this.name,
    required this.latitude,
    required this.longitude,
    this.address,
    required this.status,
    this.connectorType,
    this.mapPinUrl,
    this.focusedMapPinUrl,
    this.distance,
  });

  factory StationMarkerData.fromJson(Map<String, dynamic> json) {
    // Marker API does not contain UIDs - only coordinates and map pin URLs
    // UIDs should only be obtained from nearest station, search, or paginate APIs

    // Handle API data format only - no fallback formats
    if (json.containsKey('station_id')) {
      // API format - ONLY use real API data
      return StationMarkerData(
        id: json['station_id']?.toString() ?? '',
        name: json['station_name']?.toString() ??
            'Unknown Station', // Handle null values
        longitude: json['longitude'] != null
            ? (json['longitude'] is num
                ? (json['longitude'] as num).toDouble()
                : double.tryParse(json['longitude'].toString()) ?? 0.0)
            : 0.0,
        latitude: json['latitude'] != null
            ? (json['latitude'] is num
                ? (json['latitude'] as num).toDouble()
                : double.tryParse(json['latitude'].toString()) ?? 0.0)
            : 0.0,
        address: json['address'],
        status: json['status']?.toString() ?? 'Unknown', // Handle null values
        connectorType: json['connector_type'],
        mapPinUrl: json['map_pin_url'],
        focusedMapPinUrl: json['focused_map_pin_url'],
        distance: json['distance'] != null
            ? (json['distance'] is num
                ? (json['distance'] as num).toDouble()
                : double.tryParse(json['distance'].toString()))
            : null,
        // No UID in marker data
      );
    } else {
      // Try alternative field names
      debugPrint('❌ station_id not found, trying alternative field names...');

      // Try different possible field names for ID
      String? id;
      if (json.containsKey('id')) {
        id = json['id']?.toString();
        debugPrint('✅ Found id field: $id');
      } else if (json.containsKey('stationId')) {
        id = json['stationId']?.toString();
        debugPrint('✅ Found stationId field: $id');
      }

      // Try different possible field names for name
      String? name;
      if (json.containsKey('name')) {
        name = json['name']?.toString();
        debugPrint('✅ Found name field: $name');
      } else if (json.containsKey('station_name')) {
        name = json['station_name']?.toString();
        debugPrint('✅ Found station_name field: $name');
      }

      if (id != null && name != null) {
        debugPrint('✅ Using alternative field format');
        return StationMarkerData(
          id: id,
          name: name,
          longitude: json['longitude'] != null
              ? (json['longitude'] is num
                  ? (json['longitude'] as num).toDouble()
                  : double.tryParse(json['longitude'].toString()) ?? 0.0)
              : 0.0,
          latitude: json['latitude'] != null
              ? (json['latitude'] is num
                  ? (json['latitude'] as num).toDouble()
                  : double.tryParse(json['latitude'].toString()) ?? 0.0)
              : 0.0,
          address: json['address']?.toString(),
          status: json['status']?.toString() ?? 'Unknown',
          connectorType: json['connector_type']?.toString(),
          mapPinUrl: json['map_pin_url']?.toString(),
          focusedMapPinUrl: json['focused_map_pin_url']?.toString(),
          distance: json['distance'] != null
              ? (json['distance'] is num
                  ? (json['distance'] as num).toDouble()
                  : double.tryParse(json['distance'].toString()))
              : null,
        );
      } else {
        // Invalid format - throw error with detailed information
        debugPrint(
            '❌ Could not find required fields. Available fields: ${json.keys.join(', ')}');
        throw FormatException(
            'Invalid station marker data format: missing required fields. Available: ${json.keys.join(', ')}');
      }
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'longitude': longitude,
      'latitude': latitude,
      'address': address,
      'status': status,
      'connectorType': connectorType,
      'mapPinUrl': mapPinUrl,
      'focusedMapPinUrl': focusedMapPinUrl,
      'distance': distance,
      // No UID in marker data
    };
  }

  @override
  String toString() {
    return 'StationMarkerData(id: $id, name: $name, longitude: $longitude, latitude: $latitude, status: $status)';
  }
}
