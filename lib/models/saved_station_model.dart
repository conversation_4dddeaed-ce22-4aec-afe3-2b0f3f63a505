import 'dart:convert';
import 'package:flutter/foundation.dart';

/// Model for saved/bookmarked station response from API
class SavedStationsResponse {
  final String message;
  final bool success;
  final List<SavedStation> data;

  SavedStationsResponse({
    required this.message,
    required this.success,
    required this.data,
  });

  factory SavedStationsResponse.fromJson(Map<String, dynamic> json) {
    debugPrint('🔖 Parsing SavedStationsResponse from JSON: $json');

    return SavedStationsResponse(
      message: json['message'] ?? 'Unknown response',
      success: json['success'] ?? false,
      data: json['data'] != null
          ? (json['data'] as List)
              .map((item) => SavedStation.fromJson(item))
              .toList()
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'success': success,
      'data': data.map((station) => station.toJson()).toList(),
    };
  }
}

/// Model for a saved/bookmarked station
class SavedStation {
  final double longitude;
  final double latitude;
  final int stationId;
  final String uid;
  final String name;
  final String address;
  final String city;
  final List<ConnectorType> types;
  final String status;

  SavedStation({
    required this.longitude,
    required this.latitude,
    required this.stationId,
    required this.uid,
    required this.name,
    required this.address,
    required this.city,
    required this.types,
    required this.status,
  });

  factory SavedStation.fromJson(Map<String, dynamic> json) {
    debugPrint('🔖 Parsing SavedStation from JSON: $json');

    // Handle flexible types field - can be List or Map
    List<ConnectorType> connectorTypes = [];
    if (json['types'] != null) {
      if (json['types'] is List) {
        // Types as array
        connectorTypes = (json['types'] as List)
            .map((type) => ConnectorType.fromJson(type))
            .toList();
      } else if (json['types'] is Map) {
        // Types as object with numeric keys
        final typesMap = json['types'] as Map<String, dynamic>;
        connectorTypes = typesMap.values
            .map((type) => ConnectorType.fromJson(type))
            .toList();
      }
    }

    return SavedStation(
      longitude: (json['longitude'] as num?)?.toDouble() ?? 0.0,
      latitude: (json['latitude'] as num?)?.toDouble() ?? 0.0,
      stationId: json['station_id'] ?? 0,
      uid: json['uid'] ?? '',
      name: json['name'] ?? '',
      address: json['address'] ?? '',
      city: json['city'] ?? '',
      types: connectorTypes,
      status: json['status'] ?? 'Unknown',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'longitude': longitude,
      'latitude': latitude,
      'station_id': stationId,
      'uid': uid,
      'name': name,
      'address': address,
      'city': city,
      'types': types.map((type) => type.toJson()).toList(),
      'status': status,
    };
  }

  String toJsonString() {
    return jsonEncode(toJson());
  }
}

/// Model for connector type in saved stations
class ConnectorType {
  final String name;
  final String icon;

  ConnectorType({
    required this.name,
    required this.icon,
  });

  factory ConnectorType.fromJson(Map<String, dynamic> json) {
    return ConnectorType(
      name: json['name'] ?? '',
      icon: json['icon'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'icon': icon,
    };
  }
}
