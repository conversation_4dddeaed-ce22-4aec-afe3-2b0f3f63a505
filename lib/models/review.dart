class Review {
  final String id;
  final String stationId;
  final String userId;
  final String userName;
  final double rating;
  final String comment;
  final String createdAt;
  final String? userAvatar;

  Review({
    required this.id,
    required this.stationId,
    required this.userId,
    required this.userName,
    required this.rating,
    required this.comment,
    required this.createdAt,
    this.userAvatar,
  });

  factory Review.fromJson(Map<String, dynamic> json) {
    return Review(
      id: json['id'].toString(),
      stationId: json['station_id'].toString(),
      userId: json['user_id'].toString(),
      userName: json['user_name'] ?? '',
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      comment: json['comment'] ?? '',
      createdAt: json['created_at'] ?? DateTime.now().toIso8601String(),
      userAvatar: json['user_avatar'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'station_id': stationId,
      'user_id': userId,
      'user_name': userName,
      'rating': rating,
      'comment': comment,
      'created_at': createdAt,
      'user_avatar': userAvatar,
    };
  }
}
