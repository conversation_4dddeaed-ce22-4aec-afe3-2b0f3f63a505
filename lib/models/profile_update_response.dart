/// Model class for profile update response
class ProfileUpdateResponse {
  final bool success;
  final String message;
  final Map<String, dynamic>? data;

  ProfileUpdateResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory ProfileUpdateResponse.fromJson(Map<String, dynamic> json) {
    return ProfileUpdateResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? 'Unknown response',
      data: json['data'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> result = {
      'success': success,
      'message': message,
    };
    
    if (data != null) {
      result['data'] = data;
    }
    
    return result;
  }
}
