 import 'package:flutter/foundation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

/// Model for route information from Google Directions API
class RouteModel {
  final List<LatLng> polylinePoints;
  final String distance;
  final String duration;
  final String summary;
  final LatLngBounds bounds;
  final List<RouteStep> steps;

  const RouteModel({
    required this.polylinePoints,
    required this.distance,
    required this.duration,
    required this.summary,
    required this.bounds,
    required this.steps,
  });

  /// Create RouteModel from Google Directions API response
  factory RouteModel.fromDirectionsResponse(Map<String, dynamic> response) {
    final routes = response['routes'] as List;
    if (routes.isEmpty) {
      throw Exception('No routes found');
    }

    final route = routes.first as Map<String, dynamic>;
    return RouteModel.fromSingleRoute(route);
  }

  /// Create RouteModel from a single route data
  factory RouteModel.fromSingleRoute(Map<String, dynamic> route) {
    final legs = route['legs'] as List;
    final leg = legs.first as Map<String, dynamic>;

    // Extract polyline points
    final overviewPolyline = route['overview_polyline'] as Map<String, dynamic>;
    final encodedPolyline = overviewPolyline['points'] as String;
    final polylinePoints = _decodePolyline(encodedPolyline);

    // Check if all points are (0.0, 0.0)
    if (polylinePoints.isNotEmpty && polylinePoints.every((p) => p.latitude == 0.0 && p.longitude == 0.0)) {
      debugPrint('⚠️ DECODING WARNING: All decoded polyline points are (0.0, 0.0). This might indicate a problem with the encoded string or the decoding process. Encoded string (start): ${encodedPolyline.substring(0, encodedPolyline.length > 100 ? 100 : encodedPolyline.length)}...');
      // Optionally, throw an error or return a model that indicates this specific issue
      // For now, just logging. Depending on requirements, could throw:
      // throw Exception('All decoded polyline points are (0.0, 0.0)');
    }

    // Extract distance and duration
    final distance = leg['distance']['text'] as String;
    final duration = leg['duration']['text'] as String;

    // Extract summary
    final summary = route['summary'] as String? ?? 'Route';

    // Calculate bounds
    final bounds = _calculateBounds(polylinePoints);

    // Extract steps
    final steps = (leg['steps'] as List)
        .map((step) => RouteStep.fromJson(step as Map<String, dynamic>))
        .toList();

    return RouteModel(
      polylinePoints: polylinePoints,
      distance: distance,
      duration: duration,
      summary: summary,
      bounds: bounds,
      steps: steps,
    );
  }

  /// Decode Google polyline encoding
  static List<LatLng> _decodePolyline(String encoded) {
    debugPrint('🔗 POLYLINE DECODE: Starting decode of ${encoded.length} character string');

    if (encoded.isEmpty) {
      debugPrint('❌ POLYLINE DECODE: Empty encoded string');
      return [];
    }

    List<LatLng> points = [];
    int index = 0;
    int len = encoded.length;
    int lat = 0;
    int lng = 0;

    try {
      while (index < len) {
        int b;
        int shift = 0;
        int result = 0;

        // Decode latitude
        do {
          if (index >= len) {
            debugPrint('❌ POLYLINE DECODE: Unexpected end of string while decoding latitude');
            break;
          }
          b = encoded.codeUnitAt(index++) - 63;
          result |= (b & 0x1f) << shift;
          shift += 5;
        } while (b >= 0x20);
        int dlat = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
        lat += dlat;

        shift = 0;
        result = 0;

        // Decode longitude
        do {
          if (index >= len) {
            debugPrint('❌ POLYLINE DECODE: Unexpected end of string while decoding longitude');
            break;
          }
          b = encoded.codeUnitAt(index++) - 63;
          result |= (b & 0x1f) << shift;
          shift += 5;
        } while (b >= 0x20);
        int dlng = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
        lng += dlng;

        final decodedLat = lat / 1E5;
        final decodedLng = lng / 1E5;

        // Validate coordinates
        if (decodedLat >= -90 && decodedLat <= 90 && decodedLng >= -180 && decodedLng <= 180) {
          points.add(LatLng(decodedLat, decodedLng));
        } else {
          debugPrint('⚠️ POLYLINE DECODE: Invalid coordinate skipped: ($decodedLat, $decodedLng)');
        }
      }
    } catch (e) {
      debugPrint('❌ POLYLINE DECODE: Error during decoding: $e');
      return [];
    }

    debugPrint('✅ POLYLINE DECODE: Successfully decoded ${points.length} points');
    if (points.isNotEmpty) {
      debugPrint('🔗 POLYLINE DECODE: First point: ${points.first}');
      debugPrint('🔗 POLYLINE DECODE: Last point: ${points.last}');

      // Validate coordinate ranges
      final latitudes = points.map((p) => p.latitude);
      final longitudes = points.map((p) => p.longitude);
      final minLat = latitudes.reduce((a, b) => a < b ? a : b);
      final maxLat = latitudes.reduce((a, b) => a > b ? a : b);
      final minLng = longitudes.reduce((a, b) => a < b ? a : b);
      final maxLng = longitudes.reduce((a, b) => a > b ? a : b);

      debugPrint('🔗 POLYLINE DECODE: Coordinate ranges - Lat: $minLat to $maxLat, Lng: $minLng to $maxLng');
    }

    return points;
  }

  /// Calculate bounds for a list of points
  static LatLngBounds _calculateBounds(List<LatLng> points) {
    if (points.isEmpty) {
      return LatLngBounds(
        southwest: const LatLng(0, 0),
        northeast: const LatLng(0, 0),
      );
    }

    double minLat = points.first.latitude;
    double maxLat = points.first.latitude;
    double minLng = points.first.longitude;
    double maxLng = points.first.longitude;

    for (final point in points) {
      minLat = minLat < point.latitude ? minLat : point.latitude;
      maxLat = maxLat > point.latitude ? maxLat : point.latitude;
      minLng = minLng < point.longitude ? minLng : point.longitude;
      maxLng = maxLng > point.longitude ? maxLng : point.longitude;
    }

    return LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );
  }

  @override
  String toString() {
    return 'RouteModel(distance: $distance, duration: $duration, points: ${polylinePoints.length})';
  }
}

/// Model for individual route step
class RouteStep {
  final String instruction;
  final String distance;
  final String duration;
  final LatLng startLocation;
  final LatLng endLocation;

  const RouteStep({
    required this.instruction,
    required this.distance,
    required this.duration,
    required this.startLocation,
    required this.endLocation,
  });

  factory RouteStep.fromJson(Map<String, dynamic> json) {
    final startLoc = json['start_location'] as Map<String, dynamic>;
    final endLoc = json['end_location'] as Map<String, dynamic>;

    return RouteStep(
      instruction: json['html_instructions'] as String? ?? '',
      distance: json['distance']['text'] as String? ?? '',
      duration: json['duration']['text'] as String? ?? '',
      startLocation: LatLng(
        startLoc['lat'] as double,
        startLoc['lng'] as double,
      ),
      endLocation: LatLng(
        endLoc['lat'] as double,
        endLoc['lng'] as double,
      ),
    );
  }
}
