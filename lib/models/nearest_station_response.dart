import 'package:flutter/foundation.dart' show debugPrint;

class NearestStationResponse {
  final String? message;
  final bool? success;
  final List<NearestStation>? data;

  NearestStationResponse({
    this.message,
    this.success,
    this.data,
  });

  factory NearestStationResponse.fromJson(Map<String, dynamic> json) {
    return NearestStationResponse(
      message: json['message'],
      success: json['success'],
      data: json['data'] != null
          ? List<NearestStation>.from(
              json['data'].map((x) => NearestStation.fromJson(x)))
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'success': success,
      'data': data?.map((x) => x.toJson()).toList(),
    };
  }
}

class NearestStation {
  final double? longitude;
  final double? latitude;
  final int? stationId;
  final String uid; // UID (can be empty if not available from API)
  final String? name;
  final String? address;
  final String? city;
  final double? distance;
  final dynamic types; // Can be either Map or List
  final String? status;
  final String? mapPinUrl;
  final String? focusedMapPinUrl;

  NearestStation({
    this.longitude,
    this.latitude,
    this.stationId,
    required this.uid, // Required parameter
    this.name,
    this.address,
    this.city,
    this.distance,
    this.types,
    this.status,
    this.mapPinUrl,
    this.focusedMapPinUrl,
  });

  factory NearestStation.fromJson(Map<String, dynamic> json) {
    // Log the raw JSON data to understand the structure
    debugPrint('NearestStation.fromJson raw data: ${json.keys.join(', ')}');

    // Extract UID directly from the 'uid' field in the API response
    // Based on the provided JSON structure, 'uid' is the correct field
    String? uidValue = json['uid']?.toString();

    // Handle UID status
    if (uidValue == null || uidValue.isEmpty) {
      // Set UID to empty string instead of throwing exception
      // This allows the station to be displayed but prevents navigation to details
      uidValue = '';
    }

    return NearestStation(
      longitude: json['longitude'] is String
          ? double.tryParse(json['longitude'])
          : json['longitude']?.toDouble(),
      latitude: json['latitude'] is String
          ? double.tryParse(json['latitude'])
          : json['latitude']?.toDouble(),
      stationId: json['station_id'],
      uid: uidValue, // UID (can be empty if not available)
      name: json['name'],
      address: json['address'],
      city: json['city'],
      distance: json['distance']?.toDouble(),
      types: json['types'], // Store as dynamic
      status: json['status'],
      mapPinUrl: json['map_pin_url'],
      focusedMapPinUrl: json['focused_map_pin_url'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'longitude': longitude,
      'latitude': latitude,
      'station_id': stationId,
      'uid': uid,
      'name': name,
      'address': address,
      'city': city,
      'distance': distance,
      'types': types,
      'status': status,
      'map_pin_url': mapPinUrl,
      'focused_map_pin_url': focusedMapPinUrl,
    };
  }

  // Helper method to get connector types as a list
  List<ConnectorType> getConnectorTypes() {
    List<ConnectorType> result = [];

    try {
      if (types == null) {
        debugPrint('Station $name: types field is null');
        return result;
      }

      debugPrint('Station $name: types field is of type ${types.runtimeType}');

      // Handle different types of 'types' field
      if (types is Map<String, dynamic>) {
        // Handle map format (e.g., {"0": {"name": "CCS2", "icon": "url"}, ...})
        debugPrint(
            'Station $name: Processing types as Map<String, dynamic> with keys: ${(types as Map).keys.join(', ')}');

        (types as Map<String, dynamic>).forEach((key, value) {
          if (value is Map) {
            try {
              // Convert to Map<String, dynamic> for type safety
              final Map<String, dynamic> typeData =
                  Map<String, dynamic>.from(value);
              result.add(ConnectorType.fromJson(typeData));
            } catch (e) {
              debugPrint('Error parsing connector type from map value: $e');
            }
          }
        });
      } else if (types is Map) {
        // Handle generic Map format
        debugPrint(
            'Station $name: Processing types as generic Map with keys: ${(types as Map).keys.join(', ')}');

        (types as Map).forEach((key, value) {
          if (value is Map) {
            try {
              // Convert to Map<String, dynamic> for type safety
              final Map<String, dynamic> typeData = {};
              value.forEach((k, v) {
                typeData[k.toString()] = v;
              });
              result.add(ConnectorType.fromJson(typeData));
            } catch (e) {
              debugPrint(
                  'Error parsing connector type from generic map value: $e');
            }
          }
        });
      } else if (types is List<Map<String, dynamic>>) {
        // Handle list of maps format with specific type
        debugPrint(
            'Station $name: Processing types as List<Map<String, dynamic>> with ${(types as List).length} items');

        for (var item in types as List<Map<String, dynamic>>) {
          try {
            result.add(ConnectorType.fromJson(item));
          } catch (e) {
            debugPrint('Error parsing connector type from typed list item: $e');
          }
        }
      } else if (types is List) {
        // Handle generic list format
        debugPrint(
            'Station $name: Processing types as generic List with ${(types as List).length} items');

        for (var item in types as List) {
          try {
            if (item is Map) {
              // Convert to Map<String, dynamic> for type safety
              final Map<String, dynamic> typeData = {};
              item.forEach((k, v) {
                typeData[k.toString()] = v;
              });
              result.add(ConnectorType.fromJson(typeData));
            }
          } catch (e) {
            debugPrint(
                'Error parsing connector type from generic list item: $e');
          }
        }
      } else {
        // Handle unexpected type
        debugPrint(
            'Station $name: Unexpected types field type: ${types.runtimeType}');
      }
    } catch (e, stackTrace) {
      debugPrint('❌ CRITICAL ERROR in getConnectorTypes for station $name: $e');
      debugPrint('   Stack trace: $stackTrace');
      debugPrint('   Raw types value: $types');
      debugPrint('   Types runtime type: ${types.runtimeType}');
      // Return empty list to prevent app crash
      return result;
    }

    debugPrint('Station $name: Extracted ${result.length} connector types');
    return result;
  }

  // Helper method to get connector types as a string
  String getConnectorTypesString() {
    final connectorTypes = getConnectorTypes();
    if (connectorTypes.isEmpty) return 'Connector types not specified';

    final List<String> typeNames = [];
    for (var type in connectorTypes) {
      if (type.name != null && type.name!.isNotEmpty) {
        typeNames.add(type.name!);
      }
    }

    return typeNames.isEmpty
        ? 'Connector types not specified'
        : typeNames.join(', ');
  }
}

class ConnectorType {
  final String? name;
  final String? icon;
  final String? power; // Add power field
  final dynamic maxElectricPower; // PRESERVE EXACT API FORMAT
  final int? guns; // Add guns field
  final int? availableGuns; // Add available guns field

  ConnectorType({
    this.name,
    this.icon,
    this.power,
    this.maxElectricPower,
    this.guns,
    this.availableGuns,
  });

  factory ConnectorType.fromJson(Map<String, dynamic> json) {
    // Extract maxElectricPower preserving exact API format
    dynamic maxElectricPower;
    if (json['maxElectricPower'] != null) {
      maxElectricPower = json['maxElectricPower']; // PRESERVE EXACT API FORMAT
      debugPrint(
          '✅ Preserved maxElectricPower from API: $maxElectricPower (${maxElectricPower.runtimeType})');
    } else if (json['max_electric_power'] != null) {
      maxElectricPower =
          json['max_electric_power']; // PRESERVE EXACT API FORMAT
      debugPrint(
          '✅ Preserved max_electric_power from API: $maxElectricPower (${maxElectricPower.runtimeType})');
    }

    return ConnectorType(
      name: json['name'],
      icon: json['icon'],
      power: json['power']?.toString(),
      maxElectricPower: maxElectricPower,
      guns: json['guns'] is num ? (json['guns'] as num).toInt() : null,
      availableGuns: json['availableGuns'] is num
          ? (json['availableGuns'] as num).toInt()
          : json['available_guns'] is num
              ? (json['available_guns'] as num).toInt()
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'icon': icon,
      'power': power,
      'maxElectricPower': maxElectricPower,
      'guns': guns,
      'availableGuns': availableGuns,
    };
  }
}
