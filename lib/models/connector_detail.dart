class ConnectorDetail {
  final String id;
  final String name;
  final String type;
  final String power;
  final double price;
  final int totalGuns;
  final int availableGuns;
  final String status;
  final String? icon;

  ConnectorDetail({
    required this.id,
    required this.name,
    required this.type,
    required this.power,
    required this.price,
    required this.totalGuns,
    required this.availableGuns,
    required this.status,
    this.icon,
  });

  factory ConnectorDetail.fromJson(Map<String, dynamic> json) {
    // CRITICAL: Validate essential connector data
    if (json['id'] == null || json['id'].toString().isEmpty) {
      throw FormatException('Connector ID is missing from API response');
    }
    if (json['connector_type'] == null || json['connector_type'].toString().isEmpty) {
      throw FormatException('Connector type is missing from API response');
    }

    return ConnectorDetail(
      id: json['id'].toString(),
      name: json['name']?.toString() ?? '',
      type: json['connector_type'].toString(),
      power: json['power_output']?.toString() ?? '',
      price: (json['price_per_kwh'] as num?)?.toDouble() ?? 0.0,
      totalGuns: json['total_guns'] ?? 0,
      availableGuns: json['available_guns'] ?? 0,
      // CRITICAL: No default status - throw error if missing
      status: json['status']?.toString() ??
              (throw FormatException('Connector status is missing from API response')),
      icon: json['connector_image']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'connector_type': type,
      'power_output': power,
      'price_per_kwh': price,
      'total_guns': totalGuns,
      'available_guns': availableGuns,
      'status': status,
      'connector_image': icon,
    };
  }
}
