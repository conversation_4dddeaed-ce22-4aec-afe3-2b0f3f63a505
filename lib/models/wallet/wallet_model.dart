/// Wallet model
class WalletModel {
  final double balance;
  final int rewardPoints;
  final DateTime lastUpdated;
  final String currency;
  
  WalletModel({
    required this.balance,
    required this.rewardPoints,
    required this.lastUpdated,
    required this.currency,
  });
  
  factory WalletModel.fromJson(Map<String, dynamic> json) {
    return WalletModel(
      balance: (json['balance'] as num?)?.toDouble() ?? 0.0,
      rewardPoints: json['rewardPoints'] ?? 0,
      lastUpdated: json['lastUpdated'] != null 
          ? DateTime.parse(json['lastUpdated']) 
          : DateTime.now(),
      currency: json['currency'] ?? 'INR',
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'balance': balance,
      'rewardPoints': rewardPoints,
      'lastUpdated': lastUpdated.toIso8601String(),
      'currency': currency,
    };
  }
}
