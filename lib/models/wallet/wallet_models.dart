/// Wallet information model
class WalletInfo {
  final double balance;
  final int rewardPoints;
  final List<Transaction> recentTransactions;

  WalletInfo({
    required this.balance,
    required this.rewardPoints,
    required this.recentTransactions,
  });

  factory WalletInfo.fromJson(Map<String, dynamic> json) {
    return WalletInfo(
      balance: (json['balance'] as num?)?.toDouble() ?? 0.0,
      rewardPoints: json['rewardPoints'] ?? 0,
      recentTransactions: (json['recentTransactions'] as List<dynamic>?)
              ?.map((e) => Transaction.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'balance': balance,
      'rewardPoints': rewardPoints,
      'recentTransactions': recentTransactions.map((e) => e.toJson()).toList(),
    };
  }
}

/// Transaction model
class Transaction {
  final String id;
  final String title;
  final String description;
  final double amount;
  final DateTime timestamp;
  final String type; // 'credit', 'debit'
  final String status; // 'completed', 'pending', 'failed'
  final String? transactionReference;

  Transaction({
    required this.id,
    required this.title,
    required this.description,
    required this.amount,
    required this.timestamp,
    required this.type,
    required this.status,
    this.transactionReference,
  });

  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id']?.toString() ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      amount: (json['amount'] is int)
          ? (json['amount'] as int).toDouble()
          : ((json['amount'] as num?)?.toDouble() ?? 0.0),
      timestamp: json['timestamp'] != null
          ? DateTime.parse(json['timestamp'])
          : DateTime.now(),
      type: json['type'] ?? 'debit',
      status: json['status'] ?? 'completed',
      transactionReference: json['transactionReference'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'amount': amount,
      'timestamp': timestamp.toIso8601String(),
      'type': type,
      'status': status,
      'transactionReference': transactionReference,
    };
  }

  // Helper method to determine if this is a credit transaction
  bool get isCredit => type == 'credit';

  // Helper method to determine if this is a completed transaction
  bool get isCompleted => status == 'completed';

  // Get formatted amount with sign
  String getFormattedAmount() {
    if (isCredit) {
      return '+₹${amount.abs().toStringAsFixed(2)}';
    } else {
      return '-₹${amount.abs().toStringAsFixed(2)}';
    }
  }

  // Get color based on transaction type and status
  int getColorValue() {
    if (status == 'pending') {
      return 0xFFFF9800; // Orange for pending
    } else if (status == 'failed' || status == 'rejected') {
      return 0xFFE53935; // Red for failed/rejected
    }
    return isCredit
        ? 0xFF8cc051
        : 0xFFE53935; // Lime green for credit, Red for debit
  }
}

/// Billing details model
class BillingDetails {
  final String id;
  final double totalAmount;
  final double energyConsumed;
  final double rate;
  final DateTime startTime;
  final DateTime endTime;
  final String stationName;
  final String connectorType;

  BillingDetails({
    required this.id,
    required this.totalAmount,
    required this.energyConsumed,
    required this.rate,
    required this.startTime,
    required this.endTime,
    required this.stationName,
    required this.connectorType,
  });

  factory BillingDetails.fromJson(Map<String, dynamic> json) {
    return BillingDetails(
      id: json['id'] ?? '',
      totalAmount: (json['totalAmount'] as num?)?.toDouble() ?? 0.0,
      energyConsumed: (json['energyConsumed'] as num?)?.toDouble() ?? 0.0,
      rate: (json['rate'] as num?)?.toDouble() ?? 0.0,
      startTime: json['startTime'] != null
          ? DateTime.parse(json['startTime'])
          : DateTime.now(),
      endTime: json['endTime'] != null
          ? DateTime.parse(json['endTime'])
          : DateTime.now(),
      stationName: json['stationName'] ?? '',
      connectorType: json['connectorType'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'totalAmount': totalAmount,
      'energyConsumed': energyConsumed,
      'rate': rate,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime.toIso8601String(),
      'stationName': stationName,
      'connectorType': connectorType,
    };
  }
}

/// Charging session model
class ChargingSession {
  final String id;
  final String stationName;
  final String stationAddress;
  final String connectorType;
  final double energyConsumed;
  final double amount;
  final DateTime startTime;
  final DateTime? endTime;
  final String status; // 'ongoing', 'completed', 'failed'

  ChargingSession({
    required this.id,
    required this.stationName,
    required this.stationAddress,
    required this.connectorType,
    required this.energyConsumed,
    required this.amount,
    required this.startTime,
    this.endTime,
    required this.status,
  });

  factory ChargingSession.fromJson(Map<String, dynamic> json) {
    return ChargingSession(
      id: json['id'] ?? '',
      stationName: json['stationName'] ?? '',
      stationAddress: json['stationAddress'] ?? '',
      connectorType: json['connectorType'] ?? '',
      energyConsumed: (json['energyConsumed'] as num?)?.toDouble() ?? 0.0,
      amount: (json['amount'] as num?)?.toDouble() ?? 0.0,
      startTime: json['startTime'] != null
          ? DateTime.parse(json['startTime'])
          : DateTime.now(),
      endTime: json['endTime'] != null ? DateTime.parse(json['endTime']) : null,
      status: json['status'] ?? 'completed',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'stationName': stationName,
      'stationAddress': stationAddress,
      'connectorType': connectorType,
      'energyConsumed': energyConsumed,
      'amount': amount,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'status': status,
    };
  }
}
