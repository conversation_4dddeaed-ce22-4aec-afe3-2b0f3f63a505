import 'package:flutter/foundation.dart' as foundation;
import 'nearest_station_response.dart';
import 'station/station_details_response.dart' as station_details_response;
import 'station.dart' as app_model;

/// Utility class to convert between different station data formats
class StationConverter {
  /// Format the connector availability status for display
  static String formatConnectorStatus(String? status) {
    if (status == null || status.isEmpty) {
      debugPrint('Connector status is null or empty, returning empty string');
      return '';
    }

    // Convert to lowercase for case-insensitive comparison
    final String statusLower = status.toLowerCase().trim();
    debugPrint('Normalizing connector status: "$status" -> "$statusLower"');

    // Check for availability - more inclusive matching
    if (statusLower == 'available' ||
        statusLower.contains('available') ||
        statusLower.contains('online') ||
        statusLower.contains('operational') ||
        statusLower.contains('ready') ||
        statusLower.contains('active')) {
      debugPrint('Connector status "$status" interpreted as "Available"');
      return 'Available';
    }

    // Check for charging/in-use status
    if (statusLower.contains('charging') ||
        statusLower.contains('in use') ||
        statusLower.contains('inuse') ||
        statusLower.contains('busy') ||
        statusLower.contains('occupied')) {
      debugPrint('Connector status "$status" interpreted as "In Use"');
      return 'In Use';
    }

    // Check for fault/error status
    if (statusLower.contains('fault') ||
        statusLower.contains('error') ||
        statusLower.contains('broken') ||
        statusLower.contains('maintenance')) {
      debugPrint('Connector status "$status" interpreted as "Fault"');
      return 'Fault';
    }

    // Check for unavailable/offline status
    if (statusLower.contains('unavailable') ||
        statusLower.contains('not available') ||
        statusLower.contains('offline') ||
        statusLower.contains('disabled') ||
        statusLower.contains('closed')) {
      debugPrint('Connector status "$status" interpreted as "Unavailable"');
      return 'Unavailable';
    }

    // Check for reserved status
    if (statusLower.contains('reserved') || statusLower.contains('booked')) {
      debugPrint('Connector status "$status" interpreted as "Reserved"');
      return 'Reserved';
    }

    // Check for gun connected status
    if (statusLower.contains('gun connected') ||
        statusLower.contains('gun_connected')) {
      debugPrint('Connector status "$status" interpreted as "Gun Connected"');
      return 'Gun Connected';
    }

    // Log unrecognized status for debugging
    debugPrint('Unrecognized connector status: "$status", returning as-is');

    // Default fallback - return the original status
    return status;
  }

  /// Format the full address from station details
  static String formatFullAddress(
      station_details_response.StationDetailsData data) {
    List<String> addressParts = [];

    // Note: StationDetailsData fields are nullable, ensure checks are appropriate
    if (data.address != null && data.address!.isNotEmpty) {
      addressParts.add(data.address!);
    }

    if (data.city != null && data.city!.isNotEmpty) {
      addressParts.add(data.city!);
    }

    if (data.state != null && data.state!.isNotEmpty) {
      addressParts.add(data.state!);
    }

    if (addressParts.isEmpty) {
      throw FormatException(
          'StationDetailsData address, city, and state are all null or empty');
    }

    return addressParts.join(', ');
  }

  /// Convert a NearestStation to a Station object
  static app_model.Station fromNearestStation(NearestStation nearestStation) {
    if (nearestStation.stationId == null) {
      throw FormatException('NearestStation stationId is null');
    }
    if (nearestStation.name == null) {
      throw FormatException('NearestStation name is null');
    }
    if (nearestStation.latitude == null) {
      throw FormatException('NearestStation latitude is null');
    }
    if (nearestStation.longitude == null) {
      throw FormatException('NearestStation longitude is null');
    }
    if (nearestStation.distance == null) {
      throw FormatException('NearestStation distance is null');
    }
    if (nearestStation.status == null) {
      throw FormatException('NearestStation status is null');
    }

    final String stationId = nearestStation.stationId.toString();
    final String stationName = nearestStation.name!;
    final String stationAddress =
        _formatAddress(nearestStation.address, nearestStation.city);
    final double stationLatitude = nearestStation.latitude!;
    final double stationLongitude = nearestStation.longitude!;
    final double stationDistance = nearestStation.distance!;
    final String stationStatus = nearestStation.status!;

    // Extract UID directly from the API response
    final String uid = nearestStation.uid;

    // Log the UID for debugging
    if (uid.isNotEmpty) {
      debugPrint('NearestStation has UID: $uid');
    } else {
      debugPrint('WARNING: NearestStation has no UID, stationId: $stationId');
    }

    // NearestStation doesn't provide these values - use 0 only for model compatibility
    final double stationRating =
        0.0; // Model requires non-null, use 0.0 when API provides no data
    final int stationReviews =
        0; // Model requires non-null, use 0 when API provides no data
    final List<app_model.Connector> stationConnectors = [];
    final List<String> stationImages = [];

    return app_model.Station(
      id: stationId,
      name: stationName,
      address: stationAddress,
      city: nearestStation.city,
      state: null,
      latitude: stationLatitude,
      longitude: stationLongitude,
      distance: stationDistance,
      status: stationStatus,
      rating: stationRating,
      reviews: stationReviews,
      connectors: stationConnectors,
      images: stationImages,
      evses: [],
      uid: uid, // UID is now non-nullable
      openingTimes: null,
      mapPinUrl: nearestStation.mapPinUrl,
      focusedMapPinUrl: nearestStation.focusedMapPinUrl,
      types: null,
    );
  }

  /// Format address by combining address and city
  static String _formatAddress(String? address, String? city) {
    // This helper is used to produce app_model.Station.address, which is non-nullable.
    // Therefore, it must return a non-empty string or throw.
    final String addr = address?.trim() ?? '';
    final String cty = city?.trim() ?? '';

    if (addr.isEmpty && cty.isEmpty) {
      throw FormatException(
          'Address and City are both null or empty in NearestStation');
    }
    if (addr.isEmpty) return cty;
    if (cty.isEmpty) return addr;

    // Check if the address already contains the city name (case-insensitive)
    if (addr.toLowerCase().contains(cty.toLowerCase())) {
      return addr;
    }

    return '$addr, $cty';
  }

  /// Use Flutter's debugPrint for logging
  static void debugPrint(String message) {
    // Forward to Flutter's debugPrint
    // This allows the StationConverter to log messages without direct Flutter imports
    foundation.debugPrint(message);
  }

  /// Convert a map from the station markers API to a Station object
  static app_model.Station fromStationMarker(Map<String, dynamic> marker) {
    // Helper to safely extract and validate string fields
    String getValidatedString(String key, String fieldName) {
      final value = marker[key];
      if (value == null || value is! String || value.trim().isEmpty) {
        throw FormatException(
            'StationMarker $fieldName (key: $key) is missing, not a string, or empty');
      }
      return value.trim();
    }

    // Helper to safely extract and validate double fields
    double getValidatedDouble(String key, String fieldName) {
      final value = marker[key];
      if (value == null || value is! num) {
        throw FormatException(
            'StationMarker $fieldName (key: $key) is missing or not a number');
      }
      return value.toDouble();
    }

    // Removed unused helper function getValidatedInt

    final String stationId = getValidatedString('id', 'ID');
    final String stationName = marker['name']?.toString() ??
        marker['station_name']?.toString() ??
        (throw FormatException(
            'StationMarker name or station_name is missing'));
    if (stationName.isEmpty) {
      throw FormatException('StationMarker name is empty');
    }

    final String stationAddress = marker['address']?.toString() ??
        marker['station_address']?.toString() ??
        (throw FormatException(
            'StationMarker address or station_address is missing'));
    if (stationAddress.isEmpty) {
      throw FormatException('StationMarker address is empty');
    }

    final double stationLatitude = getValidatedDouble('latitude', 'Latitude');
    final double stationLongitude =
        getValidatedDouble('longitude', 'Longitude');
    final double stationDistance = marker['distance'] is num
        ? (marker['distance'] as num).toDouble()
        : 0.0; // Distance can be optional for a marker

    final String stationStatus = marker['status']?.toString() ??
        marker['availability']?.toString() ??
        (throw FormatException(
            'StationMarker status or availability is missing'));
    if (stationStatus.isEmpty) {
      throw FormatException('StationMarker status is empty');
    }

    // Marker API does not provide UIDs - always set to empty string
    final String stationUid = ''; // Markers never contain UIDs

    // Extract rating value with fallbacks
    final dynamic ratingValue = marker['rate'] ?? marker['rating'];
    final double stationRating =
        ratingValue is num ? ratingValue.toDouble() : 0.0;
    final int stationReviews =
        (marker['rate_total'] ?? marker['reviews']) is num
            ? ((marker['rate_total'] ?? marker['reviews']) as num)
                .round() // BULLETPROOF: Use round() instead of toInt()
            : 0;

    // Process connectors if available
    List<app_model.Connector> stationConnectors = [];
    if (marker.containsKey('connectors') && marker['connectors'] is List) {
      List<dynamic> rawConnectors = marker['connectors'];
      for (var connData in rawConnectors) {
        if (connData is Map<String, dynamic>) {
          // Validate connector fields
          // IMPROVED: Generate fallback ID if missing
          final String connId = connData['id']?.toString() ??
              'connector_${DateTime.now().millisecondsSinceEpoch}';

          // IMPROVED: Use fallback name/type if missing
          final String connName = connData['name']?.toString() ??
              connData['type']?.toString() ??
              'Standard';

          if (connName.isEmpty) {
            debugPrint('Empty connector name, using fallback: Standard');
          }

          // IMPROVED: Use fallback type if missing
          final String connType = connData['type']?.toString() ??
              connData['connectorType']?.toString() ??
              connName; // Use name as type fallback

          if (connType.isEmpty) {
            debugPrint('Empty connector type, using fallback: Standard');
          }

          // IMPROVED: Use fallback power if missing
          final String connPower = connData['power']?.toString() ?? '';
          if (connPower.isEmpty) {
            debugPrint('No power data available for connector');
          }

          final String connIcon = connData['icon']?.toString() ??
              connData['imageUrl']?.toString() ??
              connData['iconUrl']?.toString() ??
              (throw FormatException(
                  'Connector icon/imageUrl/iconUrl is missing'));
          if (connIcon.isEmpty) {
            throw FormatException('Connector icon/imageUrl/iconUrl is empty');
          }

          // IMPROVED: Use fallback status if missing
          final String connStatus =
              connData['status']?.toString() ?? 'Available';
          if (connStatus.isEmpty) {
            debugPrint('Empty connector status, using fallback: Available');
          }

          // Optional fields
          double? price = (connData['price'] as num?)?.toDouble();
          int? totalGuns = (connData['totalGuns'] as num?)
                  ?.round() ?? // BULLETPROOF: Use round() instead of toInt()
              (connData['total_guns'] as num?)
                  ?.round(); // BULLETPROOF: Use round() instead of toInt()

          // IMPROVED: availableGuns with fallback
          int availableGuns;
          final rawAvailableGuns = connData['availableGuns'] ??
              connData['freeGuns'] ??
              connData['available_guns'];
          if (rawAvailableGuns == null || rawAvailableGuns is! num) {
            availableGuns = totalGuns ?? 1; // Use totalGuns or default to 1
            debugPrint(
                'No available guns data, using fallback: $availableGuns');
          } else {
            availableGuns = rawAvailableGuns
                .round(); // BULLETPROOF: Use round() instead of toInt()
          }

          dynamic maxPower = connData[
                  'maxElectricPower'] ?? // BULLETPROOF: Store as dynamic, no conversion
              connData[
                  'max_electric_power']; // BULLETPROOF: Store as dynamic, no conversion

          stationConnectors.add(app_model.Connector(
            id: connId,
            name: connName,
            type: connType,
            price: price,
            power: connPower,
            totalGuns: totalGuns,
            availableGuns: availableGuns,
            status: connStatus,
            icon: connIcon,
            maxElectricPower: maxPower,
          ));
        }
      }
    }

    // Handle images
    List<String> stationImages = [];
    if (marker['images'] is List) {
      for (var img in marker['images']) {
        if (img is String && img.isNotEmpty) {
          stationImages.add(img);
        }
      }
    }

    // Markers do not contain UIDs - log this fact
    debugPrint(
        'Converting marker data for station ID: $stationId (markers do not contain UIDs)');

    // Create the station with all extracted fields
    return app_model.Station(
      id: stationId,
      uid: stationUid, // Always empty string for markers
      name: stationName,
      address: stationAddress,
      city: marker['city']?.toString(),
      state: marker['state']?.toString(),
      latitude: stationLatitude,
      longitude: stationLongitude,
      distance: stationDistance,
      status: stationStatus,
      rating: stationRating,
      reviews: stationReviews,
      connectors: stationConnectors,
      images: stationImages,
      evses: [],
      openingTimes: marker['opening_hours']?.toString() ??
          marker['operating_hours']?.toString(),
      mapPinUrl: marker['map_pin_url']?.toString(),
      focusedMapPinUrl: marker['focused_map_pin_url']?.toString(),
      types: null,
    );
  }

  /// Convert a StationDetailsData object to a Station object
  static app_model.Station fromStationDetail(
      station_details_response.StationDetailsData detail) {
    // Validate required fields
    if (detail.name == null) {
      throw FormatException('StationDetailsData name is null');
    }
    if (detail.address == null) {
      throw FormatException('StationDetailsData address is null');
    }
    if (detail.latitude == null) {
      throw FormatException('StationDetailsData latitude is null');
    }
    if (detail.longitude == null) {
      throw FormatException('StationDetailsData longitude is null');
    }

    // Extract station information
    final String stationName = detail.name!;
    final String stationAddress = detail.address!;
    final double stationLat = detail.latitude!;
    final double stationLong = detail.longitude!;

    // We don't need to extract EVSE keys for fallback anymore
    // Just check if EVSEs exist for logging purposes
    if (detail.evses == null || detail.evses!.isEmpty) {
      debugPrint('WARNING: StationDetailsData has no EVSEs');
    }

    // Process connectors
    List<app_model.Connector> stationConnectors = [];

    // Extract connectors from EVSEs if available
    if (detail.evses != null) {
      detail.evses!.forEach((evseId, evseDetail) {
        if (evseDetail.connectors != null) {
          for (var connector in evseDetail.connectors!) {
            // Validate connector fields
            if (connector.status == null) {
              throw FormatException('Connector status is null');
            }
            if (connector.type == null) {
              throw FormatException('Connector type is null');
            }

            // Extract connector data
            final String connectorId = connector.connectorId ?? evseId;
            final String connectorType = connector.type!;
            final String connectorStatus = connector.status!;

            // Format power output
            String connectorPowerOutput;
            if (connector.maxElectricPower != null) {
              connectorPowerOutput = '${connector.maxElectricPower} kW';
            } else if (evseDetail.powerOutput != null) {
              connectorPowerOutput = evseDetail.powerOutput!;
            } else {
              connectorPowerOutput = 'Unknown';
            }

            // Get icon URL
            final String connectorIconUrl = connector.icon ?? '';

            // Determine availability based on status
            final String formattedStatus =
                formatConnectorStatus(connectorStatus);
            debugPrint(
                'Connector status: $connectorStatus -> formatted: $formattedStatus');
            final int availableGuns = formattedStatus == 'Available' ? 1 : 0;

            // Handle price information
            double? price;
            if (connector.price != null) {
              price = connector.price!.toDouble();
            } else if (evseDetail.price != null) {
              price = evseDetail.price!.toDouble();
            }

            // Create connector object
            stationConnectors.add(app_model.Connector(
              id: connectorId,
              name: connector.label ?? connectorType,
              type: connectorType,
              price: price,
              power: connectorPowerOutput,
              totalGuns: 1,
              availableGuns: availableGuns,
              icon: connectorIconUrl,
              status: connectorStatus,
              maxElectricPower: connector.maxElectricPower,
              standard: connector.standard,
              priceLabel: connector.priceLabel,
              pricePerUnit: connector.pricePerUnit,
              soc: connector.soc,
              evsesUid: connector.evsesUid,
            ));
          }
        }
      });
    }

    // Extract UID directly from the API response
    final String? stationUid = detail.uid;

    // Log UID information for debugging
    if (stationUid != null && stationUid.isNotEmpty) {
      debugPrint('StationDetailsData has UID: $stationUid');
    } else {
      debugPrint(
          'ERROR: StationDetailsData has no UID. Cannot proceed without a valid UID.');
      throw Exception(
          'Station UID is missing in API response. Cannot create Station object without a valid UID.');
    }

    // Use the UID as the station ID - no fallbacks
    final String stationId = stationUid;

    // Handle images
    List<String> images = [];
    if (detail.images != null && detail.images!.isNotEmpty) {
      images.add(detail.images!);
    }

    // Create station object
    return app_model.Station(
      id: stationId,
      uid: detail
          .uid!, // Required field - should be validated before calling this method
      name: stationName,
      address: stationAddress,
      city: detail.city,
      state: detail.state,
      images: images,
      evses: [],
      latitude: stationLat,
      longitude: stationLong,
      distance: 0.0,
      status: detail.openStatus == true
          ? 'Available'
          : (detail.openStatus == false ? 'Unavailable' : ''),
      rating: (detail.rate as num?)?.toDouble() ?? 0.0,
      reviews: (detail.rateTotal as num?)?.round() ??
          0, // BULLETPROOF: Use round() instead of toInt()
      connectors: stationConnectors,
      mapPinUrl: null,
      focusedMapPinUrl: null,
      types: null,
      openingTimes: detail.openingTimes,
      openStatus: detail.openStatus,
    );
  }
}
