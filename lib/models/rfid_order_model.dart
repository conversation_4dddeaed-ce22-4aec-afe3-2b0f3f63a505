/// Model for RFID order data
class RfidOrderData {
  final String name;
  final String email;
  final String mobileNumber;
  final String address;
  final String state;
  final String pincode;
  final String amount;

  RfidOrderData({
    required this.name,
    required this.email,
    required this.mobileNumber,
    required this.address,
    required this.state,
    required this.pincode,
    required this.amount,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'email': email,
      'mobile_number': mobileNumber,
      'address': address,
      'state': state,
      'pincode': pincode,
      'amount': amount,
    };
  }

  factory RfidOrderData.fromJson(Map<String, dynamic> json) {
    return RfidOrderData(
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      mobileNumber: json['mobile_number'] ?? '',
      address: json['address'] ?? '',
      state: json['state'] ?? '',
      pincode: json['pincode'] ?? '',
      amount: json['amount'] ?? '',
    );
  }
}

/// Model for order response
class RfidOrderResponse {
  final bool success;
  final String? message;
  final String? environment;
  final String? merchantId;
  final String? txnId;
  final Map<String, dynamic>? payload;

  RfidOrderResponse({
    required this.success,
    this.message,
    this.environment,
    this.merchantId,
    this.txnId,
    this.payload,
  });

  factory RfidOrderResponse.fromJson(Map<String, dynamic> json) {
    return RfidOrderResponse(
      success: json['success'] ?? false,
      message: json['message'],
      environment: json['environment'],
      merchantId: json['merchantId'],
      txnId: json['txnId'],
      payload: json['payload'],
    );
  }
}

/// Model for payment response
class PaymentResponseData {
  final String code;
  final String transactionId;
  final String? checksum;
  final Map<String, dynamic> response;
  final Map<String, dynamic> callBackResponse;

  PaymentResponseData({
    required this.code,
    required this.transactionId,
    this.checksum,
    required this.response,
    required this.callBackResponse,
  });

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'transactionId': transactionId,
      'checksum': checksum,
      'response': response,
      'call_back_response': callBackResponse,
    };
  }
}
