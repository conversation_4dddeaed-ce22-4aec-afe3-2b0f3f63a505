/// Model class for profile update request
class ProfileUpdateRequest {
  final String? id;
  final String name;
  final String email;

  ProfileUpdateRequest({
    this.id,
    required this.name,
    required this.email,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    
    if (id != null) {
      data['id'] = id;
    }
    
    data['name'] = name;
    data['email'] = email;
    
    return data;
  }

  factory ProfileUpdateRequest.fromJson(Map<String, dynamic> json) {
    return ProfileUpdateRequest(
      id: json['id'],
      name: json['name'] ?? '',
      email: json['email'] ?? '',
    );
  }
}
