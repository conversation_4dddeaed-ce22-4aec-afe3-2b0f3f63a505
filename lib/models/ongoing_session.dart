/// Model for ongoing charging session from API response
class OngoingSession {
  final int id;
  final String chargerUid;
  final String invoiceNumber;
  final String connectorId;
  final String chargerSerialNumber;
  final double evChargingRate;
  final String bookingId;
  final String? vehicleId;
  final String status;
  final DateTime createdAt;
  final String authorizationReference;
  final OngoingSessionCharger charger;

  OngoingSession({
    required this.id,
    required this.chargerUid,
    required this.invoiceNumber,
    required this.connectorId,
    required this.chargerSerialNumber,
    required this.evChargingRate,
    required this.bookingId,
    this.vehicleId,
    required this.status,
    required this.createdAt,
    required this.authorizationReference,
    required this.charger,
  });

  factory OngoingSession.fromJson(Map<String, dynamic> json) {
    return OngoingSession(
      id: json['id'] ?? 0,
      chargerUid: json['charger_uid'] ?? '',
      invoiceNumber: json['invoice_number'] ?? '',
      connectorId: json['connector_id'] ?? '',
      chargerSerialNumber: json['charger_serial_number'] ?? '',
      evChargingRate: (json['ev_charging_rate'] as num?)?.toDouble() ?? 0.0,
      bookingId: json['booking_id'] ?? '',
      vehicleId: json['vehicle_id'],
      status: json['status'] ?? '',
      createdAt: DateTime.parse(
        json['created_at'] ?? DateTime.now().toIso8601String(),
      ),
      authorizationReference: json['authorization_reference'] ?? '',
      charger: OngoingSessionCharger.fromJson(json['charger'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'charger_uid': chargerUid,
      'invoice_number': invoiceNumber,
      'connector_id': connectorId,
      'charger_serial_number': chargerSerialNumber,
      'ev_charging_rate': evChargingRate,
      'booking_id': bookingId,
      'vehicle_id': vehicleId,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'authorization_reference': authorizationReference,
      'charger': charger.toJson(),
    };
  }
}

/// Model for charger information in ongoing session
class OngoingSessionCharger {
  final String uid;
  final String chargerName;

  OngoingSessionCharger({
    required this.uid,
    required this.chargerName,
  });

  factory OngoingSessionCharger.fromJson(Map<String, dynamic> json) {
    return OngoingSessionCharger(
      uid: json['uid'] ?? '',
      chargerName: json['charger_name'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'charger_name': chargerName,
    };
  }
}

/// Response model for ongoing sessions API
class OngoingSessionsResponse {
  final List<OngoingSession> data;
  final bool success;

  OngoingSessionsResponse({
    required this.data,
    required this.success,
  });

  factory OngoingSessionsResponse.fromJson(Map<String, dynamic> json) {
    return OngoingSessionsResponse(
      data: (json['data'] as List<dynamic>?)
              ?.map((e) => OngoingSession.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      success: json['success'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data.map((e) => e.toJson()).toList(),
      'success': success,
    };
  }

  /// Check if there are any active sessions
  bool get hasActiveSessions => data.isNotEmpty;

  /// Get count of active sessions
  int get activeSessionsCount => data.length;
}
