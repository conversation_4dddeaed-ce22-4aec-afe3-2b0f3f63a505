import 'dart:convert';

/// Model for review response from API
class ReviewResponse {
  final bool success;
  final List<ApiReview> reviews;

  ReviewResponse({
    required this.success,
    required this.reviews,
  });

  factory ReviewResponse.fromJson(Map<String, dynamic> json) {
    return ReviewResponse(
      success: json['success'] ?? false,
      reviews: json['reviews'] != null
          ? (json['reviews'] as List).map((item) => ApiReview.fromJson(item)).toList()
          : [],
    );
  }
}

/// Model for a review
class ApiReview {
  final String comment;
  final double rate;
  final List<String> tags;
  final String locationId;

  ApiReview({
    required this.comment,
    required this.rate,
    required this.tags,
    required this.locationId,
  });

  factory ApiReview.fromJson(Map<String, dynamic> json) {
    return ApiReview(
      comment: json['comment'] ?? '',
      rate: (json['rate'] as num?)?.toDouble() ?? 0.0,
      tags: json['tags'] != null
          ? (json['tags'] as List).map((item) => item.toString()).toList()
          : [],
      locationId: json['location_id'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'comment': comment,
      'rate': rate,
      'tags': tags,
      'location_id': locationId,
    };
  }

  String toJsonString() {
    return jsonEncode(toJson());
  }
}

/// Model for review save request
class ReviewSaveRequest {
  final String comment;
  final double rate;
  final List<String> tags;
  final String locationId;

  ReviewSaveRequest({
    required this.comment,
    required this.rate,
    required this.tags,
    required this.locationId,
  });

  Map<String, dynamic> toJson() {
    return {
      'comment': comment,
      'rate': rate,
      'tags': tags,
      'location_id': locationId,
    };
  }

  String toJsonString() {
    return jsonEncode(toJson());
  }
}
