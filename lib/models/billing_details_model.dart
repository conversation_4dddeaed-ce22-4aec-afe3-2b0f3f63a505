/// Model for billing details response from the API
class BillingDetailsResponse {
  final BillingData data;
  final int statusCode;
  final String statusMessage;
  final String timestamp;
  final bool success;

  BillingDetailsResponse({
    required this.data,
    required this.statusCode,
    required this.statusMessage,
    required this.timestamp,
    required this.success,
  });

  factory BillingDetailsResponse.fromJson(Map<String, dynamic> json) {
    return BillingDetailsResponse(
      data: BillingData.fromJson(json['data'] ?? {}),
      statusCode: json['status_code'] ?? 0,
      statusMessage: json['status_message'] ?? '',
      timestamp: json['timestamp'] ?? '',
      success: json['success'] ?? false,
    );
  }
}

/// Model for the billing data
class BillingData {
  final String gstNumber;
  final String gstAddress;
  final StationInfo station;
  final String invoiceNumber;
  final String startDateTime;
  final String endDateTime;
  final double cost;
  final double igst;
  final double cgst;
  final double sgst;
  final bool igstShow; // Controls whether to show IGST or CGST/SGST
  final double totalCost;
  final double units;
  final int socStart;
  final int socEnd;
  final String? vehicleDetails;
  final String chargerName;
  final String connector;
  final String connectorType;
  final String locationUid;

  BillingData({
    required this.gstNumber,
    required this.gstAddress,
    required this.station,
    required this.invoiceNumber,
    required this.startDateTime,
    required this.endDateTime,
    required this.cost,
    required this.igst,
    required this.cgst,
    required this.sgst,
    required this.igstShow,
    required this.totalCost,
    required this.units,
    required this.socStart,
    required this.socEnd,
    this.vehicleDetails,
    required this.chargerName,
    required this.connector,
    required this.connectorType,
    required this.locationUid,
  });

  factory BillingData.fromJson(Map<String, dynamic> json) {
    return BillingData(
      gstNumber: json['gst_number'] ?? '',
      gstAddress: json['gst_address'] ?? '',
      station: StationInfo.fromJson(json['station'] ?? {}),
      invoiceNumber: json['invoice_number'] ?? '',
      startDateTime: json['start_date_time'] ?? '',
      endDateTime: json['end_date_time'] ?? '',
      cost: (json['cost'] as num?)?.toDouble() ?? 0.0,
      igst: (json['igst'] as num?)?.toDouble() ?? 0.0,
      cgst: (json['cgst'] as num?)?.toDouble() ?? 0.0,
      sgst: (json['sgst'] as num?)?.toDouble() ?? 0.0,
      igstShow: _parseIgstShow(
          json['igst_show']), // Robust parsing for boolean/numeric values
      totalCost: (json['total_cost'] as num?)?.toDouble() ?? 0.0,
      units: (json['units'] as num?)?.toDouble() ?? 0.0,
      socStart: json['soc_start'] ?? 0,
      socEnd: json['soc_end'] ?? 0,
      vehicleDetails: json['vehicle_details'],
      chargerName: json['charger_name'] ?? '',
      connector: json['connector'] ?? '',
      connectorType: json['connector_type'] ?? '',
      locationUid: json['location_uid'] ?? '',
    );
  }

  /// Robust parsing for igst_show field that handles multiple formats:
  /// - Boolean: true/false
  /// - Numeric: 1/0
  /// - String: "true"/"false", "1"/"0"
  /// - Missing/null: defaults to false (show CGST/SGST)
  static bool _parseIgstShow(dynamic value) {
    if (value == null) {
      return false; // Default to CGST/SGST when field is missing
    }

    // Handle boolean values
    if (value is bool) {
      return value;
    }

    // Handle numeric values (1 = true, 0 or any other number = false)
    if (value is num) {
      return value == 1;
    }

    // Handle string values
    if (value is String) {
      final lowerValue = value.toLowerCase().trim();
      return lowerValue == 'true' || lowerValue == '1';
    }

    // Default to false for any other type
    return false;
  }
}

/// Model for station information
class StationInfo {
  final String uid;
  final String name;
  final String address;
  final String city;
  final String state;
  final String country;
  final String postalCode;

  const StationInfo({
    required this.uid,
    required this.name,
    required this.address,
    required this.city,
    required this.state,
    required this.country,
    required this.postalCode,
  });

  factory StationInfo.fromJson(Map<String, dynamic> json) {
    return StationInfo(
      uid: json['uid'] ?? '',
      name: json['name'] ?? '',
      address: json['address'] ?? '',
      city: json['city'] ?? '',
      state: json['state'] ?? '',
      country: json['country'] ?? '',
      postalCode: json['postal_code'] ?? '',
    );
  }
}

/// Extension to add computed properties
extension BillingDataExtension on BillingData {
  /// Get formatted start date time
  DateTime get startDateTimeParsed {
    try {
      return DateTime.parse(startDateTime);
    } catch (e) {
      return DateTime.now();
    }
  }

  /// Get formatted end date time
  DateTime get endDateTimeParsed {
    try {
      return DateTime.parse(endDateTime);
    } catch (e) {
      return DateTime.now();
    }
  }

  /// Get charging duration
  Duration get chargingDuration {
    return endDateTimeParsed.difference(startDateTimeParsed);
  }

  /// Get formatted charging duration
  String get formattedDuration {
    final duration = chargingDuration;
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    return '${hours}h ${minutes}m';
  }

  /// Get formatted start time
  String get formattedStartTime {
    final date = startDateTimeParsed;
    return '${date.day}/${date.month}/${date.year} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  /// Get formatted end time
  String get formattedEndTime {
    final date = endDateTimeParsed;
    return '${date.day}/${date.month}/${date.year} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  /// Get total tax amount
  double get totalTax {
    return igst + cgst + sgst;
  }

  /// Check if charging was free
  bool get isFreeCharging {
    return totalCost == 0.0;
  }

  /// Get energy efficiency (units per rupee)
  double get energyEfficiency {
    if (totalCost == 0.0) return 0.0;
    return units / totalCost;
  }

  /// Get tax display mode description for debugging
  String get taxDisplayMode {
    return igstShow ? 'IGST Only' : 'CGST + SGST';
  }

  /// Get applicable tax amount based on display mode
  double get applicableTaxAmount {
    return igstShow ? igst : (cgst + sgst);
  }

  /// Get SOC improvement percentage
  int get socImprovement {
    return socEnd - socStart;
  }

  /// Get full station address
  String get fullStationAddress {
    return '${station.address}, ${station.city}, ${station.state} ${station.postalCode}';
  }
}
