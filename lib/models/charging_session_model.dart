/// Model for individual charging session transaction
class ChargingSessionModel {
  final int id;
  final String chargerUid;
  final String invoiceNumber;
  final String connectorId;
  final String chargerSerialNumber;
  final double evChargingRate;
  final String bookingId;
  final String? vehicleId;
  final String status;
  final DateTime createdAt;
  final String locationUid;
  final ChargerInfo charger;
  final LocationInfo location;

  ChargingSessionModel({
    required this.id,
    required this.chargerUid,
    required this.invoiceNumber,
    required this.connectorId,
    required this.chargerSerialNumber,
    required this.evChargingRate,
    required this.bookingId,
    this.vehicleId,
    required this.status,
    required this.createdAt,
    required this.locationUid,
    required this.charger,
    required this.location,
  });

  factory ChargingSessionModel.fromJson(Map<String, dynamic> json) {
    return ChargingSessionModel(
      id: json['id'] ?? 0,
      chargerUid: json['charger_uid'] ?? '',
      invoiceNumber: json['invoice_number'] ?? '',
      connectorId: json['connector_id'] ?? '',
      chargerSerialNumber: json['charger_serial_number'] ?? '',
      evChargingRate: (json['ev_charging_rate'] as num?)?.toDouble() ?? 0.0,
      bookingId: json['booking_id'] ?? '',
      vehicleId: json['vehicle_id'],
      status: json['status'] ?? '',
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      locationUid: json['location_uid'] ?? '',
      charger: ChargerInfo.fromJson(json['charger'] ?? {}),
      location: LocationInfo.fromJson(json['location'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'charger_uid': chargerUid,
      'invoice_number': invoiceNumber,
      'connector_id': connectorId,
      'charger_serial_number': chargerSerialNumber,
      'ev_charging_rate': evChargingRate,
      'booking_id': bookingId,
      'vehicle_id': vehicleId,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'location_uid': locationUid,
      'charger': charger.toJson(),
      'location': location.toJson(),
    };
  }

  // Helper methods
  bool get isCompleted => status.toUpperCase() == 'COMPLETED';
  bool get isRejected => status.toUpperCase() == 'REJECTED';
  bool get isPending => status.toUpperCase() == 'PENDING';

  String get statusDisplayText {
    switch (status.toUpperCase()) {
      case 'COMPLETED':
        return 'Completed';
      case 'REJECTED':
        return 'Rejected';
      case 'PENDING':
        return 'Pending';
      default:
        return status;
    }
  }

  int get statusColor {
    switch (status.toUpperCase()) {
      case 'COMPLETED':
        return 0xFF8cc051; // Lime green
      case 'REJECTED':
        return 0xFFE53935; // Red
      case 'PENDING':
        return 0xFFFF9800; // Orange
      default:
        return 0xFF757575; // Grey
    }
  }

  String get formattedDate {
    // Use raw API timestamp data without timezone conversion
    return '${createdAt.day}/${createdAt.month}/${createdAt.year} • ${createdAt.hour.toString().padLeft(2, '0')}:${createdAt.minute.toString().padLeft(2, '0')}';
  }

  String get formattedChargingRate {
    return '${evChargingRate.toStringAsFixed(1)} kW';
  }
}

/// Model for charger information
class ChargerInfo {
  final String uid;
  final String chargerName;

  ChargerInfo({
    required this.uid,
    required this.chargerName,
  });

  factory ChargerInfo.fromJson(Map<String, dynamic> json) {
    return ChargerInfo(
      uid: json['uid'] ?? '',
      chargerName: json['charger_name'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'charger_name': chargerName,
    };
  }
}

/// Model for location information
class LocationInfo {
  final String uid;
  final String address;

  LocationInfo({
    required this.uid,
    required this.address,
  });

  factory LocationInfo.fromJson(Map<String, dynamic> json) {
    return LocationInfo(
      uid: json['uid'] ?? '',
      address: json['address'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'address': address,
    };
  }
}

/// Response model for charging sessions API
class ChargingSessionResponse {
  final List<ChargingSessionModel> data;
  final bool success;
  final String? message;

  ChargingSessionResponse({
    required this.data,
    required this.success,
    this.message,
  });

  factory ChargingSessionResponse.fromJson(Map<String, dynamic> json) {
    var dataList = json['data'] as List? ?? [];
    List<ChargingSessionModel> sessions = dataList
        .map((item) => ChargingSessionModel.fromJson(item))
        .toList();

    return ChargingSessionResponse(
      data: sessions,
      success: json['success'] ?? false,
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data.map((session) => session.toJson()).toList(),
      'success': success,
      'message': message,
    };
  }
}
