import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../core/api/api_service.dart';
import '../core/api/api_config.dart';
import '../core/models/api_response.dart';

/// Service for handling review operations
class ReviewService {
  final ApiService _apiService = ApiService();

  /// Submit a review for a station
  ///
  /// Endpoint: POST /api/v1/user/reviews/save
  /// Payload: {
  ///   "comment": "Best",
  ///   "rate": 4,
  ///   "tags": ["best"],
  ///   "location_id": "a81f3b85-a4bf-4dc6-98d0-b92fa7de0a13"
  /// }
  Future<ApiResponse<Map<String, dynamic>>> submitReview({
    required String locationId,
    required int rating,
    required String comment,
    List<String>? tags,
  }) async {
    try {
      debugPrint('🌟 ===== SUBMITTING REVIEW =====');
      debugPrint('🌟 Location ID: $locationId');
      debugPrint('🌟 Rating: $rating');
      debugPrint('🌟 Comment: $comment');
      debugPrint('🌟 Tags: ${tags ?? []}');

      // Validate input
      if (locationId.isEmpty) {
        throw Exception('Location ID is required');
      }
      if (rating < 1 || rating > 5) {
        throw Exception('Rating must be between 1 and 5');
      }
      if (comment.trim().isEmpty) {
        throw Exception('Comment is required');
      }

      // Prepare payload according to API specification
      final Map<String, dynamic> payload = {
        'comment': comment.trim(),
        'rate': rating,
        'tags': tags ?? [],
        'location_id': locationId,
      };

      debugPrint('🌟 Payload: ${jsonEncode(payload)}');
      debugPrint('🌟 Endpoint: ${ApiConfig.submitReview}');

      // Make API call
      final response = await _apiService.post(
        ApiConfig.submitReview,
        data: payload,
      );

      debugPrint('🌟 Review submission response: $response');

      // Handle response
      if (response is Map<String, dynamic>) {
        final bool success = response['success'] ?? false;
        final String message = response['message'] ?? 'Review submitted successfully';

        if (success) {
          debugPrint('✅ Review submitted successfully');
          return ApiResponse<Map<String, dynamic>>(
            success: true,
            message: message,
            data: response,
          );
        } else {
          debugPrint('❌ Review submission failed: $message');
          return ApiResponse<Map<String, dynamic>>(
            success: false,
            message: message,
          );
        }
      } else {
        throw Exception('Invalid response format from server');
      }
    } catch (e) {
      debugPrint('❌ Error submitting review: $e');
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: 'Failed to submit review: $e',
      );
    }
  }

  /// Fetch reviews for a station
  ///
  /// Endpoint: GET /api/v1/user/reviews/{locationId}
  Future<ApiResponse<List<Map<String, dynamic>>>> getReviews(String locationId) async {
    try {
      debugPrint('🌟 ===== FETCHING REVIEWS =====');
      debugPrint('🌟 Location ID: $locationId');

      if (locationId.isEmpty) {
        throw Exception('Location ID is required');
      }

      // Make API call to fetch reviews
      final response = await _apiService.get('${ApiConfig.getReviews}/$locationId');

      debugPrint('🌟 Reviews fetch response: $response');

      if (response is Map<String, dynamic>) {
        final bool success = response['success'] ?? false;
        final String message = response['message'] ?? '';

        if (success) {
          final List<dynamic> reviewsList = response['reviews'] ?? response['data'] ?? [];
          final List<Map<String, dynamic>> reviews = reviewsList
              .map((review) => review as Map<String, dynamic>)
              .toList();

          debugPrint('✅ Fetched ${reviews.length} reviews successfully');
          return ApiResponse<List<Map<String, dynamic>>>(
            success: true,
            message: message,
            data: reviews,
          );
        } else {
          debugPrint('❌ Failed to fetch reviews: $message');
          return ApiResponse<List<Map<String, dynamic>>>(
            success: false,
            message: message,
            data: [],
          );
        }
      } else {
        throw Exception('Invalid response format from server');
      }
    } catch (e) {
      debugPrint('❌ Error fetching reviews: $e');
      return ApiResponse<List<Map<String, dynamic>>>(
        success: false,
        message: 'Failed to fetch reviews: $e',
        data: [],
      );
    }
  }

  /// Validate review data
  bool isValidReview({
    required String locationId,
    required int rating,
    required String comment,
  }) {
    if (locationId.trim().isEmpty) return false;
    if (rating < 1 || rating > 5) return false;
    if (comment.trim().isEmpty) return false;
    return true;
  }

  /// Generate tags based on rating (optional helper)
  List<String> generateTagsFromRating(int rating) {
    switch (rating) {
      case 5:
        return ['excellent', 'best'];
      case 4:
        return ['good', 'recommended'];
      case 3:
        return ['average', 'okay'];
      case 2:
        return ['poor', 'needs improvement'];
      case 1:
        return ['bad', 'not recommended'];
      default:
        return [];
    }
  }
}
