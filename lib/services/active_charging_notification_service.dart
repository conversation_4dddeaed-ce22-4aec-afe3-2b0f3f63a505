import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:ecoplug/models/charging_session.dart';
import 'package:ecoplug/config/notification_config.dart';
import 'package:ecoplug/services/notification_navigation_service.dart';
import 'package:ecoplug/services/local_notification_manager.dart';

/// Active Charging Session Notification Service
/// Manages persistent, ongoing notifications during active charging sessions
/// Provides real-time updates and seamless navigation to charging session screen
class ActiveChargingNotificationService {
  static final ActiveChargingNotificationService _instance =
      ActiveChargingNotificationService._internal();
  factory ActiveChargingNotificationService() => _instance;
  ActiveChargingNotificationService._internal();

  final LocalNotificationManager _notificationManager =
      LocalNotificationManager();
  final NotificationNavigationService _navigationService =
      NotificationNavigationService();
  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  bool _isInitialized = false;
  bool _isChargingSessionActive = false;
  String? _activeSessionId;
  Timer? _updateTimer;

  // Notification constants
  static const int _chargingNotificationId = 1001;
  static const String _chargingChannelId = 'active_charging_session';
  static const String _chargingChannelName = 'Active Charging Session';
  static const String _chargingChannelDescription =
      'Persistent notifications for ongoing charging sessions';

  /// Initialize the active charging notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint(
          '🔋 ===== INITIALIZING ACTIVE CHARGING NOTIFICATION SERVICE =====');

      // Initialize centralized notification manager
      await _notificationManager.initialize();

      // Initialize local notifications for direct display
      await _initializeLocalNotifications();

      // Create charging notification channel
      await _createChargingNotificationChannel();

      _isInitialized = true;
      debugPrint(
          '✅ Active charging notification service initialized successfully');
      debugPrint('🔋 Using centralized notification manager');
    } catch (e) {
      debugPrint(
          '❌ Error initializing active charging notification service: $e');
      rethrow;
    }
  }

  /// Initialize local notifications for charging sessions
  Future<void> _initializeLocalNotifications() async {
    debugPrint('🔋 Initializing local notifications for charging sessions...');

    // BRANDING: Use EcoPlug launcher icon for consistent branding
    const androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onChargingNotificationTapped,
    );

    debugPrint('✅ Charging session local notifications initialized');
  }

  /// Create Android notification channel for charging sessions
  Future<void> _createChargingNotificationChannel() async {
    if (!Platform.isAndroid) return;

    const channel = AndroidNotificationChannel(
      _chargingChannelId,
      _chargingChannelName,
      description: _chargingChannelDescription,
      importance: Importance.high,
      showBadge: true,
      enableVibration: true,
      enableLights: true,
      ledColor: Color(0xFF00FF00), // Green LED for charging
      playSound: false, // No sound for ongoing notifications
    );

    await _localNotifications
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);

    debugPrint('✅ Active charging notification channel created');
  }

  /// Handle charging notification tap - navigate to charging session screen
  Future<void> _onChargingNotificationTapped(
      NotificationResponse response) async {
    debugPrint('🔋 Active charging notification tapped');
    debugPrint('🔋 Payload: ${response.payload}');

    try {
      if (response.payload != null && response.payload!.isNotEmpty) {
        // Parse the payload to get session information
        final sessionData = _parseNotificationPayload(response.payload!);

        // Navigate to charging session screen
        await _navigationService.storeNavigationIntent(
          'charging_session',
          response.payload,
        );
        await _navigationService.processPendingNavigationIntents();

        debugPrint('✅ Navigation to charging session screen initiated');
      } else {
        // Fallback navigation to charging session
        await _navigationService.storeNavigationIntent(
            'charging_session', null);
        await _navigationService.processPendingNavigationIntents();
      }
    } catch (e) {
      debugPrint('❌ Error handling charging notification tap: $e');
    }
  }

  /// Start active charging session notification
  /// DISABLED: Local notifications removed - using FCM only
  Future<void> startChargingSessionNotification(ChargingSession session) async {
    debugPrint('🔋 ===== CHARGING SESSION NOTIFICATION DISABLED =====');
    debugPrint('🔋 Session ID: ${session.id}');
    debugPrint('🔋 Station: ${session.stationUid}');
    debugPrint('🔋 Connector: ${session.connectorId}');
    debugPrint('🔋 Local notifications disabled - using FCM only');
    debugPrint('✅ FCM notifications will handle charging session updates');
  }

  /// Update active charging session notification with new data
  /// DISABLED: Local notifications removed - using FCM only
  Future<void> updateChargingSessionNotification(
      ChargingSession session) async {
    debugPrint('🔋 ===== CHARGING SESSION UPDATE DISABLED =====');
    debugPrint('🔋 Session ID: ${session.id}');
    debugPrint('🔋 Battery: ${(session.currentCharge * 100).round()}%');
    debugPrint('🔋 Power: ${session.currentPower.toStringAsFixed(1)} kW');
    debugPrint('🔋 Energy: ${session.energyDelivered.toStringAsFixed(2)} kWh');
    debugPrint('🔋 Cost: ₹${session.cost.toStringAsFixed(2)}');
    debugPrint('🔋 Local notifications disabled - using FCM only');
    debugPrint('✅ FCM notifications will handle charging session updates');
  }

  /// Stop active charging session notification
  /// DISABLED: Local notifications removed - using FCM only
  Future<void> stopChargingSessionNotification() async {
    debugPrint('🔋 ===== CHARGING SESSION STOP DISABLED =====');
    debugPrint('🔋 Local notifications disabled - using FCM only');
    debugPrint('✅ FCM notifications will handle charging session completion');

    // Reset internal state for consistency
    _isChargingSessionActive = false;
    _activeSessionId = null;
    _updateTimer?.cancel();
    _updateTimer = null;
  }

  /// Show/update the charging session notification
  Future<void> _showChargingNotification(ChargingSession session) async {
    try {
      // Create notification payload with session data
      final payload = _createNotificationPayload(session);

      // Format charging data for display
      final chargePercentage = (session.currentCharge * 100).round();
      final powerOutput = session.currentPower.toStringAsFixed(1);
      final energyDelivered = session.energyDelivered.toStringAsFixed(2);
      final cost = session.cost.toStringAsFixed(2);
      final duration = session.formattedDuration;

      // Create notification title and body
      final title = 'Charging Active • $chargePercentage%';
      final body = _buildNotificationBody(session);

      // Create Android notification details with custom layout
      final androidDetails = AndroidNotificationDetails(
        _chargingChannelId,
        _chargingChannelName,
        channelDescription: _chargingChannelDescription,
        importance: Importance.high,
        priority: Priority.high,
        ongoing: true, // Makes notification persistent
        autoCancel: false, // Prevents user from dismissing
        showWhen: true,
        when: session.startTime.millisecondsSinceEpoch,
        usesChronometer: true,
        chronometerCountDown: false,
        showProgress: true,
        maxProgress: 100,
        progress: chargePercentage,
        indeterminate: false,
        color: const Color(0xFF4CAF50), // Green color for charging
        colorized: true,
        largeIcon: const DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
        icon: '@mipmap/ic_launcher', // BRANDING: EcoPlug launcher icon
        styleInformation: BigTextStyleInformation(
          body,
          htmlFormatBigText: false,
          contentTitle: title,
          htmlFormatContentTitle: false,
          summaryText: 'EcoPlug Charging Session',
          htmlFormatSummaryText: false,
        ),
        actions: [
          const AndroidNotificationAction(
            'view_session',
            'View Session',
            showsUserInterface: true,
          ),
          const AndroidNotificationAction(
            'stop_charging',
            'Stop Charging',
            showsUserInterface: true,
          ),
        ],
      );

      // Create iOS notification details
      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: false, // No sound for ongoing notifications
        threadIdentifier: 'charging_session',
      );

      final notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // Show the notification directly to ensure it appears in Android notification tray
      await _localNotifications.show(
        _chargingNotificationId,
        title,
        body,
        notificationDetails,
        payload: payload,
      );

      debugPrint(
          '🔋 ✅ Charging notification sent to Android notification tray: $title');
      debugPrint('🔋 📱 Check Android notification tray for: "$title"');
    } catch (e) {
      debugPrint('❌ Error showing charging notification: $e');
    }
  }

  /// Build notification body with charging session details
  String _buildNotificationBody(ChargingSession session) {
    final chargePercentage = (session.currentCharge * 100).round();
    final powerOutput = session.currentPower.toStringAsFixed(1);
    final energyDelivered = session.energyDelivered.toStringAsFixed(2);
    final cost = session.cost.toStringAsFixed(2);
    final duration = session.formattedDuration;
    final co2Saved = session.co2Saved.toStringAsFixed(1);

    return 'Power: $powerOutput kW • Energy: $energyDelivered kWh\n'
        'Duration: $duration • Cost: ₹$cost\n'
        'CO₂ Saved: $co2Saved kg • Station: ${session.stationUid}';
  }

  /// Create notification payload with session data
  String _createNotificationPayload(ChargingSession session) {
    return 'charging_session|${session.id}|${session.stationUid}|${session.connectorId}';
  }

  /// Parse notification payload to extract session data
  Map<String, String> _parseNotificationPayload(String payload) {
    final parts = payload.split('|');
    if (parts.length >= 4) {
      return {
        'type': parts[0],
        'sessionId': parts[1],
        'stationUid': parts[2],
        'connectorId': parts[3],
      };
    }
    return {'type': 'charging_session'};
  }

  /// Start periodic updates for the notification
  void _startPeriodicUpdates(ChargingSession initialSession) {
    _updateTimer?.cancel();

    // Update every 30 seconds
    _updateTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (!_isChargingSessionActive) {
        timer.cancel();
        return;
      }

      // Note: In a real implementation, you would fetch updated session data
      // For now, we'll use the initial session data
      // This should be connected to your charging session data stream
      debugPrint('🔋 Periodic notification update triggered');
    });
  }

  /// Check if charging session notification is currently active
  bool get isChargingSessionActive => _isChargingSessionActive;

  /// Get the active session ID
  String? get activeSessionId => _activeSessionId;

  /// Get service initialization status
  bool get isInitialized => _isInitialized;

  /// Hide charging notification
  Future<void> hideChargingNotification() async {
    try {
      await _localNotifications.cancel(_chargingNotificationId);
      debugPrint('🔋 Charging notification hidden');
    } catch (e) {
      debugPrint('❌ Error hiding charging notification: $e');
    }
  }

  /// Test method to verify charging notifications appear in Android notification tray
  Future<void> testChargingNotification() async {
    try {
      debugPrint('🧪 ===== TESTING CHARGING NOTIFICATION DISPLAY =====');

      // Ensure service is initialized
      if (!_isInitialized) {
        await initialize();
      }

      // Create a test charging session
      final testSession = ChargingSession(
        id: 'TEST_SESSION_${DateTime.now().millisecondsSinceEpoch}',
        stationUid: 'TEST_STATION_001',
        connectorId: 'TEST_CONNECTOR_1',
        startTime: DateTime.now().subtract(const Duration(minutes: 15)),
        currentCharge: 0.65, // 65%
        currentPower: 22.5,
        energyDelivered: 8.75,
        cost: 125.50,
        co2Saved: 2.3,
      );

      // Show test notification
      await _showChargingNotification(testSession);

      debugPrint(
          '🧪 ✅ Test charging notification sent to Android notification tray');
      debugPrint(
          '🧪 📱 Check your Android notification tray for "Charging Active • 65%"');
      debugPrint('🧪 🔋 This notification should appear with charging details');
    } catch (e) {
      debugPrint('🧪 ❌ Error testing charging notification: $e');
    }
  }

  /// Dispose of the service and clean up resources
  void dispose() {
    _updateTimer?.cancel();
    _updateTimer = null;
    _isChargingSessionActive = false;
    _activeSessionId = null;
    debugPrint('🔋 Active charging notification service disposed');
  }
}
