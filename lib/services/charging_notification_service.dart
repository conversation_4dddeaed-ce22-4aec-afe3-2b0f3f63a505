import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'dart:io';
import 'notification_navigation_service.dart';
import '../constants/notification_design_constants.dart';
import '../config/notification_config.dart';
import '../utils/notification_utils.dart';

/// Service for managing Android system notifications during charging sessions
/// Uses pin bar style design with enhanced visual elements and detailed metrics
class ChargingNotificationService {
  static final ChargingNotificationService _instance =
      ChargingNotificationService._internal();
  factory ChargingNotificationService() => _instance;
  ChargingNotificationService._internal();

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  bool _isInitialized = false;
  // Notification ID for charging session (from centralized config)
  static final int _chargingNotificationId = NotificationIds.chargingSession;

  /// Initialize the notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🔔 ===== INITIALIZING CHARGING NOTIFICATION SERVICE =====');
      debugPrint('🔔 Platform: ${Platform.isAndroid ? "Android" : "Other"}');

      // Create notification channel for Android 8.0+ (API level 26+)
      if (Platform.isAndroid) {
        await _createNotificationChannel();
      }

      // Android initialization settings - BRANDING: EcoPlug launcher icon
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // iOS initialization settings (optional, for future use)
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      debugPrint('🔔 Initializing flutter_local_notifications plugin...');
      await _flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // Request notification permissions for Android 13+
      if (Platform.isAndroid) {
        debugPrint('🔔 Requesting notification permissions...');
        final permissionGranted = await _flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin>()
            ?.requestNotificationsPermission();
        debugPrint('🔔 Permission granted: $permissionGranted');

        // Check if notifications are enabled
        final areEnabled = await areNotificationsEnabled();
        debugPrint('🔔 Notifications enabled: $areEnabled');

        if (!areEnabled) {
          debugPrint('⚠️ Notifications are disabled by user');
        }
      }

      _isInitialized = true;
      debugPrint('✅ Charging Notification Service initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing Charging Notification Service: $e');
      debugPrint('❌ Stack trace: ${StackTrace.current}');
      rethrow; // Re-throw to allow caller to handle
    }
  }

  /// Create pixel-perfect notification channel with Material Design specifications
  Future<void> _createNotificationChannel() async {
    if (!Platform.isAndroid) return;

    try {
      debugPrint(
          '🔔 Creating charging notification channel using shared utility...');

      // Use shared notification channel creation utility
      final success = await NotificationUtils.createChargingNotificationChannel(
          _flutterLocalNotificationsPlugin);

      if (success) {
        debugPrint('✅ Charging notification channel created successfully');
      } else {
        debugPrint('❌ Failed to create charging notification channel');
        throw Exception('Failed to create charging notification channel');
      }
    } catch (e) {
      debugPrint('❌ Error creating notification channel: $e');
      rethrow;
    }
  }

  /// Handle notification tap events with guaranteed navigation
  void _onNotificationTapped(NotificationResponse notificationResponse) {
    debugPrint('🔔 ===== NOTIFICATION TAPPED =====');
    debugPrint('🔔 Payload: ${notificationResponse.payload}');
    debugPrint('🔔 Action ID: ${notificationResponse.actionId}');
    debugPrint('🔔 Input: ${notificationResponse.input}');
    debugPrint('🔔 Notification ID: ${notificationResponse.id}');

    try {
      // Handle different action types
      if (notificationResponse.actionId == 'view_details' ||
          notificationResponse.actionId == null) {
        debugPrint('🔔 Opening charging session screen...');
        _navigateToChargingSession(notificationResponse.payload);
      } else if (notificationResponse.actionId == 'stop_charging') {
        debugPrint('🔔 Stop charging action triggered');
        _handleStopChargingAction();
      }
    } catch (e) {
      debugPrint('❌ Error handling notification tap: $e');
    }
  }

  /// Navigate to charging session screen from notification tap
  void _navigateToChargingSession(String? payload) {
    debugPrint('🔔 ===== NAVIGATING TO CHARGING SESSION =====');
    debugPrint('🔔 Payload: $payload');

    // Store navigation intent for app to handle when it becomes active
    _storeNavigationIntent('charging_session', payload);

    // Trigger app to foreground (this will be handled by the main app)
    debugPrint('🔔 Navigation intent stored, app should handle when active');
  }

  /// Handle stop charging action from notification
  void _handleStopChargingAction() {
    debugPrint('🔔 ===== STOP CHARGING ACTION =====');

    // Store stop charging intent
    _storeNavigationIntent('stop_charging', null);

    debugPrint('🔔 Stop charging intent stored');
  }

  /// Store navigation intent for app to handle
  void _storeNavigationIntent(String action, String? payload) {
    debugPrint('🔔 Storing navigation intent: $action with payload: $payload');

    // Use the navigation service to store the intent
    final navigationService = NotificationNavigationService();
    navigationService.storeNavigationIntent(action, payload);
  }

  /// Show persistent charging notification for direct mode (Pin Bar Style)
  Future<void> showChargingNotification({
    required bool isCharging,
    required double chargePercentage,
    required String currentPower,
    required String energyDelivered,
    required String currentPrice,
    required String co2Saved,
    required String chargingTimer,
  }) async {
    try {
      debugPrint('🔔 ===== SHOWING STANDARD CHARGING NOTIFICATION =====');
      debugPrint('🔔 Initialized: $_isInitialized');
      debugPrint('🔔 Is Charging: $isCharging');
      debugPrint('🔔 Charge Percentage: ${(chargePercentage * 100).toInt()}%');
      debugPrint('🔔 Current Power: $currentPower');
      debugPrint('🔔 Energy Delivered: $energyDelivered');
      debugPrint('🔔 Current Price: $currentPrice');
      debugPrint('🔔 CO2 Saved: $co2Saved');
      debugPrint('🔔 Charging Timer: $chargingTimer');

      if (!_isInitialized) {
        debugPrint('🔔 Service not initialized, initializing now...');
        await initialize();
      }

      // Check if notifications are enabled
      final areEnabled = await areNotificationsEnabled();
      debugPrint('🔔 Notifications enabled: $areEnabled');
      if (!areEnabled) {
        debugPrint('❌ Notifications are disabled by user');
        throw Exception('Notifications are disabled by user');
      }
      // Format the charge percentage
      final percentageText = '${(chargePercentage * 100).toInt()}%';

      // Create status text similar to pin bar
      final statusText =
          isCharging ? '🟢 ACTIVE CHARGING' : '⚪ CHARGING COMPLETE';

      // Create compact title for collapsed view (similar to pin bar top section)
      final title = '$statusText • $percentageText • $chargingTimer';

      // Create body text for collapsed view
      final body = isCharging
          ? 'Power: $currentPower • Energy: $energyDelivered • Cost: $currentPrice'
          : 'Charging session completed at $percentageText';

      // Create pixel-perfect expanded content with precise formatting
      final expandedContent = _buildPixelPerfectExpandedContent(
        isCharging: isCharging,
        percentageText: percentageText,
        currentPower: currentPower,
        energyDelivered: energyDelivered,
        currentPrice: currentPrice,
        co2Saved: co2Saved,
        chargingTimer: chargingTimer,
      );

      // Enhanced big text style with precise typography
      final bigTextStyle = BigTextStyleInformation(
        expandedContent,
        contentTitle: _buildStyledContentTitle(statusText, isCharging),
        summaryText: '🔋 Ecoplug • Charging Session',
        htmlFormatContentTitle: true,
        htmlFormatContent: true,
        htmlFormatSummaryText: true,
      );

      // Create notification actions (similar to pin bar close button)
      final actions = <AndroidNotificationAction>[
        if (isCharging)
          const AndroidNotificationAction(
            'stop_charging',
            'Stop Charging',
            contextual: true,
          ),
        const AndroidNotificationAction(
          'view_details',
          'View Details',
        ),
      ];

      // Pixel-perfect Android notification with Material Design specifications
      final androidNotificationDetails = AndroidNotificationDetails(
        'charging_session',
        'Charging Session',
        channelDescription:
            'Real-time charging session notifications with detailed metrics',
        importance: Importance.high,
        priority: Priority.high,
        ongoing: isCharging, // Make it persistent while charging
        autoCancel: !isCharging, // Don't auto-cancel while charging

        // Pixel-perfect progress bar implementation
        showProgress: true,
        maxProgress: 100,
        progress: (chargePercentage * 100).toInt(),
        indeterminate: false,

        // Enhanced visual styling
        styleInformation: bigTextStyle,
        icon: '@mipmap/ic_launcher', // BRANDING: EcoPlug launcher icon
        largeIcon: const DrawableResourceAndroidBitmap(
            '@mipmap/ic_launcher'), // BRANDING: EcoPlug launcher icon

        // Precise color specifications matching app theme using design constants
        color: NotificationDesignConstants
            .primaryGreen, // Primary green from app theme
        colorized: true,

        // LED notification with exact timing using design constants
        ledColor: NotificationDesignConstants.primaryGreen,
        ledOnMs: 800, // Precise timing for visual consistency
        ledOffMs: 1200,

        // Audio/vibration settings for non-intrusive updates
        enableVibration: false, // Silent updates during charging
        playSound: false, // No sound for progress updates

        // Action buttons with Material Design styling
        actions: actions,
        category: AndroidNotificationCategory.progress,
        visibility: NotificationVisibility.public,

        // Precise ticker text for status bar
        ticker: isCharging
            ? 'EcoPlug: $percentageText charging at $currentPower'
            : 'EcoPlug: Charging complete at $percentageText',

        // Android 12+ Material You enhancements
        channelShowBadge: true,
        onlyAlertOnce: true, // Prevent notification sound/vibration on updates
        when: DateTime.now().millisecondsSinceEpoch,
        usesChronometer: isCharging, // Show elapsed time for active sessions
        chronometerCountDown: false,
        showWhen: true,

        // Enhanced icon specifications for pixel-perfect rendering (already set above)

        // Additional Material Design specifications
        groupKey: 'ecoplug_charging_sessions',
        setAsGroupSummary: false,
        groupAlertBehavior: GroupAlertBehavior.children,

        // Precise timing and behavior
        timeoutAfter: null, // No timeout for ongoing charging

        // Enhanced accessibility
        subText: isCharging ? 'Active Session' : 'Session Complete',
      );

      final NotificationDetails notificationDetails = NotificationDetails(
        android: androidNotificationDetails,
      );

      debugPrint('🔔 Notification title: $title');
      debugPrint('🔔 Notification body: $body');
      debugPrint('🔔 Calling _flutterLocalNotificationsPlugin.show()...');

      // Show or update the notification
      await _flutterLocalNotificationsPlugin.show(
        _chargingNotificationId,
        title,
        body,
        notificationDetails,
        payload:
            'charging_session_${chargePercentage}_${isCharging ? 'active' : 'complete'}',
      );

      debugPrint(
          '✅ Standard charging notification shown successfully: $percentageText (${isCharging ? 'Active' : 'Complete'})');
    } catch (e) {
      debugPrint('❌ Error showing standard charging notification: $e');
      debugPrint('❌ Error type: ${e.runtimeType}');
      debugPrint('❌ Stack trace: ${StackTrace.current}');
      rethrow; // Re-throw to allow caller to handle
    }
  }

  /// Build pixel-perfect expanded notification content with precise visual design
  String _buildPixelPerfectExpandedContent({
    required bool isCharging,
    required String percentageText,
    required String currentPower,
    required String energyDelivered,
    required String currentPrice,
    required String co2Saved,
    required String chargingTimer,
  }) {
    // Precise status indicator with exact color coding using design constants
    final statusIcon = isCharging
        ? NotificationDesignConstants.activeChargingIcon
        : NotificationDesignConstants.chargingCompleteIcon;
    final statusText = isCharging ? 'ACTIVE CHARGING' : 'CHARGING COMPLETE';
    final statusColor = NotificationDesignConstants.getColorHex(
        NotificationDesignConstants.getStatusColor(isCharging, false));

    // Create pixel-perfect progress bar with exact spacing
    final progressBar = _buildPixelPerfectProgressBar(percentageText);

    // Get color hex values for consistent styling
    final primaryBlueHex = NotificationDesignConstants.getColorHex(
        NotificationDesignConstants.primaryBlue);
    final textSecondaryHex = NotificationDesignConstants.getColorHex(
        NotificationDesignConstants.textSecondary);
    final textHintHex = NotificationDesignConstants.getColorHex(
        NotificationDesignConstants.textHint);
    final primaryOrangeHex = NotificationDesignConstants.getColorHex(
        NotificationDesignConstants.primaryOrange);
    final primaryGreenHex = NotificationDesignConstants.getColorHex(
        NotificationDesignConstants.primaryGreen);

    // Build content with precise typography and spacing using design constants
    return '''<b><font color="$statusColor">$statusIcon $statusText</font></b>

<b>${NotificationDesignConstants.batteryIcon} Battery: <font color="$primaryBlueHex">$percentageText</font></b>
$progressBar

<font color="$textSecondaryHex">${NotificationDesignConstants.powerIcon} Power:</font> <b>$currentPower</b>
<font color="$textSecondaryHex">${NotificationDesignConstants.energyIcon} Energy:</font> <b>$energyDelivered</b>
<font color="$textSecondaryHex">${NotificationDesignConstants.costIcon} Cost:</font> <b><font color="$primaryOrangeHex">$currentPrice</font></b>
<font color="$textSecondaryHex">${NotificationDesignConstants.co2Icon} CO₂ Saved:</font> <b><font color="$primaryGreenHex">$co2Saved</font></b>
<font color="$textSecondaryHex">${NotificationDesignConstants.timerIcon} Duration:</font> <b>$chargingTimer</b>

<small><font color="$textHintHex">${NotificationDesignConstants.appIcon} Tap to view full details in app</font></small>''';
  }

  /// Build pixel-perfect progress bar with exact visual specifications
  String _buildPixelPerfectProgressBar(String percentageText) {
    final percentage = double.tryParse(percentageText.replaceAll('%', '')) ?? 0;
    final filledLength =
        (percentage / 100 * NotificationDesignConstants.progressBarLength)
            .round();

    // Use precise Unicode characters for pixel-perfect rendering
    final filledBar = NotificationDesignConstants.filledBlock * filledLength;
    final emptyBar = NotificationDesignConstants.emptyBlock *
        (NotificationDesignConstants.progressBarLength - filledLength);

    // Get color based on charge level using design constants
    final progressColor =
        NotificationDesignConstants.getProgressColor(percentage);
    final colorCode = NotificationDesignConstants.getColorHex(progressColor);
    final emptyColorCode = NotificationDesignConstants.getColorHex(
        NotificationDesignConstants.progressEmpty);

    return '<font color="$colorCode">$filledBar</font><font color="$emptyColorCode">$emptyBar</font>';
  }

  /// Build styled content title with precise typography
  String _buildStyledContentTitle(String statusText, bool isCharging) {
    final statusColor =
        NotificationDesignConstants.getStatusColor(isCharging, false);
    final colorHex = NotificationDesignConstants.getColorHex(statusColor);
    return '<b><font color="$colorHex">$statusText</font></b>';
  }

  /// Hide the charging notification
  Future<void> hideChargingNotification() async {
    try {
      await _flutterLocalNotificationsPlugin.cancel(_chargingNotificationId);
      debugPrint('🔔 Charging notification hidden');
    } catch (e) {
      debugPrint('❌ Error hiding charging notification: $e');
    }
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    if (Platform.isAndroid) {
      final androidImplementation = _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();
      return await androidImplementation?.areNotificationsEnabled() ?? false;
    }
    return true; // Assume enabled for other platforms
  }

  /// Request notification permissions with Android version-specific handling
  Future<bool> requestPermissions() async {
    if (Platform.isAndroid) {
      try {
        debugPrint('🔔 ===== REQUESTING NOTIFICATION PERMISSIONS =====');

        final androidImplementation = _flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin>();

        if (androidImplementation == null) {
          debugPrint('❌ Android implementation not available');
          return false;
        }

        // For Android 13+ (API 33+), explicit permission request is required
        debugPrint('🔔 Requesting POST_NOTIFICATIONS permission...');
        final permissionGranted =
            await androidImplementation.requestNotificationsPermission();
        debugPrint(
            '🔔 POST_NOTIFICATIONS permission result: $permissionGranted');

        // Additional check for exact alarm permissions (Android 12+)
        try {
          final exactAlarmPermission =
              await androidImplementation.requestExactAlarmsPermission();
          debugPrint(
              '🔔 SCHEDULE_EXACT_ALARM permission result: $exactAlarmPermission');
        } catch (e) {
          debugPrint(
              '🔔 Exact alarm permission not available or not needed: $e');
        }

        // Verify final permission status
        final finalStatus = await areNotificationsEnabled();
        debugPrint('🔔 Final notification status: $finalStatus');

        return permissionGranted ?? false;
      } catch (e) {
        debugPrint('❌ Error requesting notification permissions: $e');
        return false;
      }
    }
    return true; // Assume granted for other platforms
  }
}
