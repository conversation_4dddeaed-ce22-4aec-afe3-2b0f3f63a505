import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/api_profile_model.dart';
import '../models/api_review_model.dart';
import '../models/api_vehicle_model.dart';
import '../models/bookmark_model.dart';
import '../models/station/station_search_response.dart';
import '../utils/api_constants.dart';

/// Comprehensive API service for handling all API calls
class ApiService {
  final String _baseUrl = ApiConstants.baseUrl;
  final Map<String, String> _headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // Singleton pattern
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  // Add auth token to headers
  Future<void> setAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('auth_token');
    if (token != null && token.isNotEmpty) {
      _headers['Authorization'] = 'Bearer $token';
    }
  }

  // Generic GET request
  Future<Map<String, dynamic>> get(String endpoint) async {
    await setAuthToken();

    try {
      final response = await http.get(
        Uri.parse('$_baseUrl$endpoint'),
        headers: _headers,
      );

      debugPrint('GET $endpoint - Status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        debugPrint('Error in GET $endpoint: ${response.body}');
        throw Exception('Failed to load data: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Exception in GET $endpoint: $e');
      throw Exception('Failed to load data: $e');
    }
  }

  // Generic POST request
  Future<Map<String, dynamic>> post(
      String endpoint, Map<String, dynamic> data) async {
    await setAuthToken();

    try {
      final response = await http.post(
        Uri.parse('$_baseUrl$endpoint'),
        headers: _headers,
        body: jsonEncode(data),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to submit data: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to submit data: $e');
    }
  }

  // Profile API
  Future<ProfileResponse> getUserProfile() async {
    final response = await get(ApiConstants.userProfile);
    return ProfileResponse.fromJson(response);
  }

  // Vehicles API
  Future<VehicleResponse> getVehicles() async {
    final response = await get(ApiConstants.vehicles);
    return VehicleResponse.fromJson(response);
  }

  Future<bool> saveVehicle(VehicleRegistration registration) async {
    final response =
        await post(ApiConstants.saveVehicle, registration.toJson());
    return response['success'] ?? false;
  }

  // DELETED: No default vehicle functionality - only real user vehicles
  Future<bool> setDefaultVehicle(int vehicleId) async {
    // Use the save vehicle endpoint instead
    final response = await post(ApiConstants.saveVehicle, {'id': vehicleId});
    return response['success'] ?? false;
  }

  // Reviews API
  Future<ReviewResponse> getReviews(String locationId) async {
    final response =
        await get('${ApiConstants.reviews}?location_id=$locationId');
    return ReviewResponse.fromJson(response);
  }

  Future<bool> saveReview(ReviewSaveRequest review) async {
    final response = await post(ApiConstants.saveReview, review.toJson());
    return response['success'] ?? false;
  }

  // Bookmarks API
  Future<BookmarkResponse> getBookmarks() async {
    final response = await get(ApiConstants.bookmarks);
    return BookmarkResponse.fromJson(response);
  }

  Future<bool> saveBookmark(BookmarkSaveRequest bookmark) async {
    final response = await post(ApiConstants.saveBookmark, bookmark.toJson());
    return response['success'] ?? false;
  }

  // Station Search API
  Future<StationSearchResponse> searchStationsByName(String query) async {
    try {
      final response = await get(
          '/api/v1/user/station/search?search=${Uri.encodeComponent(query)}');
      return StationSearchResponse.fromJson(response);
    } catch (e) {
      debugPrint('Error searching stations: $e');
      return StationSearchResponse(
        success: false,
        message: 'Failed to search stations: $e',
        data: null,
      );
    }
  }
}
