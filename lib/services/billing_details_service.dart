import 'package:flutter/foundation.dart';
import '../models/billing_details_model.dart';
import '../core/api/api_service.dart';
import '../core/api/api_config.dart';

/// Service to handle billing details API calls
class BillingDetailsService {
  static final BillingDetailsService _instance =
      BillingDetailsService._internal();
  factory BillingDetailsService() => _instance;
  BillingDetailsService._internal();

  final ApiService _apiService = ApiService();

  /// Fetch billing details for a specific transaction ID
  ///
  /// Endpoint: GET /api/v1/user/sessions/billing-details/{transactionId}
  /// Example: https://api2.eeil.online/api/v1/user/sessions/billing-details/23514
  Future<BillingDetailsResponse?> getBillingDetails(
      String transactionId) async {
    try {
      debugPrint(
          '🧾 ===== BILLING API CALL WITH PRESERVED TRANSACTION ID =====');
      debugPrint('🧾 Transaction ID: "$transactionId"');
      debugPrint('🧾 Transaction ID Source: PRESERVED from charging flow');
      debugPrint(
          '🧾 Expected Flow: Start Transaction → Store → Stop → Billing');
      debugPrint('🧾 Endpoint: ${ApiConfig.billingDetails}/$transactionId');

      // Use ApiService for API call (same pattern as other services)
      final rawResponse = await _apiService.get(
        '${ApiConfig.billingDetails}/$transactionId',
      );

      debugPrint('🧾 API Response received');

      // Convert response to Map
      final Map<String, dynamic> response;
      if (rawResponse is Map<String, dynamic>) {
        response = rawResponse;
      } else {
        debugPrint('❌ Invalid response type');
        throw Exception('Invalid response format from server');
      }

      if (response['success'] == true && response['data'] != null) {
        try {
          final billingResponse = BillingDetailsResponse.fromJson(response);
          debugPrint('✅ Billing details fetched successfully');
          debugPrint(
              '📋 Invoice Number: ${billingResponse.data.invoiceNumber}');
          debugPrint('💰 Total Cost: ₹${billingResponse.data.totalCost}');
          debugPrint('⚡ Units: ${billingResponse.data.units} kWh');

          return billingResponse;
        } catch (parseError) {
          debugPrint('❌ Error parsing response: $parseError');
          throw Exception('Failed to parse billing details: $parseError');
        }
      } else {
        final errorMessage =
            response['message'] ?? 'Failed to fetch billing details';
        debugPrint('❌ API call failed: $errorMessage');
        throw Exception(errorMessage);
      }
    } catch (e) {
      debugPrint('❌ Error fetching billing details: $e');
      rethrow;
    }
  }

  /// Validate transaction ID format
  bool isValidTransactionId(String transactionId) {
    // Basic validation - transaction ID should not be empty and should be numeric
    if (transactionId.trim().isEmpty) {
      return false;
    }

    // Check if it's a valid number (transaction IDs are typically numeric)
    final numericRegex = RegExp(r'^\d+$');
    return numericRegex.hasMatch(transactionId.trim());
  }

  /// Get billing details with validation
  Future<BillingDetailsResponse?> getBillingDetailsWithValidation(
      String transactionId) async {
    // Validate transaction ID
    if (!isValidTransactionId(transactionId)) {
      throw Exception('Invalid transaction ID format');
    }

    return await getBillingDetails(transactionId);
  }

  /// Download invoice PDF for a specific transaction ID
  ///
  /// Endpoint: GET /api/v1/user/reports/trans-invoice-download?id={transactionId}
  /// Example: https://api2.eeil.online/api/v1/user/reports/trans-invoice-download?id=25264
  Future<Uint8List> downloadInvoicePDF(String transactionId) async {
    try {
      debugPrint(
          '📄 ===== INVOICE DOWNLOAD API CALL WITH TRANSACTION ID =====');
      debugPrint('📄 Transaction ID: "$transactionId"');
      debugPrint('📄 Transaction ID Source: SAME as billing API call');
      debugPrint('📄 Endpoint: ${ApiConfig.invoiceDownload}?id=$transactionId');

      // Validate transaction ID first
      if (!isValidTransactionId(transactionId)) {
        throw Exception('Invalid transaction ID format: $transactionId');
      }

      // Use ApiService for binary download (same pattern as billing API)
      final pdfBytes = await _apiService.downloadBinary(
        ApiConfig.invoiceDownload,
        queryParams: {'id': transactionId},
      );

      debugPrint('📄 PDF download successful');
      debugPrint('📄 PDF size: ${pdfBytes.length} bytes');

      // Validate PDF header
      if (pdfBytes.length > 4) {
        final pdfHeader = String.fromCharCodes(pdfBytes.take(4));
        if (pdfHeader == '%PDF') {
          debugPrint('✅ Valid PDF file downloaded');
          return pdfBytes;
        } else {
          debugPrint('❌ Invalid PDF file - header: $pdfHeader');
          throw Exception('Invalid PDF file received from server');
        }
      } else {
        debugPrint('❌ Empty or invalid PDF response');
        throw Exception('Empty PDF response received from server');
      }
    } catch (e) {
      debugPrint('❌ Error downloading invoice PDF: $e');
      rethrow;
    }
  }

  // REMOVED: Mock billing details method
  // This service now ONLY provides real API data - no mock data or fallbacks
}
