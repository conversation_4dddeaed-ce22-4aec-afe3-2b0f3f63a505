# Unified API Service for Ecoplug

This document explains how to use the new unified API service in the Ecoplug application.

## Overview

The unified API service provides a robust, centralized way to make API calls with:

- Proper error handling
- Retry mechanisms
- Rate limiting compliance
- Comprehensive logging
- Device information for debugging
- Type-safe request/response models
- Centralized configuration

## Key Components

1. **UnifiedApiService**: The main service for making API calls (`lib/services/unified_api_service.dart`)
2. **ApiResponseHandler**: Utility for handling API responses consistently (`lib/utils/api_response_handler.dart`)
3. **ErrorHandler**: Utility for converting API errors to user-friendly messages (`lib/utils/error_handler.dart`)
4. **ApiLoadingIndicator**: Reusable loading indicator for API calls (`lib/widgets/api_loading_indicator.dart`)
5. **ConnectivityService**: Service for monitoring network connectivity (`lib/services/connectivity_service.dart`)

## How to Use

### 1. Access the API service through the service locator

```dart
final apiService = ServiceLocator().apiService;
```

### 2. Make API calls

```dart
// GET request
final response = await apiService.get('/endpoint', queryParams: {'param': 'value'});

// POST request with authentication
final response = await apiService.post('/endpoint', data: {'key': 'value'});

// POST request without authentication (e.g., for login/OTP)
final response = await apiService.publicPost('/endpoint', {'key': 'value'});
```

### 3. Handle API responses with ApiResponseHandler

```dart
ApiResponseHandler.handle(
  apiCall: () => apiService.get('/endpoint'),
  onSuccess: (data) {
    // Handle success
    setState(() {
      _data = data;
      _isLoading = false;
    });
  },
  onError: (message, errorCode) {
    // Handle error
    setState(() {
      _errorMessage = message;
      _isLoading = false;
    });
  },
  onLoading: () {
    // Show loading indicator
    setState(() {
      _isLoading = true;
    });
  },
);
```

### 4. Use ApiLoadingIndicator for loading states

```dart
// Show loading indicator
if (_isLoading) {
  return ApiLoadingIndicator(
    message: 'Loading data...',
    showRetry: true,
    onRetry: _loadData,
  );
}

// Or use the overlay version
return ApiLoadingOverlay(
  isLoading: _isLoading,
  message: 'Loading data...',
  onRetry: _loadData,
  child: YourWidget(),
);
```

### 5. Check connectivity before making important API calls

```dart
final connectivityService = ServiceLocator().connectivityService;

if (await connectivityService.checkConnectionManually()) {
  // Make API call
} else {
  // Show offline message
}

// Or listen for connectivity changes
@override
void initState() {
  super.initState();
  connectivityService.connectionStatus.listen((hasConnection) {
    setState(() {
      _isOnline = hasConnection;
    });
  });
}
```

## Error Handling

The API service includes comprehensive error handling:

1. **Automatic retries** for network issues and server errors
2. **User-friendly error messages** through the ErrorHandler
3. **Detailed logging** for debugging
4. **Device information** included in error logs

## Example

Here's a complete example of using the unified API service:

```dart
import 'package:flutter/material.dart';
import '../services/service_locator.dart';
import '../utils/api_response_handler.dart';
import '../widgets/api_loading_indicator.dart';

class ExampleScreen extends StatefulWidget {
  @override
  _ExampleScreenState createState() => _ExampleScreenState();
}

class _ExampleScreenState extends State<ExampleScreen> {
  final _apiService = ServiceLocator().apiService;
  bool _isLoading = false;
  String? _errorMessage;
  Map<String, dynamic>? _data;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    ApiResponseHandler.handle(
      apiCall: () => _apiService.get('/endpoint'),
      onSuccess: (data) {
        setState(() {
          _data = data;
          _isLoading = false;
          _errorMessage = null;
        });
      },
      onError: (message, errorCode) {
        setState(() {
          _errorMessage = message;
          _isLoading = false;
        });
      },
      onLoading: () {
        setState(() {
          _isLoading = true;
          _errorMessage = null;
        });
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('API Example')),
      body: ApiLoadingOverlay(
        isLoading: _isLoading,
        message: 'Loading data...',
        onRetry: _loadData,
        child: _errorMessage != null
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error_outline, color: Colors.red, size: 48),
                    SizedBox(height: 16),
                    Text(_errorMessage!, style: TextStyle(color: Colors.red)),
                    SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadData,
                      child: Text('Retry'),
                    ),
                  ],
                ),
              )
            : _data != null
                ? ListView.builder(
                    itemCount: _data!.length,
                    itemBuilder: (context, index) {
                      final key = _data!.keys.elementAt(index);
                      return ListTile(
                        title: Text(key),
                        subtitle: Text(_data![key].toString()),
                      );
                    },
                  )
                : Center(child: Text('No data available')),
      ),
    );
  }
}
```

## Benefits

This implementation provides:

1. **Consistent error handling** across the app
2. **Improved user experience** with better error messages
3. **Reduced code duplication** with centralized API logic
4. **Better debugging** with comprehensive logging
5. **Improved reliability** with retry mechanisms
6. **Rate limiting compliance** to avoid API throttling
7. **Device information** for better error reporting

## Next Steps

After running `flutter pub get` to install the new dependencies, you can:

1. Uncomment the device info code in `UnifiedApiService._loadDeviceInfo()`
2. Start using the unified API service in your screens
3. Gradually replace existing API calls with the new implementation
