import '../../core/api/api_service.dart';

/// A provider class that gives access to the appropriate auth service
class AuthServiceProvider {
  // Singleton pattern
  static final AuthServiceProvider _instance = AuthServiceProvider._internal();
  factory AuthServiceProvider() => _instance;
  AuthServiceProvider._internal();
  
  // The original auth service
  final ApiService _originalService = ApiService();
  
  // The new robust auth service
  final ApiService _robustService = ApiService();
  
  // Flag to determine which service to use
  bool _useRobustService = true;
  
  /// Get the appropriate auth service
  dynamic getService() {
    return _useRobustService ? _robustService : _originalService;
  }
  
  /// Set whether to use the robust service
  void setUseRobustService(bool useRobust) {
    _useRobustService = useRobust;
  }
  
  /// Get whether the robust service is being used
  bool isUsingRobustService() {
    return _useRobustService;
  }
}
