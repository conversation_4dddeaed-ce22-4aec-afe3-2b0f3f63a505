import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

/// Model for storing route search history
class RouteSearchHistory {
  final String startLocationName;
  final String destinationName;
  final LatLng startCoordinates;
  final LatLng destinationCoordinates;
  final DateTime timestamp;

  const RouteSearchHistory({
    required this.startLocationName,
    required this.destinationName,
    required this.startCoordinates,
    required this.destinationCoordinates,
    required this.timestamp,
  });

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'startLocationName': startLocationName,
      'destinationName': destinationName,
      'startLat': startCoordinates.latitude,
      'startLng': startCoordinates.longitude,
      'destLat': destinationCoordinates.latitude,
      'destLng': destinationCoordinates.longitude,
      'timestamp': timestamp.millisecondsSinceEpoch,
    };
  }

  /// Create from JSON
  factory RouteSearchHistory.fromJson(Map<String, dynamic> json) {
    return RouteSearchHistory(
      startLocationName: json['startLocationName'] as String,
      destinationName: json['destinationName'] as String,
      startCoordinates: LatLng(
        json['startLat'] as double,
        json['startLng'] as double,
      ),
      destinationCoordinates: LatLng(
        json['destLat'] as double,
        json['destLng'] as double,
      ),
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp'] as int),
    );
  }

  /// Get formatted display text for the route
  String get displayText => 'From $startLocationName to $destinationName';

  /// Get short display text for compact UI
  String get shortDisplayText {
    final start = startLocationName.length > 20 
        ? '${startLocationName.substring(0, 20)}...' 
        : startLocationName;
    final dest = destinationName.length > 20 
        ? '${destinationName.substring(0, 20)}...' 
        : destinationName;
    return '$start → $dest';
  }

  @override
  String toString() {
    return 'RouteSearchHistory(start: $startLocationName, dest: $destinationName, time: $timestamp)';
  }
}

/// Service for managing route search history
class RouteSearchHistoryService {
  static const String _historyKey = 'route_search_history';
  static const int _maxHistoryItems = 3; // Store last 3 searches

  /// Save a new route search to history
  Future<void> saveRouteSearch({
    required String startLocationName,
    required String destinationName,
    required LatLng startCoordinates,
    required LatLng destinationCoordinates,
  }) async {
    try {
      debugPrint('🔍 HISTORY: Saving route search to history');
      debugPrint('🔍 HISTORY: From "$startLocationName" to "$destinationName"');

      final newSearch = RouteSearchHistory(
        startLocationName: startLocationName,
        destinationName: destinationName,
        startCoordinates: startCoordinates,
        destinationCoordinates: destinationCoordinates,
        timestamp: DateTime.now(),
      );

      // Get existing history
      final existingHistory = await getRouteSearchHistory();

      // Check if this exact route already exists (avoid duplicates)
      final isDuplicate = existingHistory.any((history) =>
          history.startLocationName == startLocationName &&
          history.destinationName == destinationName);

      if (isDuplicate) {
        debugPrint('🔍 HISTORY: Route already exists in history, skipping save');
        return;
      }

      // Add new search to the beginning of the list
      final updatedHistory = [newSearch, ...existingHistory];

      // Keep only the most recent searches (limit to _maxHistoryItems)
      final limitedHistory = updatedHistory.take(_maxHistoryItems).toList();

      // Save to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final historyJson = limitedHistory.map((h) => h.toJson()).toList();
      await prefs.setString(_historyKey, jsonEncode(historyJson));

      debugPrint('✅ HISTORY: Route search saved successfully');
      debugPrint('✅ HISTORY: Total history items: ${limitedHistory.length}');
    } catch (e) {
      debugPrint('❌ HISTORY: Error saving route search: $e');
    }
  }

  /// Get all route search history
  Future<List<RouteSearchHistory>> getRouteSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyString = prefs.getString(_historyKey);

      if (historyString == null || historyString.isEmpty) {
        debugPrint('🔍 HISTORY: No search history found');
        return [];
      }

      final historyJson = jsonDecode(historyString) as List<dynamic>;
      final history = historyJson
          .map((json) => RouteSearchHistory.fromJson(json as Map<String, dynamic>))
          .toList();

      debugPrint('🔍 HISTORY: Retrieved ${history.length} search history items');
      return history;
    } catch (e) {
      debugPrint('❌ HISTORY: Error retrieving route search history: $e');
      return [];
    }
  }

  /// Clear all search history
  Future<void> clearHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_historyKey);
      debugPrint('🗑️ HISTORY: Search history cleared');
    } catch (e) {
      debugPrint('❌ HISTORY: Error clearing search history: $e');
    }
  }

  /// Remove a specific route from history
  Future<void> removeRouteFromHistory(RouteSearchHistory routeToRemove) async {
    try {
      final existingHistory = await getRouteSearchHistory();
      final updatedHistory = existingHistory.where((history) =>
          !(history.startLocationName == routeToRemove.startLocationName &&
            history.destinationName == routeToRemove.destinationName &&
            history.timestamp == routeToRemove.timestamp)).toList();

      final prefs = await SharedPreferences.getInstance();
      final historyJson = updatedHistory.map((h) => h.toJson()).toList();
      await prefs.setString(_historyKey, jsonEncode(historyJson));

      debugPrint('🗑️ HISTORY: Route removed from history');
    } catch (e) {
      debugPrint('❌ HISTORY: Error removing route from history: $e');
    }
  }

  /// Check if history has any items
  Future<bool> hasHistory() async {
    final history = await getRouteSearchHistory();
    return history.isNotEmpty;
  }
}
