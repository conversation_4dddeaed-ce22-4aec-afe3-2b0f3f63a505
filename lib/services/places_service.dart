import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:geolocator/geolocator.dart';

import '../config/google_maps_config.dart';
import '../models/place_suggestion.dart';
import '../services/location_service.dart';

/// Service for Google Places API integration
class PlacesService {
  static const String _baseUrl = 'https://maps.googleapis.com/maps/api';
  final LocationService _locationService = LocationService();

  /// Search for place suggestions using Google Places Autocomplete API
  Future<List<PlaceSuggestion>> searchPlaces(String query) async {
    if (query.trim().isEmpty) {
      return [];
    }

    try {
      // Get current location for bias (optional)
      Position? currentPosition = await _locationService.getCurrentLocation();
      
      // Build the autocomplete request URL
      final url = Uri.parse('$_baseUrl/place/autocomplete/json').replace(
        queryParameters: {
          'input': query,
          'key': GoogleMapsConfig.apiKey,
          'types': 'geocode', // Focus on addresses and places
          'components': 'country:in', // Restrict to India
          if (currentPosition != null) ...{
            'location': '${currentPosition.latitude},${currentPosition.longitude}',
            'radius': '50000', // 50km radius for location bias
          },
        },
      );

      debugPrint('🔍 PLACES SEARCH: Searching for "$query"');
      debugPrint('🌐 PLACES API: $url');

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['status'] == 'OK') {
          final predictions = data['predictions'] as List;
          final suggestions = predictions
              .map((prediction) => PlaceSuggestion.fromPrediction(prediction))
              .toList();

          debugPrint('📍 PLACES RESULTS: Found ${suggestions.length} suggestions');
          return suggestions;
        } else {
          debugPrint('❌ PLACES ERROR: ${data['status']} - ${data['error_message'] ?? 'Unknown error'}');
          return [];
        }
      } else {
        debugPrint('❌ PLACES HTTP ERROR: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      debugPrint('❌ PLACES EXCEPTION: $e');
      return [];
    }
  }

  /// Get place details including coordinates for a place ID
  Future<PlaceSuggestion?> getPlaceDetails(String placeId) async {
    try {
      final url = Uri.parse('$_baseUrl/place/details/json').replace(
        queryParameters: {
          'place_id': placeId,
          'key': GoogleMapsConfig.apiKey,
          'fields': 'geometry,formatted_address,name',
        },
      );

      debugPrint('📍 PLACE DETAILS: Getting details for $placeId');

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['status'] == 'OK') {
          final result = data['result'];
          final geometry = result['geometry'];
          final location = geometry['location'];
          
          final coordinates = LatLng(
            location['lat'].toDouble(),
            location['lng'].toDouble(),
          );
          
          final formattedAddress = result['formatted_address'] ?? '';
          
          debugPrint('✅ PLACE DETAILS: Got coordinates $coordinates');
          
          return PlaceSuggestion(
            placeId: placeId,
            description: formattedAddress,
            mainText: result['name'] ?? '',
            secondaryText: formattedAddress,
            coordinates: coordinates,
            formattedAddress: formattedAddress,
          );
        } else {
          debugPrint('❌ PLACE DETAILS ERROR: ${data['status']}');
          return null;
        }
      } else {
        debugPrint('❌ PLACE DETAILS HTTP ERROR: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ PLACE DETAILS EXCEPTION: $e');
      return null;
    }
  }

  /// Get current location as a place suggestion
  Future<PlaceSuggestion?> getCurrentLocationSuggestion() async {
    try {
      final position = await _locationService.getCurrentLocation();
      if (position != null) {
        final coordinates = LatLng(position.latitude, position.longitude);
        return PlaceSuggestion.currentLocation(coordinates);
      }
      return null;
    } catch (e) {
      debugPrint('❌ CURRENT LOCATION ERROR: $e');
      return null;
    }
  }

  /// Reverse geocode coordinates to get address
  Future<String?> reverseGeocode(LatLng coordinates) async {
    try {
      final url = Uri.parse('$_baseUrl/geocode/json').replace(
        queryParameters: {
          'latlng': '${coordinates.latitude},${coordinates.longitude}',
          'key': GoogleMapsConfig.apiKey,
        },
      );

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['status'] == 'OK' && data['results'].isNotEmpty) {
          return data['results'][0]['formatted_address'];
        }
      }
      return null;
    } catch (e) {
      debugPrint('❌ REVERSE GEOCODE ERROR: $e');
      return null;
    }
  }
}
