import 'package:flutter/foundation.dart';

/// Global service to store and retrieve authentic charging parameters
/// This ensures that authentic API data flows through the entire charging process
class ChargingParametersService {
  // Singleton pattern
  static final ChargingParametersService _instance =
      ChargingParametersService._internal();
  factory ChargingParametersService() => _instance;
  ChargingParametersService._internal();

  // Storage for authentic charging parameters
  Map<String, dynamic>? _currentChargingParams;

  // Storage for transaction ID from Step 1 (startChargingSession)
  String? _transactionId;

  /// Store authentic charging parameters from station detail API
  void storeChargingParameters(Map<String, dynamic> params) {
    _currentChargingParams = Map<String, dynamic>.from(params);

    debugPrint('🔌 STORING AUTHENTIC CHARGING PARAMETERS:');
    debugPrint('  Charging Value: ${params['chargingValue']}');
    debugPrint('  Charge Type: ${params['chargeType']}');
    debugPrint('  Price Per Unit: ₹${params['pricePerUnit']}');
    debugPrint('  Max Power: ${params['maxPower']}kW');
    debugPrint('  Connector Type: ${params['connectorType']}');
    debugPrint('  EVSE UID: ${params['evsesUid']}');
  }

  /// Get stored authentic charging parameters
  Map<String, dynamic>? getChargingParameters() {
    return _currentChargingParams != null
        ? Map<String, dynamic>.from(_currentChargingParams!)
        : null;
  }

  /// Get specific charging value (kWh or amount)
  double getChargingValue() {
    return _currentChargingParams?['chargingValue']?.toDouble() ?? 20.0;
  }

  /// Get charge type (units or amount)
  String getChargeType() {
    return _currentChargingParams?['chargeType']?.toString() ?? 'units';
  }

  /// Get authentic price per unit from API
  double getPricePerUnit() {
    return _currentChargingParams?['pricePerUnit']?.toDouble() ?? 0.0;
  }

  /// Get authentic max power from API
  double getMaxPower() {
    return _currentChargingParams?['maxPower']?.toDouble() ?? 0.0;
  }

  /// Get connector type
  String getConnectorType() {
    return _currentChargingParams?['connectorType']?.toString() ?? '';
  }

  /// Get EVSE UID
  String getEvsesUid() {
    return _currentChargingParams?['evsesUid']?.toString() ?? '';
  }

  /// Get instant charging flag
  bool getInstantCharging() {
    return _currentChargingParams?['instantCharging'] == true;
  }

  /// Get wallet balance
  double getWalletBalance() {
    return _currentChargingParams?['walletBalance']?.toDouble() ?? 0.0;
  }

  /// Check if authentic parameters are available
  bool hasAuthenticParameters() {
    return _currentChargingParams != null && _currentChargingParams!.isNotEmpty;
  }

  /// 🚨 NEW: Store transaction ID from Step 1 (startChargingSession)
  void storeTransactionId(String transactionId) {
    _transactionId = transactionId;
    debugPrint('🔒 STORED TRANSACTION ID: $_transactionId');
  }

  /// 🚨 NEW: Get stored transaction ID for Step 2 (session verification)
  String? getTransactionId() {
    debugPrint('🔍 RETRIEVING TRANSACTION ID: $_transactionId');
    return _transactionId;
  }

  /// Clear stored parameters (call after charging session ends)
  void clearParameters() {
    debugPrint('🧹 CLEARING STORED CHARGING PARAMETERS');
    _currentChargingParams = null;
    _transactionId = null; // Also clear transaction ID
  }

  /// Get formatted charging summary for display
  String getChargingSummary() {
    if (!hasAuthenticParameters()) {
      return 'No charging parameters available';
    }

    final value = getChargingValue();
    final type = getChargeType();
    final price = getPricePerUnit();
    final power = getMaxPower();

    if (type == 'units') {
      return '${value.toStringAsFixed(1)} kWh at ₹${price.toStringAsFixed(2)}/kWh (${power.toStringAsFixed(0)}kW)';
    } else {
      return '₹${value.toStringAsFixed(0)} charging session (${power.toStringAsFixed(0)}kW)';
    }
  }

  /// Validate that all required parameters are present
  bool validateParameters() {
    if (!hasAuthenticParameters()) {
      debugPrint('❌ No charging parameters stored');
      return false;
    }

    final params = _currentChargingParams!;

    // Check required fields
    if (params['chargingValue'] == null || params['chargingValue'] <= 0) {
      debugPrint('❌ Invalid charging value: ${params['chargingValue']}');
      return false;
    }

    if (params['chargeType'] == null ||
        params['chargeType'].toString().isEmpty) {
      debugPrint('❌ Invalid charge type: ${params['chargeType']}');
      return false;
    }

    if (params['pricePerUnit'] == null || params['pricePerUnit'] < 0) {
      debugPrint('❌ Invalid price per unit: ${params['pricePerUnit']}');
      return false;
    }

    if (params['evsesUid'] == null || params['evsesUid'].toString().isEmpty) {
      debugPrint('❌ Invalid EVSE UID: ${params['evsesUid']}');
      return false;
    }

    debugPrint('✅ All charging parameters validated successfully');
    return true;
  }

  /// Create payload for charging API with authentic data
  Map<String, dynamic> createChargingPayload() {
    if (!hasAuthenticParameters()) {
      throw Exception(
          'No authentic charging parameters available for payload creation');
    }

    final params = _currentChargingParams!;

    // Get instant charging flag from stored parameters
    final bool isInstantCharging = params['instantCharging'] == true;

    return {
      'charge_type': params['chargeType'],
      'charging_value': params['chargingValue'],
      'instant_charging': isInstantCharging ? 1 : 0, // FIXED: Use 1/0 format for API
      'connector_id': params['connectorId'] ?? '1',
      // Add any additional fields from authentic API data
      'max_power': params['maxPower'],
      'price_per_unit': params['pricePerUnit'],
      'connector_type': params['connectorType'],
    };
  }

  /// Debug method to print all stored parameters
  void debugPrintParameters() {
    if (!hasAuthenticParameters()) {
      debugPrint('🔍 No charging parameters stored');
      return;
    }

    debugPrint('🔍 CURRENT STORED CHARGING PARAMETERS:');
    _currentChargingParams!.forEach((key, value) {
      debugPrint('  $key: $value');
    });
  }
}
