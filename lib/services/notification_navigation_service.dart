import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

/// Service to handle navigation from notification taps
/// Guarantees proper navigation to charging session screen from any app state
class NotificationNavigationService {
  static final NotificationNavigationService _instance =
      NotificationNavigationService._internal();
  factory NotificationNavigationService() => _instance;
  NotificationNavigationService._internal();

  static const String _navigationIntentKey = 'notification_navigation_intent';
  static const String _chargingDataKey = 'charging_session_data';

  GlobalKey<NavigatorState>? _navigatorKey;

  /// Initialize the navigation service with navigator key
  void initialize(GlobalKey<NavigatorState> navigatorKey) {
    _navigatorKey = navigatorKey;
    debugPrint(
        '🔔 NotificationNavigationService initialized with navigator key');
  }

  /// Store navigation intent for later processing
  Future<void> storeNavigationIntent(String action, String? payload) async {
    try {
      debugPrint('🔔 ===== STORING NAVIGATION INTENT =====');
      debugPrint('🔔 Action: $action');
      debugPrint('🔔 Payload: $payload');

      final prefs = await SharedPreferences.getInstance();

      final intent = {
        'action': action,
        'payload': payload,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      await prefs.setString(_navigationIntentKey, jsonEncode(intent));
      debugPrint('✅ Navigation intent stored successfully');
    } catch (e) {
      debugPrint('❌ Error storing navigation intent: $e');
    }
  }

  /// Check and process pending navigation intents
  Future<void> processPendingNavigationIntents() async {
    try {
      debugPrint('🔔 ===== CHECKING PENDING NAVIGATION INTENTS =====');

      final prefs = await SharedPreferences.getInstance();
      final intentString = prefs.getString(_navigationIntentKey);

      if (intentString == null) {
        debugPrint('🔔 No pending navigation intents');
        return;
      }

      final intent = jsonDecode(intentString) as Map<String, dynamic>;
      final action = intent['action'] as String?;
      final payload = intent['payload'] as String?;
      final timestamp = intent['timestamp'] as int?;

      debugPrint('🔔 Found pending intent: $action with payload: $payload');
      debugPrint(
          '🔔 Intent timestamp: ${DateTime.fromMillisecondsSinceEpoch(timestamp ?? 0)}');

      // Check if intent is not too old (within 5 minutes)
      if (timestamp != null) {
        final intentAge = DateTime.now().millisecondsSinceEpoch - timestamp;
        if (intentAge > 300000) {
          // 5 minutes
          debugPrint('🔔 Intent too old, ignoring');
          await clearNavigationIntent();
          return;
        }
      }

      // Process the intent
      await _processNavigationIntent(action, payload);

      // Clear the processed intent
      await clearNavigationIntent();
    } catch (e) {
      debugPrint('❌ Error processing navigation intents: $e');
    }
  }

  /// Process navigation intent
  Future<void> _processNavigationIntent(String? action, String? payload) async {
    if (_navigatorKey?.currentContext == null) {
      debugPrint('❌ Navigator context not available');
      return;
    }

    final context = _navigatorKey!.currentContext!;

    switch (action) {
      case 'charging_session':
        await _navigateToChargingSession(context, payload);
        break;
      case 'stop_charging':
        await _handleStopCharging(context);
        break;
      case 'station_details':
        await _navigateToStationDetails(context, payload);
        break;
      case 'wallet':
        await _navigateToWallet(context);
        break;
      case 'trip':
        await _navigateToTrip(context);
        break;
      case 'dashboard':
        await _navigateToDashboard(context);
        break;
      default:
        debugPrint('🔔 Unknown navigation action: $action');
        // Default to dashboard for unknown actions
        await _navigateToDashboard(context);
    }
  }

  /// Navigate to charging session screen
  Future<void> _navigateToChargingSession(
      BuildContext context, String? payload) async {
    try {
      debugPrint('🔔 ===== NAVIGATING TO CHARGING SESSION SCREEN =====');
      debugPrint('🔔 Payload: $payload');

      // Parse payload to extract charging session data
      Map<String, dynamic>? chargingData;
      if (payload != null && payload.isNotEmpty) {
        try {
          // Try to parse as JSON first (enhanced payload)
          if (payload.startsWith('{')) {
            chargingData = jsonDecode(payload) as Map<String, dynamic>;
            debugPrint('🔔 Parsed JSON payload: $chargingData');
          } else {
            // Fallback to legacy parsing
            final parts = payload.split('_');
            if (parts.length >= 3) {
              final percentage = double.tryParse(parts[1]) ?? 0.0;
              final isActive = parts[2] == 'active';

              chargingData = {
                'charge_percentage': percentage,
                'is_charging': isActive,
                'from_notification': true,
              };
            }
          }
        } catch (e) {
          debugPrint('🔔 Error parsing payload: $e');
          // Create fallback data
          chargingData = {
            'from_notification': true,
            'from_fcm': true,
            'is_charging': true,
          };
        }
      } else {
        // Create default charging data when no payload
        chargingData = {
          'from_notification': true,
          'is_charging': true,
        };
      }

      debugPrint('🔔 Final charging data: $chargingData');

      // Navigate to charging session screen with enhanced error handling
      try {
        await Navigator.of(context).pushNamedAndRemoveUntil(
          '/charging_session',
          (route) => route.isFirst,
          arguments: chargingData,
        );
        debugPrint('✅ Successfully navigated to charging session screen');
      } catch (navError) {
        debugPrint('❌ Navigation error, trying alternative route: $navError');

        // Fallback navigation attempt
        try {
          await Navigator.of(context).pushNamed(
            '/charging_session',
            arguments: chargingData,
          );
          debugPrint('✅ Fallback navigation successful');
        } catch (fallbackError) {
          debugPrint('❌ Fallback navigation failed: $fallbackError');

          // Last resort - navigate to dashboard with horizontal cards
          await Navigator.of(context).pushNamedAndRemoveUntil(
            '/dashboard',
            (route) => false,
            arguments: {'from_notification': true, 'navigation_failed': true},
          );
        }
      }
    } catch (e) {
      debugPrint('❌ Error navigating to charging session: $e');
      debugPrint('❌ Stack trace: ${StackTrace.current}');
    }
  }

  /// Handle stop charging action
  Future<void> _handleStopCharging(BuildContext context) async {
    try {
      debugPrint('🔔 ===== HANDLING STOP CHARGING ACTION =====');

      // Show confirmation dialog
      final shouldStop = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Stop Charging'),
          content:
              const Text('Are you sure you want to stop the charging session?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Stop'),
            ),
          ],
        ),
      );

      if (shouldStop == true) {
        debugPrint('🔔 User confirmed stop charging');
        // TODO: Implement actual stop charging logic
        // This would typically call the charging service to stop the session
      } else {
        debugPrint('🔔 User cancelled stop charging');
      }
    } catch (e) {
      debugPrint('❌ Error handling stop charging: $e');
    }
  }

  /// Navigate to station details screen
  Future<void> _navigateToStationDetails(
      BuildContext context, String? stationId) async {
    try {
      debugPrint('🔔 ===== NAVIGATING TO STATION DETAILS =====');
      debugPrint('🔔 Station ID: $stationId');

      if (stationId != null) {
        await Navigator.of(context).pushNamedAndRemoveUntil(
          '/station_details',
          (route) => route.isFirst,
          arguments: {'station_id': stationId, 'from_notification': true},
        );
      } else {
        // Navigate to station list if no specific station ID
        await Navigator.of(context).pushNamedAndRemoveUntil(
          '/station_list',
          (route) => route.isFirst,
        );
      }

      debugPrint('✅ Successfully navigated to station details');
    } catch (e) {
      debugPrint('❌ Error navigating to station details: $e');
    }
  }

  /// Navigate to wallet screen
  Future<void> _navigateToWallet(BuildContext context) async {
    try {
      debugPrint('🔔 ===== NAVIGATING TO WALLET =====');

      await Navigator.of(context).pushNamedAndRemoveUntil(
        '/wallet',
        (route) => route.isFirst,
        arguments: {'from_notification': true},
      );

      debugPrint('✅ Successfully navigated to wallet');
    } catch (e) {
      debugPrint('❌ Error navigating to wallet: $e');
    }
  }

  /// Navigate to trip page
  Future<void> _navigateToTrip(BuildContext context) async {
    try {
      debugPrint('🔔 ===== NAVIGATING TO TRIP PAGE =====');

      await Navigator.of(context).pushNamedAndRemoveUntil(
        '/trip',
        (route) => route.isFirst,
        arguments: {'from_notification': true},
      );

      debugPrint('✅ Successfully navigated to trip page');
    } catch (e) {
      debugPrint('❌ Error navigating to trip page: $e');
    }
  }

  /// Navigate to dashboard (home screen with horizontal cards)
  Future<void> _navigateToDashboard(BuildContext context) async {
    try {
      debugPrint('🔔 ===== NAVIGATING TO DASHBOARD (HORIZONTAL CARDS) =====');

      await Navigator.of(context).pushNamedAndRemoveUntil(
        '/dashboard', // Navigate to main dashboard with horizontal cards as home tab
        (route) => false, // Clear all routes
        arguments: {'from_notification': true},
      );

      debugPrint('✅ Successfully navigated to horizontal cards dashboard');
    } catch (e) {
      debugPrint('❌ Error navigating to dashboard: $e');
    }
  }

  /// Clear navigation intent
  Future<void> clearNavigationIntent() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_navigationIntentKey);
      debugPrint('🔔 Navigation intent cleared');
    } catch (e) {
      debugPrint('❌ Error clearing navigation intent: $e');
    }
  }

  /// Store current charging session data for navigation
  Future<void> storeChargingSessionData(Map<String, dynamic> data) async {
    try {
      debugPrint('🔔 Storing charging session data for navigation');

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_chargingDataKey, jsonEncode(data));

      debugPrint('✅ Charging session data stored');
    } catch (e) {
      debugPrint('❌ Error storing charging session data: $e');
    }
  }

  /// Get stored charging session data
  Future<Map<String, dynamic>?> getStoredChargingSessionData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dataString = prefs.getString(_chargingDataKey);

      if (dataString != null) {
        final data = jsonDecode(dataString) as Map<String, dynamic>;
        debugPrint('🔔 Retrieved stored charging session data');
        return data;
      }

      return null;
    } catch (e) {
      debugPrint('❌ Error getting stored charging session data: $e');
      return null;
    }
  }

  /// Clear stored charging session data
  Future<void> clearChargingSessionData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_chargingDataKey);
      debugPrint('🔔 Charging session data cleared');
    } catch (e) {
      debugPrint('❌ Error clearing charging session data: $e');
    }
  }
}
