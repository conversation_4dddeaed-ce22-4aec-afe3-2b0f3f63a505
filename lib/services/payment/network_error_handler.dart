import 'package:flutter/foundation.dart';
import 'dart:async';
import 'dart:io';
import '../connectivity_service.dart';

/// Network and connectivity error handler for payment gateways
/// Provides robust handling for network timeouts, connectivity issues, and API failures
class NetworkErrorHandler {
  static const String _logPrefix = '🌐 NETWORK_HANDLER:';
  static final ConnectivityService _connectivityService = ConnectivityService();

  /// Check internet connectivity using enhanced connectivity service
  static Future<bool> hasInternetConnection() async {
    try {
      debugPrint('$_logPrefix Checking internet connectivity...');

      // Use enhanced connectivity verification
      final hasConnection =
          await _connectivityService.verifyInternetConnectivity();
      debugPrint(
          '$_logPrefix Enhanced connectivity check result: $hasConnection');

      return hasConnection;
    } catch (e) {
      debugPrint('$_logPrefix Internet connectivity check failed: $e');
      return false;
    }
  }

  /// Handle network timeout with retry mechanism
  static Future<T> handleWithRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 2),
    Duration timeout = const Duration(seconds: 30),
    String operationName = 'Network Operation',
  }) async {
    debugPrint('$_logPrefix Starting $operationName with retry mechanism');
    debugPrint(
        '$_logPrefix Max retries: $maxRetries, Timeout: ${timeout.inSeconds}s');

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        debugPrint(
            '$_logPrefix Attempt $attempt/$maxRetries for $operationName');

        // Check connectivity before each attempt
        if (!(await hasInternetConnection())) {
          throw NetworkException(
            'No internet connection available',
            type: NetworkErrorType.noConnection,
            isRetryable: true,
          );
        }

        final result = await operation().timeout(timeout);
        debugPrint('$_logPrefix $operationName succeeded on attempt $attempt');
        return result;
      } on TimeoutException catch (e) {
        debugPrint('$_logPrefix Timeout on attempt $attempt: $e');

        if (attempt == maxRetries) {
          throw NetworkException(
            'Operation timed out after $maxRetries attempts',
            type: NetworkErrorType.timeout,
            originalError: e,
            isRetryable: false,
          );
        }
      } on SocketException catch (e) {
        debugPrint('$_logPrefix Socket error on attempt $attempt: $e');

        if (attempt == maxRetries) {
          throw NetworkException(
            'Network connection failed after $maxRetries attempts',
            type: NetworkErrorType.connectionFailed,
            originalError: e,
            isRetryable: false,
          );
        }
      } on NetworkException catch (e) {
        debugPrint('$_logPrefix Network exception on attempt $attempt: $e');

        if (!e.isRetryable || attempt == maxRetries) {
          rethrow;
        }
      } catch (e) {
        debugPrint('$_logPrefix Unexpected error on attempt $attempt: $e');

        if (attempt == maxRetries) {
          throw NetworkException(
            'Unexpected network error: ${e.toString()}',
            type: NetworkErrorType.unknown,
            originalError: e,
            isRetryable: false,
          );
        }
      }

      if (attempt < maxRetries) {
        debugPrint(
            '$_logPrefix Waiting ${retryDelay.inSeconds}s before retry...');
        await Future.delayed(retryDelay);
      }
    }

    throw NetworkException(
      'All retry attempts failed for $operationName',
      type: NetworkErrorType.maxRetriesExceeded,
      isRetryable: false,
    );
  }

  /// Handle API call with comprehensive error handling
  static Future<Map<String, dynamic>> handleApiCall(
    Future<Map<String, dynamic>> Function() apiCall, {
    required String apiName,
    int maxRetries = 3,
    Duration timeout = const Duration(seconds: 30),
  }) async {
    debugPrint('$_logPrefix Handling API call: $apiName');

    return await handleWithRetry<Map<String, dynamic>>(
      apiCall,
      maxRetries: maxRetries,
      timeout: timeout,
      operationName: 'API Call: $apiName',
    );
  }

  /// Get user-friendly error message for network errors
  static String getUserFriendlyMessage(NetworkException error) {
    switch (error.type) {
      case NetworkErrorType.noConnection:
        return 'No internet connection. Please check your network settings and try again.';
      case NetworkErrorType.timeout:
        return 'Request timed out. Please check your connection and try again.';
      case NetworkErrorType.connectionFailed:
        return 'Failed to connect to payment service. Please try again.';
      case NetworkErrorType.serverError:
        return 'Payment service is temporarily unavailable. Please try again later.';
      case NetworkErrorType.maxRetriesExceeded:
        return 'Multiple connection attempts failed. Please check your internet connection.';
      case NetworkErrorType.unknown:
      default:
        return 'Network error occurred. Please check your connection and try again.';
    }
  }

  /// Check if error is network-related
  static bool isNetworkError(dynamic error) {
    if (error is NetworkException) return true;
    if (error is SocketException) return true;
    if (error is TimeoutException) return true;

    final errorString = error.toString().toLowerCase();
    return errorString.contains('network') ||
        errorString.contains('connection') ||
        errorString.contains('timeout') ||
        errorString.contains('socket') ||
        errorString.contains('internet');
  }

  /// Create network exception from generic error
  static NetworkException createFromError(dynamic error) {
    if (error is NetworkException) return error;

    if (error is SocketException) {
      return NetworkException(
        'Network connection failed',
        type: NetworkErrorType.connectionFailed,
        originalError: error,
        isRetryable: true,
      );
    }

    if (error is TimeoutException) {
      return NetworkException(
        'Request timed out',
        type: NetworkErrorType.timeout,
        originalError: error,
        isRetryable: true,
      );
    }

    final errorString = error.toString().toLowerCase();

    if (errorString.contains('no internet') ||
        errorString.contains('no connection')) {
      return NetworkException(
        'No internet connection',
        type: NetworkErrorType.noConnection,
        originalError: error,
        isRetryable: true,
      );
    }

    return NetworkException(
      'Network error: ${error.toString()}',
      type: NetworkErrorType.unknown,
      originalError: error,
      isRetryable: true,
    );
  }

  /// Monitor connectivity changes using enhanced connectivity service
  static Stream<ConnectionStatus> get connectivityStream =>
      _connectivityService.connectionStatus;
}

/// Network error types
enum NetworkErrorType {
  noConnection,
  timeout,
  connectionFailed,
  serverError,
  maxRetriesExceeded,
  unknown,
}

/// Custom network exception
class NetworkException implements Exception {
  final String message;
  final NetworkErrorType type;
  final dynamic originalError;
  final bool isRetryable;
  final DateTime timestamp;

  NetworkException(
    this.message, {
    required this.type,
    this.originalError,
    this.isRetryable = true,
  }) : timestamp = DateTime.now();

  @override
  String toString() {
    return 'NetworkException: $message (Type: $type, Retryable: $isRetryable)';
  }
}

/// Network status monitor using enhanced connectivity service
class NetworkStatusMonitor {
  static const String _logPrefix = '📡 NETWORK_MONITOR:';
  static StreamSubscription<ConnectionStatus>? _subscription;
  static bool _isOnline = true;
  static final List<Function(bool)> _listeners = [];

  /// Start monitoring network status
  static void startMonitoring() {
    debugPrint('$_logPrefix Starting network monitoring...');

    _subscription?.cancel();
    _subscription = NetworkErrorHandler.connectivityStream.listen(
      (ConnectionStatus status) {
        final wasOnline = _isOnline;
        _isOnline = status == ConnectionStatus.connected;

        debugPrint(
            '$_logPrefix Connectivity changed: $status (Online: $_isOnline)');

        if (wasOnline != _isOnline) {
          _notifyListeners(_isOnline);
        }
      },
      onError: (error) {
        debugPrint('$_logPrefix Connectivity monitoring error: $error');
      },
    );
  }

  /// Stop monitoring network status
  static void stopMonitoring() {
    debugPrint('$_logPrefix Stopping network monitoring...');
    _subscription?.cancel();
    _subscription = null;
  }

  /// Add network status listener
  static void addListener(Function(bool isOnline) listener) {
    _listeners.add(listener);
  }

  /// Remove network status listener
  static void removeListener(Function(bool isOnline) listener) {
    _listeners.remove(listener);
  }

  /// Notify all listeners of network status change
  static void _notifyListeners(bool isOnline) {
    debugPrint(
        '$_logPrefix Notifying ${_listeners.length} listeners of network status: $isOnline');

    for (final listener in _listeners) {
      try {
        listener(isOnline);
      } catch (e) {
        debugPrint('$_logPrefix Error notifying listener: $e');
      }
    }
  }

  /// Get current network status
  static bool get isOnline => _isOnline;
}
