import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'payu_service.dart';

/// PayU Payment Lifecycle Manager
/// Handles persistent payment state and app lifecycle for PayU payments
class PayULifecycleManager {
  static PayULifecycleManager? _instance;
  static const String _pendingPaymentKey = 'payu_pending_payment';
  static const String _transactionDataKey = 'payu_transaction_data';
  static const String _paymentStartTimeKey = 'payu_payment_start_time';

  /// Get singleton instance
  static PayULifecycleManager get instance {
    _instance ??= PayULifecycleManager._();
    return _instance!;
  }

  PayULifecycleManager._();

  /// Store pending PayU payment data
  Future<void> storePendingPayment({
    required String transactionId,
    required String merchantTxnId,
    required double amount,
    required Map<String, dynamic> paymentParams,
  }) async {
    try {
      debugPrint('💾 PAYU LIFECYCLE: Storing pending payment data');
      debugPrint('💾 PAYU LIFECYCLE: Transaction ID: $transactionId');
      debugPrint('💾 PAYU LIFECYCLE: Amount: ₹$amount');

      final prefs = await SharedPreferences.getInstance();

      final paymentData = {
        'transactionId': transactionId,
        'merchantTxnId': merchantTxnId,
        'amount': amount,
        'paymentParams': paymentParams,
        'startTime': DateTime.now().toIso8601String(),
      };

      await prefs.setBool(_pendingPaymentKey, true);
      await prefs.setString(_transactionDataKey, jsonEncode(paymentData));
      await prefs.setInt(_paymentStartTimeKey, DateTime.now().millisecondsSinceEpoch);

      debugPrint('✅ PAYU LIFECYCLE: Pending payment data stored successfully');
    } catch (e) {
      debugPrint('❌ PAYU LIFECYCLE: Error storing pending payment: $e');
    }
  }

  /// Check if there's a pending PayU payment
  Future<bool> hasPendingPayment() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hasPending = prefs.getBool(_pendingPaymentKey) ?? false;

      if (hasPending) {
        // Check if payment is not too old (max 10 minutes)
        final startTime = prefs.getInt(_paymentStartTimeKey) ?? 0;
        final now = DateTime.now().millisecondsSinceEpoch;
        final elapsedMinutes = (now - startTime) / (1000 * 60);

        if (elapsedMinutes > 10) {
          debugPrint('⏰ PAYU LIFECYCLE: Pending payment expired (${elapsedMinutes.toStringAsFixed(1)} minutes)');
          await clearPendingPayment();
          return false;
        }

        debugPrint('🔍 PAYU LIFECYCLE: Found pending payment (${elapsedMinutes.toStringAsFixed(1)} minutes ago)');
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('❌ PAYU LIFECYCLE: Error checking pending payment: $e');
      return false;
    }
  }

  /// Get pending payment data
  Future<Map<String, dynamic>?> getPendingPaymentData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dataString = prefs.getString(_transactionDataKey);

      if (dataString != null) {
        final data = jsonDecode(dataString) as Map<String, dynamic>;
        debugPrint('📋 PAYU LIFECYCLE: Retrieved pending payment data');
        debugPrint('📋 PAYU LIFECYCLE: Transaction ID: ${data['transactionId']}');
        return data;
      }

      return null;
    } catch (e) {
      debugPrint('❌ PAYU LIFECYCLE: Error getting pending payment data: $e');
      return null;
    }
  }

  /// Clear pending payment data
  Future<void> clearPendingPayment() async {
    try {
      debugPrint('🧹 PAYU LIFECYCLE: Clearing pending payment data');

      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_pendingPaymentKey);
      await prefs.remove(_transactionDataKey);
      await prefs.remove(_paymentStartTimeKey);

      debugPrint('✅ PAYU LIFECYCLE: Pending payment data cleared');
    } catch (e) {
      debugPrint('❌ PAYU LIFECYCLE: Error clearing pending payment: $e');
    }
  }

  /// Handle app resume - check for pending PayU payments
  Future<PayUPaymentResult?> handleAppResume() async {
    try {
      debugPrint('🔄 PAYU LIFECYCLE: ========== APP RESUME HANDLER ==========');

      final hasPending = await hasPendingPayment();
      if (!hasPending) {
        debugPrint('🔄 PAYU LIFECYCLE: No pending PayU payment found');
        return null;
      }

      final paymentData = await getPendingPaymentData();
      if (paymentData == null) {
        debugPrint('❌ PAYU LIFECYCLE: Pending payment data is null');
        await clearPendingPayment();
        return null;
      }

      debugPrint('🔍 PAYU LIFECYCLE: Found pending payment, checking status...');
      debugPrint('🔍 PAYU LIFECYCLE: Transaction ID: ${paymentData['transactionId']}');

      // Try to get payment status from PayU SDK or server
      final result = await _checkPaymentStatus(paymentData);

      if (result != null) {
        debugPrint('✅ PAYU LIFECYCLE: Payment status resolved: ${result.type}');
        await clearPendingPayment();
        return result;
      }

      debugPrint('⏳ PAYU LIFECYCLE: Payment status still pending');
      return null;

    } catch (e) {
      debugPrint('❌ PAYU LIFECYCLE: Error handling app resume: $e');
      await clearPendingPayment();
      return null;
    }
  }

  /// Check payment status from server or PayU SDK
  Future<PayUPaymentResult?> _checkPaymentStatus(Map<String, dynamic> paymentData) async {
    try {
      debugPrint('🔍 PAYU LIFECYCLE: Checking payment status from multiple sources...');

      // 1. Check if payment was completed in memory (from PayU service)
      final lastCompleted = PayUService.getLastCompletedPayment();
      if (lastCompleted != null &&
          _isMatchingTransaction(lastCompleted, paymentData['transactionId'])) {
        debugPrint('✅ PAYU LIFECYCLE: Found completed payment in memory');
        return lastCompleted;
      }

      // 2. TODO: Implement server-side payment status check
      debugPrint('🔍 PAYU LIFECYCLE: Checking payment status from server...');
      // Example:
      // final response = await http.get('/api/payment/status/${paymentData['transactionId']}');
      // if (response.statusCode == 200) {
      //   final data = json.decode(response.body);
      //   return PayUPaymentResult.fromServerResponse(data);
      // }

      // 3. Check if payment is too old (consider it failed/timeout)
      final startTime = DateTime.parse(paymentData['startTime']);
      final elapsed = DateTime.now().difference(startTime);
      if (elapsed.inMinutes > 10) {
        debugPrint('⏰ PAYU LIFECYCLE: Payment too old (${elapsed.inMinutes} minutes), considering timeout');
        return PayUPaymentResult.timeout();
      }

      // 4. If payment is recent but we have no status, check if it was likely cancelled
      // This handles cases where user cancelled but we didn't get proper callback
      if (elapsed.inMinutes > 2) {
        debugPrint('🔍 PAYU LIFECYCLE: Payment older than 2 minutes with no resolution, likely cancelled');
        return PayUPaymentResult.cancelled(data: {
          'status': 'cancelled',
          'reason': 'Payment timeout or user cancellation',
          'elapsed_minutes': elapsed.inMinutes,
        });
      }

      return null; // Status still unknown/pending
    } catch (e) {
      debugPrint('❌ PAYU LIFECYCLE: Error checking payment status: $e');
      return null;
    }
  }

  /// Check if a payment result matches the given transaction ID
  bool _isMatchingTransaction(PayUPaymentResult result, String transactionId) {
    // Check various possible fields where transaction ID might be stored
    final data = result.data;
    if (data == null) return false;

    return data['txnid'] == transactionId ||
           data['transactionId'] == transactionId ||
           data['mihpayid'] == transactionId;
  }

  /// Mark payment as completed (called from PayU callbacks)
  Future<void> markPaymentCompleted(PayUPaymentResult result) async {
    try {
      debugPrint('✅ PAYU LIFECYCLE: Marking payment as completed: ${result.type}');
      await clearPendingPayment();
    } catch (e) {
      debugPrint('❌ PAYU LIFECYCLE: Error marking payment completed: $e');
    }
  }
}
