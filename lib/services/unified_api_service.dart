import '../core/api/api_service.dart';

/// This class is a compatibility layer for code that used the old UnifiedApiService
/// It forwards all calls to the centralized ApiService
class UnifiedApiService {
  // Base URL for the API
  static const String baseUrl = 'https://api2.eeil.online';

  // API version
  static const String apiVersion = 'v1';

  // Full API URL with version
  static const String apiUrl = '$baseUrl/api/$apiVersion';

  // Singleton pattern
  static final UnifiedApiService _instance = UnifiedApiService._internal();
  factory UnifiedApiService() => _instance;

  // Reference to the centralized API service
  final ApiService _apiService = ApiService();

  UnifiedApiService._internal();

  /// Get method that forwards to the centralized API service
  Future<dynamic> get(String endpoint, {Map<String, dynamic>? queryParams}) {
    return _apiService.get(endpoint, queryParams: queryParams);
  }

  /// Post method that forwards to the centralized API service
  Future<dynamic> post(String endpoint, {Map<String, dynamic>? data}) {
    return _apiService.post(endpoint, data: data);
  }

  /// Public post method that forwards to the centralized API service
  Future<dynamic> publicPost(String endpoint, Map<String, dynamic> data) {
    return _apiService.publicPost(endpoint, data);
  }

  /// Get token method that forwards to the centralized API service
  Future<String?> getToken() {
    return _apiService.getToken();
  }

  /// Save token method that forwards to the centralized API service
  Future<void> saveToken(String token) {
    return _apiService.saveToken(token);
  }

  /// Clear token method that forwards to the centralized API service
  Future<void> clearToken() {
    return _apiService.clearToken();
  }

  /// Check if user is logged in
  Future<bool> isLoggedIn() {
    return _apiService.isLoggedIn();
  }
}
