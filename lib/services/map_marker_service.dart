import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../core/api/api_service.dart';
import '../models/station/station_marker_response.dart';

/// Service for handling map marker operations with caching
class MapMarkerService {
  // Singleton pattern
  static final MapMarkerService _instance = MapMarkerService._internal();
  factory MapMarkerService() => _instance;
  MapMarkerService._internal();

  // API service for authentication
  final ApiService _apiService = ApiService();

  // Cache for marker images
  final Map<String, Uint8List> _imageCache = {};

  // Cache for marker data
  List<StationMarkerData>? _cachedMarkers;
  DateTime? _lastFetchTime;

  // Cache expiration time (5 minutes)
  static const Duration _cacheExpiration = Duration(minutes: 5);

  /// Get cached markers or fetch new ones if cache is expired
  Future<List<StationMarkerData>> getMarkers(
      Future<List<StationMarkerData>> Function() fetchFunction) async {
    // Check if we have cached markers and they're not expired
    if (_cachedMarkers != null &&
        _lastFetchTime != null &&
        DateTime.now().difference(_lastFetchTime!) < _cacheExpiration) {
      debugPrint('Using cached markers (${_cachedMarkers!.length})');
      return _cachedMarkers!;
    }

    // Fetch new markers
    try {
      final markers = await fetchFunction();
      _cachedMarkers = markers;
      _lastFetchTime = DateTime.now();

      // Pre-fetch marker images in the background
      _prefetchMarkerImages(markers);

      return markers;
    } catch (e) {
      // Return cached markers if available, even if expired
      if (_cachedMarkers != null) {
        return _cachedMarkers!;
      }
      rethrow;
    }
  }

  /// Pre-fetch marker images to cache them
  Future<void> _prefetchMarkerImages(List<StationMarkerData> markers) async {
    // Collect unique image URLs
    final Set<String> uniqueUrls = {};
    for (final marker in markers) {
      if (marker.mapPinUrl != null && marker.mapPinUrl!.isNotEmpty) {
        uniqueUrls.add(marker.mapPinUrl!);
      }
      if (marker.focusedMapPinUrl != null &&
          marker.focusedMapPinUrl!.isNotEmpty) {
        uniqueUrls.add(marker.focusedMapPinUrl!);
      }
    }

    debugPrint('Preloading ${uniqueUrls.length} unique marker images');

    // Load images in batches to avoid overwhelming the network
    const int batchSize = 10;
    for (int i = 0; i < uniqueUrls.length; i += batchSize) {
      final batch = uniqueUrls.skip(i).take(batchSize);
      await Future.wait(
        batch.map((url) => _fetchAndCacheImage(url)),
      );
      debugPrint('Loaded batch ${(i ~/ batchSize) + 1} of marker images');
    }
  }

  /// Fetch an image from a URL and cache it
  Future<Uint8List?> _fetchAndCacheImage(String url) async {
    // Check if the image is already cached
    if (_imageCache.containsKey(url)) {
      return _imageCache[url];
    }

    try {
      // Try to get from persistent cache first
      final cachedImage = await _getImageFromPersistentCache(url);
      if (cachedImage != null) {
        _imageCache[url] = cachedImage;
        return cachedImage;
      }

      // Implement a retry mechanism for fetching images
      int retryCount = 0;
      const maxRetries = 3;
      const retryDelay = Duration(seconds: 2);

      while (retryCount < maxRetries) {
        try {
          // Add authorization header for API2 URLs
          Map<String, String> headers = {};
          if (url.contains('api2.eeil.online')) {
            final token = await _apiService.getToken();
            if (token != null) {
              headers['Authorization'] = 'Bearer $token';
              headers['Content-Type'] = 'text/plain';
            }
          }

          // Use a shorter timeout to fail fast and retry
          final response = await http
              .get(
                Uri.parse(url),
                headers: headers,
              )
              .timeout(
                const Duration(seconds: 5),
              );

          if (response.statusCode == 200) {
            final imageData = response.bodyBytes;
            _imageCache[url] = imageData;

            // Save to persistent cache
            await _saveImageToPersistentCache(url, imageData);

            return imageData;
          }

          // If we get a non-200 status code, retry
          retryCount++;
          if (retryCount < maxRetries) {
            debugPrint(
                'Retrying image fetch ($retryCount/$maxRetries) for $url');
            await Future.delayed(retryDelay);
          }
        } catch (networkError) {
          debugPrint('Network error fetching image from $url: $networkError');

          // Check if it's a connection reset error
          if (networkError.toString().contains('Connection reset by peer')) {
            debugPrint('Connection reset detected, retrying with delay');
            retryCount++;
            if (retryCount < maxRetries) {
              // Use a longer delay for connection reset errors
              await Future.delayed(retryDelay * 2);
              continue;
            }
          } else {
            // For other errors, retry with normal delay
            retryCount++;
            if (retryCount < maxRetries) {
              await Future.delayed(retryDelay);
              continue;
            }
          }

          // All retries failed, break out of the loop
          break;
        }
      }
    } catch (e) {
      debugPrint('Error in image loading process for $url: $e');
    }
    return null;
  }

  /// Get a cached image for a marker
  Future<Uint8List?> getMarkerImage(String url) async {
    // Try to fetch the image with retries
    final result = await _fetchAndCacheImage(url);
    if (result != null) {
      return result;
    }

    // If all attempts fail, create a simple colored circle as a marker
    // This ensures we always have something to display even if image loading fails
    try {
      // Create a 48x48 transparent image with a colored circle
      final int width = 48;
      final int height = 48;
      final Uint8List bytes = Uint8List(width * height * 4);

      // Fill with transparent pixels
      for (int i = 0; i < bytes.length; i += 4) {
        bytes[i] = 0; // R
        bytes[i + 1] = 0; // G
        bytes[i + 2] = 0; // B
        bytes[i + 3] = 0; // A (transparent)
      }

      // Draw a colored circle in the center
      final int centerX = width ~/ 2;
      final int centerY = height ~/ 2;
      final int radius = width ~/ 3;

      // Determine color based on URL (green for available, red for unavailable, blue for charging)
      int r = 0, g = 0, b = 0;
      if (url.contains('unavailable')) {
        r = 255; // Red for unavailable
      } else if (url.contains('charging')) {
        b = 255; // Blue for charging
      } else {
        g = 255; // Green for available
      }

      // Draw the circle
      for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
          final int dx = x - centerX;
          final int dy = y - centerY;
          final int distanceSquared = dx * dx + dy * dy;

          if (distanceSquared <= radius * radius) {
            final int index = (y * width + x) * 4;
            bytes[index] = r; // R
            bytes[index + 1] = g; // G
            bytes[index + 2] = b; // B
            bytes[index + 3] = 255; // A (fully opaque)
          }
        }
      }

      return bytes;
    } catch (e) {
      debugPrint('Failed to create fallback marker image: $e');
      return null;
    }
  }

  /// Save image to persistent cache
  Future<void> _saveImageToPersistentCache(
      String url, Uint8List imageData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = _getCacheKey(url);
      final base64Image = base64Encode(imageData);
      await prefs.setString(key, base64Image);
    } catch (e) {
      debugPrint('Error saving image to persistent cache: $e');
    }
  }

  /// Get image from persistent cache
  Future<Uint8List?> _getImageFromPersistentCache(String url) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = _getCacheKey(url);
      final base64Image = prefs.getString(key);
      if (base64Image != null) {
        return base64Decode(base64Image);
      }
    } catch (e) {
      debugPrint('Error getting image from persistent cache: $e');
    }
    return null;
  }

  /// Generate a cache key for a URL
  String _getCacheKey(String url) {
    // Create a hash of the URL to use as a key
    return 'marker_image_${url.hashCode}';
  }

  /// Clear the cache
  Future<void> clearCache() async {
    _imageCache.clear();
    _cachedMarkers = null;
    _lastFetchTime = null;

    // Clear persistent cache
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      for (final key in keys) {
        if (key.startsWith('marker_image_')) {
          await prefs.remove(key);
        }
      }
    } catch (e) {
      debugPrint('Error clearing persistent cache: $e');
    }
  }
}
