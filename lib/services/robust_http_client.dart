import 'dart:convert';
import 'dart:io';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

/// A robust HTTP client that can handle connection resets and other network issues
class RobustHttpClient {
  // Base URL for the API
  static const String baseUrl = 'https://api2.eeil.online/api/v1';

  // Maximum number of attempts for each approach
  static const int maxAttempts = 3;

  // Singleton pattern
  static final RobustHttpClient _instance = RobustHttpClient._internal();
  factory RobustHttpClient() => _instance;
  RobustHttpClient._internal();

  /// Make a robust POST request that tries multiple approaches
  Future<Map<String, dynamic>> post(
    String endpoint,
    Map<String, dynamic> data, {
    Map<String, String>? headers,
  }) async {
    debugPrint('\n=== ROBUST HTTP CLIENT POST REQUEST ===');
    debugPrint('Endpoint: $endpoint');
    debugPrint('Data: $data');

    // List of approaches to try
    final approaches = [
      _postWithStandardClient,
      _postWithCustomSettings,
      _postWithHttpClient,
      _postWithDirectSocket,
    ];

    // Try each approach
    for (var i = 0; i < approaches.length; i++) {
      try {
        debugPrint(
            '\n=== TRYING HTTP APPROACH ${i + 1}/${approaches.length} ===');
        final result = await approaches[i](endpoint, data, headers);

        // If successful, return the result
        if (result['success'] == true) {
          debugPrint('HTTP approach ${i + 1} succeeded!');
          return result;
        }

        // If we got a response but it wasn't successful due to connection reset,
        // try the next approach
        if (result['error_code'] == 'CONNECTION_RESET') {
          debugPrint(
              'HTTP approach ${i + 1} failed with connection reset, trying next approach...');
          continue;
        }

        // For other errors, return the result
        return result;
      } catch (e) {
        debugPrint('Error with HTTP approach ${i + 1}: $e');
        // Continue to the next approach
      }
    }

    // If all approaches failed and mock responses are allowed, return a mock response

    // If all approaches failed, return a generic error
    return {
      'success': false,
      'message':
          'Failed to connect to the server after trying multiple approaches. Please try again later.',
      'error_code': 'ALL_APPROACHES_FAILED'
    };
  }

  /// Standard approach using http package
  Future<Map<String, dynamic>> _postWithStandardClient(
    String endpoint,
    Map<String, dynamic> data,
    Map<String, String>? headers,
  ) async {
    // Create a new client for this request only to avoid connection pooling
    final client = http.Client();

    try {
      // Create the URL - ensure it has the protocol
      final uri = Uri.parse('$baseUrl$endpoint');

      // Set default headers if not provided
      final requestHeaders = headers ??
          {
            'Content-Type': 'application/json',
          };

      // Log the request
      debugPrint('\n=== API REQUEST (STANDARD CLIENT) ===');
      debugPrint('URL: $uri');
      debugPrint('Headers: $requestHeaders');
      debugPrint('Data: $data');

      // Make a single request with shorter timeout
      final response = await client
          .post(
            uri,
            headers: requestHeaders,
            body: json.encode(data),
          )
          .timeout(const Duration(
              seconds: 8)); // Shorter timeout for faster response

      // Log the response
      debugPrint('\n=== API RESPONSE (STANDARD CLIENT) ===');
      debugPrint('Status code: ${response.statusCode}');
      debugPrint('Response body: ${response.body}');

      // Parse the response
      if (response.statusCode >= 200 && response.statusCode < 300) {
        try {
          final responseData =
              json.decode(response.body) as Map<String, dynamic>;
          return responseData;
        } catch (e) {
          debugPrint('Error parsing response: $e');
          return {
            'success': false,
            'message': 'Failed to parse response: $e',
            'error_code': 'PARSE_ERROR',
          };
        }
      } else {
        return {
          'success': false,
          'status_code': response.statusCode,
          'message': 'Server error: ${response.statusCode}',
          'body': response.body,
        };
      }
    } catch (e) {
      // Handle errors
      debugPrint('\n=== API ERROR (STANDARD CLIENT) ===');
      debugPrint('Error: $e');

      // Return appropriate error message
      if (e.toString().contains('Connection reset by peer')) {
        return {
          'success': false,
          'message':
              'The server unexpectedly closed the connection. Please try again in a few moments.',
          'error_code': 'CONNECTION_RESET',
          'detailed_error': e.toString(),
        };
      } else if (e.toString().contains('SocketException')) {
        return {
          'success': false,
          'message':
              'Network error. Please check your internet connection and try again.',
          'error_code': 'NETWORK_ERROR',
        };
      } else if (e.toString().contains('TimeoutException')) {
        return {
          'success': false,
          'message':
              'The server took too long to respond. Please try again later.',
          'error_code': 'TIMEOUT',
        };
      } else {
        return {
          'success': false,
          'message': 'Failed to connect to the server: $e',
          'error_code': 'CONNECTION_ERROR',
          'detailed_error': e.toString(),
        };
      }
    } finally {
      // Always close the client to release resources
      client.close();
    }
  }

  /// Approach with custom settings
  Future<Map<String, dynamic>> _postWithCustomSettings(
    String endpoint,
    Map<String, dynamic> data,
    Map<String, String>? headers,
  ) async {
    // Create a new client for this request only to avoid connection pooling
    final client = http.Client();

    try {
      // Create the URL with a different format
      final uri = Uri.parse('$baseUrl$endpoint');

      // Set more detailed headers
      final requestHeaders = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Connection': 'close', // Force connection close
        'User-Agent': 'EcoPlug-App/1.0', // Add user agent
      };

      // Add custom headers if provided
      if (headers != null) {
        requestHeaders.addAll(headers);
      }

      // Log the request
      debugPrint('\n=== API REQUEST (CUSTOM SETTINGS) ===');
      debugPrint('URL: $uri');
      debugPrint('Headers: $requestHeaders');
      debugPrint('Data: $data');

      // Make a single request with different timeout
      final response = await client
          .post(
            uri,
            headers: requestHeaders,
            body: json.encode(data),
          )
          .timeout(const Duration(seconds: 10)); // Slightly longer timeout

      // Log the response
      debugPrint('\n=== API RESPONSE (CUSTOM SETTINGS) ===');
      debugPrint('Status code: ${response.statusCode}');
      debugPrint('Response body: ${response.body}');

      // Parse the response
      if (response.statusCode >= 200 && response.statusCode < 300) {
        try {
          final responseData =
              json.decode(response.body) as Map<String, dynamic>;
          return responseData;
        } catch (e) {
          debugPrint('Error parsing response: $e');
          return {
            'success': false,
            'message': 'Failed to parse response: $e',
            'error_code': 'PARSE_ERROR',
          };
        }
      } else {
        return {
          'success': false,
          'status_code': response.statusCode,
          'message': 'Server error: ${response.statusCode}',
          'body': response.body,
        };
      }
    } catch (e) {
      // Handle errors
      debugPrint('\n=== API ERROR (CUSTOM SETTINGS) ===');
      debugPrint('Error: $e');

      // Return appropriate error message
      if (e.toString().contains('Connection reset by peer')) {
        return {
          'success': false,
          'message':
              'The server unexpectedly closed the connection. Please try again in a few moments.',
          'error_code': 'CONNECTION_RESET',
          'detailed_error': e.toString(),
        };
      } else if (e.toString().contains('SocketException')) {
        return {
          'success': false,
          'message':
              'Network error. Please check your internet connection and try again.',
          'error_code': 'NETWORK_ERROR',
        };
      } else if (e.toString().contains('TimeoutException')) {
        return {
          'success': false,
          'message':
              'The server took too long to respond. Please try again later.',
          'error_code': 'TIMEOUT',
        };
      } else {
        return {
          'success': false,
          'message': 'Failed to connect to the server: $e',
          'error_code': 'CONNECTION_ERROR',
          'detailed_error': e.toString(),
        };
      }
    } finally {
      // Always close the client to release resources
      client.close();
    }
  }

  /// Approach using HttpClient
  Future<Map<String, dynamic>> _postWithHttpClient(
    String endpoint,
    Map<String, dynamic> data,
    Map<String, String>? headers,
  ) async {
    try {
      // Create the URL
      final uri = Uri.parse('$baseUrl$endpoint');

      // Log the request
      debugPrint('\n=== API REQUEST (HTTP CLIENT) ===');
      debugPrint('URL: $uri');
      debugPrint('Data: $data');

      // Create a HttpClient
      final httpClient = HttpClient();

      // Configure the client
      httpClient.connectionTimeout = const Duration(seconds: 10);
      httpClient.idleTimeout = const Duration(seconds: 5);
      httpClient.autoUncompress = true;

      try {
        // Create the request
        final request = await httpClient.postUrl(uri);

        // Set headers
        request.headers.set('Content-Type', 'application/json');
        request.headers.set('Accept', 'application/json');
        request.headers.set('Connection', 'close');

        // Add custom headers if provided
        if (headers != null) {
          headers.forEach((key, value) {
            request.headers.set(key, value);
          });
        }

        // Set body
        request.add(utf8.encode(json.encode(data)));

        // Send the request and get the response
        final response =
            await request.close().timeout(const Duration(seconds: 12));

        // Read the response
        final responseBody = await response.transform(utf8.decoder).join();

        // Log the response
        debugPrint('\n=== API RESPONSE (HTTP CLIENT) ===');
        debugPrint('Status code: ${response.statusCode}');
        debugPrint('Response body: $responseBody');

        // Close the client
        httpClient.close();

        // Parse the response
        if (response.statusCode >= 200 && response.statusCode < 300) {
          try {
            final responseData =
                json.decode(responseBody) as Map<String, dynamic>;
            return responseData;
          } catch (e) {
            debugPrint('Error parsing response: $e');
            return {
              'success': false,
              'message': 'Failed to parse response: $e',
              'error_code': 'PARSE_ERROR',
            };
          }
        } else {
          return {
            'success': false,
            'status_code': response.statusCode,
            'message': 'Server error: ${response.statusCode}',
            'body': responseBody,
          };
        }
      } finally {
        // Always close the client
        httpClient.close();
      }
    } catch (e) {
      // Handle errors
      debugPrint('\n=== API ERROR (HTTP CLIENT) ===');
      debugPrint('Error: $e');

      // Return appropriate error message
      if (e.toString().contains('Connection reset by peer')) {
        return {
          'success': false,
          'message':
              'The server unexpectedly closed the connection. Please try again in a few moments.',
          'error_code': 'CONNECTION_RESET',
          'detailed_error': e.toString(),
        };
      } else if (e.toString().contains('SocketException')) {
        return {
          'success': false,
          'message':
              'Network error. Please check your internet connection and try again.',
          'error_code': 'NETWORK_ERROR',
        };
      } else if (e.toString().contains('TimeoutException')) {
        return {
          'success': false,
          'message':
              'The server took too long to respond. Please try again later.',
          'error_code': 'TIMEOUT',
        };
      } else {
        return {
          'success': false,
          'message': 'Failed to connect to the server: $e',
          'error_code': 'CONNECTION_ERROR',
          'detailed_error': e.toString(),
        };
      }
    }
  }

  /// Approach using direct socket connection
  Future<Map<String, dynamic>> _postWithDirectSocket(
    String endpoint,
    Map<String, dynamic> data,
    Map<String, String>? headers,
  ) async {
    Socket? socket;

    try {
      // Extract host from base URL
      final uri = Uri.parse(baseUrl);
      final host = uri.host;
      final port = uri.port != 0 ? uri.port : 443;

      // Log the request
      debugPrint('\n=== API REQUEST (DIRECT SOCKET) ===');
      debugPrint('Host: $host');
      debugPrint('Port: $port');
      debugPrint('Path: $endpoint');
      debugPrint('Data: $data');

      // Connect to the server
      socket = await Socket.connect(host, port,
          timeout: const Duration(seconds: 10));

      // Set socket options
      socket.setOption(SocketOption.tcpNoDelay, true);

      // Prepare the HTTP request
      final requestBody = json.encode(data);

      // Build headers
      final headerLines = [
        'POST $endpoint HTTP/1.1',
        'Host: $host',
        'Content-Type: application/json',
        'Content-Length: ${requestBody.length}',
        'Connection: close',
      ];

      // Add custom headers if provided
      if (headers != null) {
        headers.forEach((key, value) {
          headerLines.add('$key: $value');
        });
      }

      // Complete the request
      headerLines.add('');
      headerLines.add(requestBody);

      // Join the request lines
      final request = headerLines.join('\r\n');

      // Send the request
      socket.write(request);

      // Read the response
      final responseCompleter = Completer<String>();
      final responseBuffer = StringBuffer();

      socket.listen(
        (data) {
          responseBuffer.write(String.fromCharCodes(data));
        },
        onDone: () {
          responseCompleter.complete(responseBuffer.toString());
        },
        onError: (error) {
          responseCompleter.completeError(error);
        },
        cancelOnError: true,
      );

      // Wait for the response with timeout
      final response =
          await responseCompleter.future.timeout(const Duration(seconds: 15));

      // Log the response
      debugPrint('\n=== API RESPONSE (DIRECT SOCKET) ===');
      debugPrint('Response: $response');

      // Parse the response
      final responseLines = response.split('\r\n');
      final statusLine = responseLines.first;
      final statusCode = int.parse(statusLine.split(' ')[1]);

      // Find the response body (after the empty line)
      final bodyIndex = responseLines.indexOf('');
      final responseBody = bodyIndex >= 0
          ? responseLines.sublist(bodyIndex + 1).join('\r\n')
          : '';

      // Log the parsed response
      debugPrint('Status code: $statusCode');
      debugPrint('Response body: $responseBody');

      // Parse the JSON response
      if (statusCode >= 200 && statusCode < 300 && responseBody.isNotEmpty) {
        try {
          final responseData =
              json.decode(responseBody) as Map<String, dynamic>;
          return responseData;
        } catch (e) {
          debugPrint('Error parsing response: $e');
          return {
            'success': false,
            'message': 'Failed to parse response: $e',
            'error_code': 'PARSE_ERROR',
          };
        }
      } else {
        return {
          'success': false,
          'status_code': statusCode,
          'message': 'Server error: $statusCode',
          'body': responseBody,
        };
      }
    } catch (e) {
      // Handle errors
      debugPrint('\n=== API ERROR (DIRECT SOCKET) ===');
      debugPrint('Error: $e');

      // Return appropriate error message
      if (e.toString().contains('Connection reset by peer')) {
        return {
          'success': false,
          'message':
              'The server unexpectedly closed the connection. Please try again in a few moments.',
          'error_code': 'CONNECTION_RESET',
          'detailed_error': e.toString(),
        };
      } else if (e.toString().contains('SocketException')) {
        return {
          'success': false,
          'message':
              'Network error. Please check your internet connection and try again.',
          'error_code': 'NETWORK_ERROR',
        };
      } else if (e.toString().contains('TimeoutException')) {
        return {
          'success': false,
          'message':
              'The server took too long to respond. Please try again later.',
          'error_code': 'TIMEOUT',
        };
      } else {
        return {
          'success': false,
          'message': 'Failed to connect to the server: $e',
          'error_code': 'CONNECTION_ERROR',
          'detailed_error': e.toString(),
        };
      }
    } finally {
      // Close the socket if it's open
      socket?.destroy();
    }
  }
}
