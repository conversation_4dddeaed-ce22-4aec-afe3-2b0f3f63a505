import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

/// Service for managing charging channel subscriptions with the backend
/// Implements the channel subscription system for real-time charging notifications
/// Format: charging_{transaction_id}
class ChargingChannelSubscriptionService {
  static const MethodChannel _channel =
      MethodChannel('com.eeil.ecoplug/custom_charging_notification');

  static bool _isInitialized = false;
  static String? _currentTransactionId;

  /// Initialize the channel subscription service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🔔 Initializing Charging Channel Subscription Service...');

      if (Platform.isAndroid) {
        await _channel.invokeMethod('initialize');
        _isInitialized = true;
        debugPrint(
            '✅ Charging Channel Subscription Service initialized successfully');
      } else {
        debugPrint('⚠️ Channel subscriptions only supported on Android');
        _isInitialized = true;
      }
    } catch (e) {
      debugPrint(
          '❌ Error initializing Charging Channel Subscription Service: $e');
    }
  }

  /// Subscribe to charging FCM topic when charging session starts
  /// Format: charging_{transaction_id}
  static Future<bool> subscribeToChargingTopic(String transactionId) async {
    if (!_isInitialized) {
      await initialize();
    }

    if (!Platform.isAndroid) {
      debugPrint('⚠️ Channel subscriptions only supported on Android');
      return false;
    }

    try {
      debugPrint('🔔 ===== SUBSCRIBING TO CHARGING CHANNEL =====');
      debugPrint('🔔 Transaction ID: $transactionId');
      debugPrint('🔔 Channel Format: charging_$transactionId');

      final result = await _channel.invokeMethod('subscribeToChargingTopic', {
        'transactionId': transactionId,
      });

      if (result == true) {
        _currentTransactionId = transactionId;
        debugPrint(
            '✅ Successfully subscribed to charging channel: charging_$transactionId');
        return true;
      } else {
        debugPrint(
            '❌ Failed to subscribe to charging channel: charging_$transactionId');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error subscribing to charging channel: $e');
      return false;
    }
  }

  /// Unsubscribe from charging FCM topic when charging session completes/stops
  static Future<bool> unsubscribeFromChargingTopic(
      [String? transactionId]) async {
    if (!Platform.isAndroid) {
      debugPrint('⚠️ Channel subscriptions only supported on Android');
      return false;
    }

    try {
      final targetTransactionId = transactionId ?? _currentTransactionId;

      if (targetTransactionId == null) {
        debugPrint('⚠️ No transaction ID available for unsubscription');
        return true; // Consider it successful if no active subscription
      }

      debugPrint('🔕 ===== UNSUBSCRIBING FROM CHARGING CHANNEL =====');
      debugPrint('🔕 Transaction ID: $targetTransactionId');
      debugPrint('🔕 Channel Format: charging_$targetTransactionId');

      final result =
          await _channel.invokeMethod('unsubscribeFromChargingTopic', {
        'transactionId': targetTransactionId,
      });

      if (result == true) {
        if (_currentTransactionId == targetTransactionId) {
          _currentTransactionId = null;
        }
        debugPrint(
            '✅ Successfully unsubscribed from charging channel: charging_$targetTransactionId');
        return true;
      } else {
        debugPrint(
            '❌ Failed to unsubscribe from charging channel: charging_$targetTransactionId');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error unsubscribing from charging channel: $e');
      return false;
    }
  }

  /// Get the current active transaction ID
  static String? getCurrentTransactionId() {
    return _currentTransactionId;
  }

  /// Check if currently subscribed to a charging channel
  static bool isSubscribed() {
    return _currentTransactionId != null;
  }

  /// Subscribe to charging channel with automatic session management
  /// This method handles the complete subscription lifecycle
  static Future<bool> startChargingSession({
    required String transactionId,
    required String stationUid,
    required String connectorId,
    String? authReference,
  }) async {
    try {
      debugPrint(
          '🚀 ===== STARTING CHARGING SESSION WITH CHANNEL SUBSCRIPTION =====');
      debugPrint('🚀 Transaction ID: $transactionId');
      debugPrint('🚀 Station UID: $stationUid');
      debugPrint('🚀 Connector ID: $connectorId');
      debugPrint('🚀 Auth Reference: $authReference');

      // Subscribe to the charging topic
      final subscriptionSuccess = await subscribeToChargingTopic(transactionId);

      if (subscriptionSuccess) {
        debugPrint(
            '✅ Charging session started with channel subscription: charging_$transactionId');
        return true;
      } else {
        debugPrint(
            '❌ Failed to start charging session - channel subscription failed');
        return false;
      }
    } catch (e) {
      debugPrint(
          '❌ Error starting charging session with channel subscription: $e');
      return false;
    }
  }

  /// Stop charging session with automatic channel unsubscription
  /// This method handles the complete unsubscription lifecycle
  static Future<bool> stopChargingSession([String? transactionId]) async {
    try {
      final targetTransactionId = transactionId ?? _currentTransactionId;

      debugPrint(
          '🛑 ===== STOPPING CHARGING SESSION WITH CHANNEL UNSUBSCRIPTION =====');
      debugPrint('🛑 Transaction ID: $targetTransactionId');

      if (targetTransactionId == null) {
        debugPrint('⚠️ No active charging session to stop');
        return true;
      }

      // Unsubscribe from the charging topic
      final unsubscriptionSuccess =
          await unsubscribeFromChargingTopic(targetTransactionId);

      if (unsubscriptionSuccess) {
        debugPrint(
            '✅ Charging session stopped with channel unsubscription: charging_$targetTransactionId');
        return true;
      } else {
        debugPrint(
            '❌ Failed to stop charging session - channel unsubscription failed');
        return false;
      }
    } catch (e) {
      debugPrint(
          '❌ Error stopping charging session with channel unsubscription: $e');
      return false;
    }
  }

  /// Emergency cleanup - unsubscribe from all channels
  static Future<void> emergencyCleanup() async {
    try {
      debugPrint('🚨 ===== EMERGENCY CHANNEL CLEANUP =====');

      if (_currentTransactionId != null) {
        await unsubscribeFromChargingTopic(_currentTransactionId);
      }

      _currentTransactionId = null;
      debugPrint('✅ Emergency channel cleanup completed');
    } catch (e) {
      debugPrint('❌ Error during emergency channel cleanup: $e');
    }
  }
}
