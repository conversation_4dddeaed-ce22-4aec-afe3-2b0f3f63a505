import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service for managing FCM token subscription with the backend server
/// Handles charging notification subscriptions and token management
class FCMSubscriptionService {
  static final FCMSubscriptionService _instance =
      FCMSubscriptionService._internal();
  factory FCMSubscriptionService() => _instance;
  FCMSubscriptionService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  String? _fcmToken;
  bool _isSubscribed = false;
  String? _currentTransactionId;

  /// Initialize the FCM subscription service
  Future<void> initialize() async {
    try {
      debugPrint('🔔 ===== INITIALIZING FCM SUBSCRIPTION SERVICE =====');

      // Get FCM token
      await _getFCMToken();

      // Check existing subscription status
      await _loadSubscriptionStatus();

      debugPrint('✅ FCM Subscription Service initialized successfully');
      debugPrint('🔔 Current subscription status: $_isSubscribed');
      if (_currentTransactionId != null) {
        debugPrint('🔔 Current transaction ID: $_currentTransactionId');
      }
    } catch (e) {
      debugPrint('❌ Error initializing FCM Subscription Service: $e');
    }
  }

  /// Get FCM token from Firebase
  Future<void> _getFCMToken() async {
    try {
      _fcmToken = await _firebaseMessaging.getToken();
      if (_fcmToken != null) {
        debugPrint('🔔 FCM Token obtained: ${_fcmToken!.substring(0, 20)}...');
        await _storeFCMToken(_fcmToken!);
      } else {
        debugPrint('❌ Failed to get FCM token');
      }
    } catch (e) {
      debugPrint('❌ Error getting FCM token: $e');
    }
  }

  /// Store FCM token locally
  Future<void> _storeFCMToken(String token) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('fcm_token', token);
      debugPrint('✅ FCM token stored locally');
    } catch (e) {
      debugPrint('❌ Error storing FCM token: $e');
    }
  }

  /// Load subscription status from local storage
  Future<void> _loadSubscriptionStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _isSubscribed = prefs.getBool('fcm_subscribed') ?? false;
      _currentTransactionId = prefs.getString('fcm_transaction_id');

      debugPrint('📱 Loaded subscription status: $_isSubscribed');
      if (_currentTransactionId != null) {
        debugPrint('📱 Loaded transaction ID: $_currentTransactionId');
      }
    } catch (e) {
      debugPrint('❌ Error loading subscription status: $e');
    }
  }

  /// Save subscription status to local storage
  Future<void> _saveSubscriptionStatus(
      bool isSubscribed, String? transactionId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('fcm_subscribed', isSubscribed);

      if (transactionId != null) {
        await prefs.setString('fcm_transaction_id', transactionId);
      } else {
        await prefs.remove('fcm_transaction_id');
      }

      _isSubscribed = isSubscribed;
      _currentTransactionId = transactionId;

      debugPrint('✅ Subscription status saved: $isSubscribed');
      if (transactionId != null) {
        debugPrint('✅ Transaction ID saved: $transactionId');
      }
    } catch (e) {
      debugPrint('❌ Error saving subscription status: $e');
    }
  }

  /// Subscribe to charging notifications using FCM topics only
  /// Backend team confirmed: No server endpoints needed, use self-subscription with topic format: Charging_id
  Future<bool> subscribeToChargingNotifications(String chargingId) async {
    try {
      debugPrint(
          '🔔 ===== SUBSCRIBING TO CHARGING NOTIFICATIONS (FCM TOPICS ONLY) =====');
      debugPrint('🔔 Charging ID: $chargingId');

      if (_fcmToken == null) {
        await _getFCMToken();
        if (_fcmToken == null) {
          debugPrint('❌ Cannot subscribe: FCM token not available');
          return false;
        }
      }

      debugPrint(
          '🔔 FCM Token available: ${_fcmToken!.length > 20 ? "${_fcmToken!.substring(0, 20)}..." : _fcmToken}');
      debugPrint(
          '🔔 Backend confirmed: Using FCM topic self-subscription only');
      debugPrint('🔔 Topic format: Charging_id (where id = $chargingId)');

      // Subscribe to FCM topic with backend-specified format: Charging_id
      final topicName = 'Charging_$chargingId';
      debugPrint('🔔 Subscribing to FCM topic: $topicName');

      final subscriptionSuccess = await _subscribeToFCMTopic(topicName);

      if (subscriptionSuccess) {
        debugPrint('✅ Successfully subscribed to FCM topic: $topicName');
        debugPrint('✅ Charging notifications will be received via FCM topic');

        // Save subscription status locally
        await _saveSubscriptionStatus(true, chargingId);

        // Clear any previous error details since subscription succeeded
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove('last_subscription_error');
        await prefs.remove('last_subscription_error_time');

        return true;
      } else {
        debugPrint('❌ Failed to subscribe to FCM topic: $topicName');

        // Store error details for debugging
        final errorDetails =
            'FCM topic subscription failed for topic: $topicName';
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('last_subscription_error', errorDetails);
        await prefs.setString(
            'last_subscription_error_time', DateTime.now().toIso8601String());

        return false;
      }
    } catch (e) {
      debugPrint('❌ Critical error subscribing to charging notifications: $e');
      debugPrint('❌ Stack trace: ${StackTrace.current}');

      // Store error for debugging
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('last_subscription_error', 'Critical error: $e');
      await prefs.setString(
          'last_subscription_error_time', DateTime.now().toIso8601String());

      return false;
    }
  }

  /// Unsubscribe from charging notifications using FCM topics only
  /// Backend team confirmed: No server endpoints needed, use FCM topic format: Charging_id
  Future<bool> unsubscribeFromChargingNotifications() async {
    try {
      debugPrint(
          '🔔 ===== UNSUBSCRIBING FROM CHARGING NOTIFICATIONS (FCM TOPICS ONLY) =====');

      if (!_isSubscribed || _currentTransactionId == null) {
        debugPrint('ℹ️ No active subscription to unsubscribe from');
        return true;
      }

      debugPrint('🔔 Current charging ID: $_currentTransactionId');
      debugPrint(
          '🔔 Backend confirmed: Using FCM topic self-unsubscription only');

      // Unsubscribe from FCM topic with backend-specified format: Charging_id
      final topicName = 'Charging_$_currentTransactionId';
      debugPrint('🔕 Unsubscribing from FCM topic: $topicName');

      final unsubscriptionSuccess = await _unsubscribeFromFCMTopic(topicName);

      if (unsubscriptionSuccess) {
        debugPrint('✅ Successfully unsubscribed from FCM topic: $topicName');
        debugPrint('✅ Charging notifications unsubscribed via FCM topic');

        // Clear subscription status locally
        await _saveSubscriptionStatus(false, null);

        return true;
      } else {
        debugPrint('❌ Failed to unsubscribe from FCM topic: $topicName');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error unsubscribing from charging notifications: $e');
      return false;
    }
  }

  /// Subscribe to FCM topic with enhanced error handling and retry logic
  Future<bool> _subscribeToFCMTopic(String topic) async {
    try {
      debugPrint('🔔 Attempting to subscribe to FCM topic: $topic');

      // Validate topic name format
      if (!_isValidTopicName(topic)) {
        debugPrint('❌ Invalid topic name format: $topic');
        return false;
      }

      // Attempt subscription with timeout
      await _firebaseMessaging.subscribeToTopic(topic).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw TimeoutException(
              'FCM topic subscription timeout', const Duration(seconds: 10));
        },
      );

      debugPrint('✅ Successfully subscribed to FCM topic: $topic');

      // Verify subscription by attempting to get token
      final token = await _firebaseMessaging.getToken();
      if (token != null) {
        debugPrint(
            '✅ FCM token available for topic subscription: ${token.substring(0, 20)}...');
      }

      return true;
    } on TimeoutException catch (e) {
      debugPrint('❌ FCM topic subscription timeout for $topic: $e');
      return false;
    } catch (e) {
      debugPrint('❌ Error subscribing to FCM topic $topic: $e');
      debugPrint('❌ Error type: ${e.runtimeType}');

      // Retry once after a delay
      try {
        debugPrint('🔄 Retrying FCM topic subscription for $topic...');
        await Future.delayed(const Duration(seconds: 2));
        await _firebaseMessaging.subscribeToTopic(topic);
        debugPrint('✅ FCM topic subscription retry successful: $topic');
        return true;
      } catch (retryError) {
        debugPrint(
            '❌ FCM topic subscription retry failed for $topic: $retryError');
        return false;
      }
    }
  }

  /// Unsubscribe from FCM topic with enhanced error handling
  Future<bool> _unsubscribeFromFCMTopic(String topic) async {
    try {
      debugPrint('🔕 Attempting to unsubscribe from FCM topic: $topic');

      // Validate topic name format
      if (!_isValidTopicName(topic)) {
        debugPrint('❌ Invalid topic name format: $topic');
        return false;
      }

      // Attempt unsubscription with timeout
      await _firebaseMessaging.unsubscribeFromTopic(topic).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw TimeoutException(
              'FCM topic unsubscription timeout', const Duration(seconds: 10));
        },
      );

      debugPrint('✅ Successfully unsubscribed from FCM topic: $topic');
      return true;
    } on TimeoutException catch (e) {
      debugPrint('❌ FCM topic unsubscription timeout for $topic: $e');
      return false;
    } catch (e) {
      debugPrint('❌ Error unsubscribing from FCM topic $topic: $e');
      debugPrint('❌ Error type: ${e.runtimeType}');

      // Retry once after a delay
      try {
        debugPrint('🔄 Retrying FCM topic unsubscription for $topic...');
        await Future.delayed(const Duration(seconds: 2));
        await _firebaseMessaging.unsubscribeFromTopic(topic);
        debugPrint('✅ FCM topic unsubscription retry successful: $topic');
        return true;
      } catch (retryError) {
        debugPrint(
            '❌ FCM topic unsubscription retry failed for $topic: $retryError');
        return false;
      }
    }
  }

  /// Validate FCM topic name format
  bool _isValidTopicName(String topic) {
    // FCM topic names must match the pattern: [a-zA-Z0-9-_.~%]+
    final topicRegex = RegExp(r'^[a-zA-Z0-9\-_.~%]+$');
    final isValid = topicRegex.hasMatch(topic) && topic.length <= 900;

    if (!isValid) {
      debugPrint(
          '❌ Invalid topic name: $topic (must match [a-zA-Z0-9-_.~%]+ and be ≤900 chars)');
    }

    return isValid;
  }

  /// Get current FCM token
  String? get fcmToken => _fcmToken;

  /// Check if currently subscribed to charging notifications
  bool get isSubscribed => _isSubscribed;

  /// Get current transaction ID if subscribed
  String? get currentTransactionId => _currentTransactionId;

  /// Show debug dialog with subscription status
  Future<void> showSubscriptionDebugInfo() async {
    debugPrint('🔍 ===== FCM SUBSCRIPTION DEBUG INFO =====');
    debugPrint(
        '🔍 FCM Token: ${_fcmToken != null ? (_fcmToken!.length > 20 ? "${_fcmToken!.substring(0, 20)}..." : _fcmToken) : "NULL"}');
    debugPrint('🔍 Is Subscribed: $_isSubscribed');
    debugPrint('🔍 Current Transaction ID: $_currentTransactionId');
    debugPrint('🔍 ==========================================');
  }

  /// Test subscription functionality
  Future<Map<String, dynamic>> testSubscription() async {
    try {
      debugPrint('🧪 ===== TESTING FCM SUBSCRIPTION =====');

      // Test with a dummy transaction ID
      final testTransactionId = 'TEST_${DateTime.now().millisecondsSinceEpoch}';

      // Attempt subscription
      final subscriptionResult =
          await subscribeToChargingNotifications(testTransactionId);

      // Wait a moment
      await Future.delayed(const Duration(seconds: 2));

      // Attempt unsubscription
      final unsubscriptionResult = await unsubscribeFromChargingNotifications();

      final testResults = {
        'fcm_token_available': _fcmToken != null,
        'fcm_token': _fcmToken != null
            ? (_fcmToken!.length > 20
                ? "${_fcmToken!.substring(0, 20)}..."
                : _fcmToken)
            : null,
        'subscription_success': subscriptionResult,
        'unsubscription_success': unsubscriptionResult,
        'test_transaction_id': testTransactionId,
        'timestamp': DateTime.now().toIso8601String(),
      };

      debugPrint('🧪 Test Results: $testResults');
      return testResults;
    } catch (e) {
      debugPrint('❌ Error testing subscription: $e');
      return {
        'error': true,
        'message': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }
}
