import 'package:flutter/foundation.dart';
import '../core/api/api_service.dart';
import '../core/api/api_config.dart';
import '../models/charging_session_model.dart';

/// Service for handling charging session transaction history
/// This is separate from the main charging session service to avoid conflicts
class ChargingHistoryService {
  final ApiService _apiService = ApiService();

  /// Fetch charging session history from API - ONLY REAL API DATA
  Future<ChargingSessionResponse?> getChargingSessionHistory() async {
    try {
      debugPrint('🔌 ===== FETCHING CHARGING SESSION HISTORY =====');
      debugPrint(
          '🔌 REAL API MODE: Only real API data - no mock data or fallbacks');
      debugPrint('🔌 Endpoint: ${ApiConfig.chargingHistoryList}');
      debugPrint(
          '🔌 Full URL: ${ApiConfig.apiUrl}${ApiConfig.chargingHistoryList}');
      debugPrint(
          '🔌 Expected URL: https://api2.eeil.online/api/v1/user/sessions/charging-list');

      final response = await _apiService.get(ApiConfig.chargingHistoryList);

      debugPrint('🔌 ===== API RESPONSE RECEIVED =====');
      debugPrint('🔌 Response is null: ${response == null}');
      debugPrint('🔌 Response type: ${response.runtimeType}');

      if (response != null) {
        debugPrint('🔌 Response keys: ${response.keys.toList()}');
        debugPrint('🔌 Response content: $response');

        // Validate response structure
        if (response is Map<String, dynamic>) {
          debugPrint('🔌 ✅ Response is valid JSON object');

          if (response.containsKey('success') && response.containsKey('data')) {
            debugPrint('🔌 ✅ Response has required fields (success, data)');
            debugPrint('🔌 Success value: ${response['success']}');
            debugPrint('🔌 Data type: ${response['data'].runtimeType}');
            debugPrint('🔌 Data content: ${response['data']}');

            final chargingResponse = ChargingSessionResponse.fromJson(response);
            debugPrint(
                '🔌 ✅ Successfully parsed ${chargingResponse.data.length} charging sessions');

            // Log each session for debugging
            for (int i = 0; i < chargingResponse.data.length && i < 3; i++) {
              final session = chargingResponse.data[i];
              debugPrint(
                  '🔌 Session $i: ID=${session.id}, Status=${session.status}, Charger=${session.charger.chargerName}');
            }

            return chargingResponse;
          } else {
            debugPrint(
                '🔌 ❌ Invalid response structure: missing success or data fields');
            debugPrint('🔌 Available keys: ${response.keys.toList()}');
            throw Exception('Invalid response format from server');
          }
        } else {
          debugPrint('🔌 ❌ Response is not a valid JSON object');
          debugPrint('🔌 Response type: ${response.runtimeType}');
          throw Exception('Invalid response format from server');
        }
      } else {
        debugPrint('🔌 ❌ No response data received from server');
        throw Exception('No data received from server');
      }
    } catch (e) {
      debugPrint('🔌 ❌ Error fetching charging session history: $e');
      debugPrint('🔌 Exception type: ${e.runtimeType}');
      debugPrint('🔌 Exception details: ${e.toString()}');

      // Provide more specific error messages
      if (e.toString().contains('401') ||
          e.toString().contains('Unauthorized')) {
        throw Exception('Authentication failed. Please login again.');
      } else if (e.toString().contains('403') ||
          e.toString().contains('Forbidden')) {
        throw Exception(
            'Access denied. You don\'t have permission to view charging history.');
      } else if (e.toString().contains('404') ||
          e.toString().contains('Not Found')) {
        throw Exception(
            'Charging history service not found. Please try again later.');
      } else if (e.toString().contains('500') ||
          e.toString().contains('Internal Server Error')) {
        throw Exception('Server error. Please try again later.');
      } else if (e.toString().contains('timeout') ||
          e.toString().contains('TimeoutException')) {
        throw Exception(
            'Request timeout. Please check your internet connection.');
      } else if (e.toString().contains('SocketException') ||
          e.toString().contains('NetworkException')) {
        throw Exception(
            'Network error. Please check your internet connection.');
      } else {
        throw Exception('Failed to fetch charging history: ${e.toString()}');
      }
    }
  }

  /// Refresh charging session history with pull-to-refresh
  Future<ChargingSessionResponse?> refreshChargingSessionHistory() async {
    debugPrint('🔌 Refreshing charging session history...');

    // Add a small delay for better UX
    await Future.delayed(const Duration(milliseconds: 500));

    return await getChargingSessionHistory();
  }

  /// Filter charging sessions by status
  List<ChargingSessionModel> filterSessionsByStatus(
      List<ChargingSessionModel> sessions, String? status) {
    if (status == null || status.isEmpty) {
      return sessions;
    }

    return sessions
        .where(
            (session) => session.status.toUpperCase() == status.toUpperCase())
        .toList();
  }

  /// Sort charging sessions by date
  List<ChargingSessionModel> sortSessionsByDate(
      List<ChargingSessionModel> sessions,
      {bool ascending = false}) {
    final sortedSessions = List<ChargingSessionModel>.from(sessions);

    sortedSessions.sort((a, b) {
      if (ascending) {
        return a.createdAt.compareTo(b.createdAt);
      } else {
        return b.createdAt.compareTo(a.createdAt);
      }
    });

    return sortedSessions;
  }

  /// Search charging sessions by charger name or location
  List<ChargingSessionModel> searchSessions(
      List<ChargingSessionModel> sessions, String query) {
    if (query.isEmpty) {
      return sessions;
    }

    final lowercaseQuery = query.toLowerCase();

    return sessions
        .where((session) =>
            session.charger.chargerName
                .toLowerCase()
                .contains(lowercaseQuery) ||
            session.location.address.toLowerCase().contains(lowercaseQuery) ||
            session.invoiceNumber.toLowerCase().contains(lowercaseQuery))
        .toList();
  }
}
