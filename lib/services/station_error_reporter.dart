import 'package:flutter/foundation.dart';

/// Comprehensive error reporting service for station-related issues
/// This service helps identify and report specific issues with station data
class StationErrorReporter {
  static const String _logPrefix = 'StationErrorReporter';

  /// Report station details loading error with comprehensive context
  static void reportStationDetailsError({
    required String uid,
    required String errorMessage,
    required String errorType,
    Map<String, dynamic>? apiResponse,
    Map<String, dynamic>? additionalContext,
  }) {
    debugPrint('\n=== STATION DETAILS ERROR REPORT ===');
    debugPrint('$_logPrefix: Station UID: $uid');
    debugPrint('$_logPrefix: Error Type: $errorType');
    debugPrint('$_logPrefix: Error Message: $errorMessage');
    debugPrint('$_logPrefix: Timestamp: ${DateTime.now().toIso8601String()}');
    
    if (apiResponse != null) {
      debugPrint('$_logPrefix: API Response Structure:');
      _logResponseStructure(apiResponse);
    }
    
    if (additionalContext != null) {
      debugPrint('$_logPrefix: Additional Context:');
      additionalContext.forEach((key, value) {
        debugPrint('  $key: $value');
      });
    }
    
    // Provide specific troubleshooting guidance
    _provideTroubleshootingGuidance(errorType, errorMessage);
    
    debugPrint('=====================================\n');
  }

  /// Report API validation error with detailed analysis
  static void reportApiValidationError({
    required String endpoint,
    required Map<String, dynamic> response,
    required List<String> validationErrors,
  }) {
    debugPrint('\n=== API VALIDATION ERROR REPORT ===');
    debugPrint('$_logPrefix: Endpoint: $endpoint');
    debugPrint('$_logPrefix: Validation Errors:');
    
    for (int i = 0; i < validationErrors.length; i++) {
      debugPrint('  ${i + 1}. ${validationErrors[i]}');
    }
    
    debugPrint('$_logPrefix: Response Analysis:');
    _analyzeResponseStructure(response);
    
    debugPrint('=====================================\n');
  }

  /// Report connector data issues
  static void reportConnectorDataError({
    required String stationUid,
    required String evseUid,
    required Map<String, dynamic> connectorData,
    required String issue,
  }) {
    debugPrint('\n=== CONNECTOR DATA ERROR REPORT ===');
    debugPrint('$_logPrefix: Station UID: $stationUid');
    debugPrint('$_logPrefix: EVSE UID: $evseUid');
    debugPrint('$_logPrefix: Issue: $issue');
    debugPrint('$_logPrefix: Connector Data:');
    
    _logConnectorData(connectorData);
    
    debugPrint('=====================================\n');
  }

  /// Report UID extraction/validation issues
  static void reportUidIssue({
    required String source,
    required String expectedUidField,
    required Map<String, dynamic> data,
    String? extractedUid,
  }) {
    debugPrint('\n=== UID EXTRACTION ERROR REPORT ===');
    debugPrint('$_logPrefix: Source: $source');
    debugPrint('$_logPrefix: Expected UID Field: $expectedUidField');
    debugPrint('$_logPrefix: Extracted UID: ${extractedUid ?? "NULL"}');
    debugPrint('$_logPrefix: Available Fields:');
    
    data.forEach((key, value) {
      if (key.toLowerCase().contains('uid') || 
          key.toLowerCase().contains('id')) {
        debugPrint('  $key: $value (${value.runtimeType})');
      }
    });
    
    debugPrint('=====================================\n');
  }

  /// Analyze response structure for debugging
  static void _analyzeResponseStructure(Map<String, dynamic> response) {
    debugPrint('$_logPrefix: Response Keys: ${response.keys.toList()}');
    
    if (response.containsKey('success')) {
      debugPrint('$_logPrefix: Success Field: ${response['success']}');
    }
    
    if (response.containsKey('message')) {
      debugPrint('$_logPrefix: Message Field: ${response['message']}');
    }
    
    if (response.containsKey('data')) {
      final data = response['data'];
      if (data is Map<String, dynamic>) {
        debugPrint('$_logPrefix: Data Keys: ${data.keys.toList()}');
        
        // Check for critical station fields
        final criticalFields = ['uid', 'name', 'address', 'latitude', 'longitude', 'evses'];
        for (final field in criticalFields) {
          if (data.containsKey(field)) {
            final value = data[field];
            debugPrint('$_logPrefix: $field: $value (${value.runtimeType})');
          } else {
            debugPrint('$_logPrefix: MISSING FIELD: $field');
          }
        }
      } else {
        debugPrint('$_logPrefix: Data is not a Map: ${data.runtimeType}');
      }
    } else {
      debugPrint('$_logPrefix: MISSING DATA FIELD');
    }
  }

  /// Log response structure for debugging
  static void _logResponseStructure(Map<String, dynamic> response) {
    debugPrint('$_logPrefix: Response Structure:');
    _logMapStructure(response, '  ');
  }

  /// Recursively log map structure
  static void _logMapStructure(Map<String, dynamic> map, String indent) {
    map.forEach((key, value) {
      if (value is Map<String, dynamic>) {
        debugPrint('$indent$key: Map (${value.length} keys)');
        if (value.length <= 5) { // Only expand small maps
          _logMapStructure(value, '$indent  ');
        }
      } else if (value is List) {
        debugPrint('$indent$key: List (${value.length} items)');
        if (value.isNotEmpty && value.length <= 3) {
          for (int i = 0; i < value.length; i++) {
            if (value[i] is Map<String, dynamic>) {
              debugPrint('$indent  [$i]: Map');
            } else {
              debugPrint('$indent  [$i]: ${value[i]} (${value[i].runtimeType})');
            }
          }
        }
      } else {
        debugPrint('$indent$key: $value (${value.runtimeType})');
      }
    });
  }

  /// Log connector data for debugging
  static void _logConnectorData(Map<String, dynamic> connectorData) {
    final importantFields = [
      'type', 'status', 'evses_uid', 'max_electric_power', 
      'price', 'price_label', 'connector_id', 'icon'
    ];
    
    for (final field in importantFields) {
      if (connectorData.containsKey(field)) {
        final value = connectorData[field];
        debugPrint('  $field: $value (${value.runtimeType})');
      } else {
        debugPrint('  MISSING: $field');
      }
    }
  }

  /// Provide troubleshooting guidance based on error type
  static void _provideTroubleshootingGuidance(String errorType, String errorMessage) {
    debugPrint('$_logPrefix: Troubleshooting Guidance:');
    
    switch (errorType.toLowerCase()) {
      case 'validation_error':
        debugPrint('  1. Check if API endpoint is returning complete data');
        debugPrint('  2. Verify all required fields are present in response');
        debugPrint('  3. Check for null or empty values in critical fields');
        break;
        
      case 'network_error':
        debugPrint('  1. Check internet connection');
        debugPrint('  2. Verify API endpoint URL is correct');
        debugPrint('  3. Check if API server is accessible');
        break;
        
      case 'authentication_error':
        debugPrint('  1. Check if auth token is valid');
        debugPrint('  2. Verify token is being sent in request headers');
        debugPrint('  3. Check if user session has expired');
        break;
        
      case 'data_format_error':
        debugPrint('  1. Check if API response format has changed');
        debugPrint('  2. Verify JSON parsing is working correctly');
        debugPrint('  3. Check for unexpected data types in response');
        break;
        
      case 'uid_error':
        debugPrint('  1. Verify UID is being extracted from correct API endpoint');
        debugPrint('  2. Check if UID format is valid (should be UUID)');
        debugPrint('  3. Ensure navigation is using proper UID source');
        break;
        
      default:
        debugPrint('  1. Check error message for specific details');
        debugPrint('  2. Verify API response structure');
        debugPrint('  3. Check application logs for additional context');
    }
  }

  /// Report successful station loading for comparison
  static void reportSuccessfulStationLoad({
    required String uid,
    required Map<String, dynamic> stationData,
  }) {
    if (kDebugMode) {
      debugPrint('\n=== SUCCESSFUL STATION LOAD ===');
      debugPrint('$_logPrefix: Station UID: $uid');
      debugPrint('$_logPrefix: Station Name: ${stationData['name']}');
      debugPrint('$_logPrefix: Station Address: ${stationData['address']}');
      debugPrint('$_logPrefix: EVSEs Count: ${(stationData['evses'] as Map?)?.length ?? 0}');
      debugPrint('===============================\n');
    }
  }
}
