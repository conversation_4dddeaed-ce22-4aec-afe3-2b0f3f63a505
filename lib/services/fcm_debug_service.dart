import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// FCM Debug Service for testing and verifying FCM subscription and data flow
class FCMDebugService {
  static final FCMDebugService _instance = FCMDebugService._internal();
  factory FCMDebugService() => _instance;
  FCMDebugService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  bool _isInitialized = false;
  String? _fcmToken;
  final List<Map<String, dynamic>> _receivedMessages = [];

  /// Initialize FCM debug service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🔍 ===== INITIALIZING FCM DEBUG SERVICE =====');

      // Get FCM token
      _fcmToken = await _firebaseMessaging.getToken();
      debugPrint('🔍 FCM Token: ${_fcmToken?.substring(0, 30)}...');

      // Set up message listeners for debugging
      _setupMessageListeners();

      _isInitialized = true;
      debugPrint('✅ FCM Debug Service initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing FCM Debug Service: $e');
    }
  }

  /// Set up message listeners to capture all FCM messages
  void _setupMessageListeners() {
    // Listen for foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      debugPrint('🔍 ===== FCM FOREGROUND MESSAGE RECEIVED =====');
      _logMessage(message, 'FOREGROUND');
    });

    // Listen for background messages when app is opened from notification
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      debugPrint('🔍 ===== FCM BACKGROUND MESSAGE OPENED =====');
      _logMessage(message, 'BACKGROUND_OPENED');
    });

    // Check for initial message when app is launched from terminated state
    _checkInitialMessage();
  }

  /// Check for initial message when app is launched from notification
  Future<void> _checkInitialMessage() async {
    try {
      final RemoteMessage? initialMessage = await _firebaseMessaging.getInitialMessage();
      if (initialMessage != null) {
        debugPrint('🔍 ===== FCM INITIAL MESSAGE (APP TERMINATED) =====');
        _logMessage(initialMessage, 'TERMINATED_LAUNCH');
      }
    } catch (e) {
      debugPrint('❌ Error checking initial message: $e');
    }
  }

  /// Log FCM message details for debugging
  void _logMessage(RemoteMessage message, String source) {
    final messageData = {
      'messageId': message.messageId,
      'source': source,
      'timestamp': DateTime.now().toIso8601String(),
      'from': message.from,
      'notification': {
        'title': message.notification?.title,
        'body': message.notification?.body,
      },
      'data': message.data,
    };

    _receivedMessages.add(messageData);

    debugPrint('🔍 Message ID: ${message.messageId}');
    debugPrint('🔍 Source: $source');
    debugPrint('🔍 From: ${message.from}');
    debugPrint('🔍 Notification Title: ${message.notification?.title}');
    debugPrint('🔍 Notification Body: ${message.notification?.body}');
    debugPrint('🔍 Data Payload: ${message.data}');

    // Check if this is a charging notification
    _analyzeChargingData(message);

    // Save to local storage for later analysis
    _saveMessageToStorage(messageData);
  }

  /// Analyze charging data in FCM message
  void _analyzeChargingData(RemoteMessage message) {
    final data = message.data;
    
    debugPrint('🔍 ===== CHARGING DATA ANALYSIS =====');
    
    // Check for charging indicators
    final isChargingMessage = data['type'] == 'charging' ||
        data['category'] == 'charging' ||
        data['action'] == 'charging_update' ||
        data.containsKey('soc') ||
        data.containsKey('charge_percentage');
    
    debugPrint('🔍 Is Charging Message: $isChargingMessage');
    
    if (isChargingMessage) {
      // Extract charging data
      final soc = data['soc'] ?? data['charge_percentage'] ?? 'NOT_FOUND';
      final power = data['power'] ?? data['current_power'] ?? 'NOT_FOUND';
      final energy = data['energy'] ?? data['energy_delivered'] ?? 'NOT_FOUND';
      final cost = data['cost'] ?? data['current_price'] ?? 'NOT_FOUND';
      final timer = data['timer'] ?? data['charging_timer'] ?? 'NOT_FOUND';
      final transactionId = data['transaction_id'] ?? data['session_id'] ?? 'NOT_FOUND';
      
      debugPrint('🔍 SOC/Battery: $soc');
      debugPrint('🔍 Power: $power');
      debugPrint('🔍 Energy: $energy');
      debugPrint('🔍 Cost: $cost');
      debugPrint('🔍 Timer: $timer');
      debugPrint('🔍 Transaction ID: $transactionId');
      
      // Check if we have real data or default values
      if (soc == 'NOT_FOUND' || soc == '0' || soc.isEmpty) {
        debugPrint('⚠️ WARNING: No real SOC data found in FCM message');
      } else {
        debugPrint('✅ Real SOC data found: $soc');
      }
      
      if (power == 'NOT_FOUND' || power == '0.0 kW' || power.isEmpty) {
        debugPrint('⚠️ WARNING: No real power data found in FCM message');
      } else {
        debugPrint('✅ Real power data found: $power');
      }
    } else {
      debugPrint('ℹ️ This is not a charging notification');
    }
  }

  /// Save message to local storage for analysis
  Future<void> _saveMessageToStorage(Map<String, dynamic> messageData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final existingMessages = prefs.getStringList('fcm_debug_messages') ?? [];
      existingMessages.add(jsonEncode(messageData));
      
      // Keep only last 50 messages
      if (existingMessages.length > 50) {
        existingMessages.removeAt(0);
      }
      
      await prefs.setStringList('fcm_debug_messages', existingMessages);
    } catch (e) {
      debugPrint('❌ Error saving message to storage: $e');
    }
  }

  /// Test FCM subscription to a specific topic
  Future<bool> testTopicSubscription(String topic) async {
    try {
      debugPrint('🔍 ===== TESTING FCM TOPIC SUBSCRIPTION =====');
      debugPrint('🔍 Topic: $topic');
      debugPrint('🔍 FCM Token: ${_fcmToken?.substring(0, 30)}...');

      await _firebaseMessaging.subscribeToTopic(topic);
      debugPrint('✅ Successfully subscribed to topic: $topic');
      
      // Save subscription for tracking
      final prefs = await SharedPreferences.getInstance();
      final subscriptions = prefs.getStringList('fcm_debug_subscriptions') ?? [];
      if (!subscriptions.contains(topic)) {
        subscriptions.add(topic);
        await prefs.setStringList('fcm_debug_subscriptions', subscriptions);
      }
      
      return true;
    } catch (e) {
      debugPrint('❌ Error subscribing to topic $topic: $e');
      return false;
    }
  }

  /// Get all received messages for analysis
  List<Map<String, dynamic>> getReceivedMessages() {
    return List.from(_receivedMessages);
  }

  /// Get FCM token
  String? getFCMToken() {
    return _fcmToken;
  }

  /// Get subscription status
  Future<Map<String, dynamic>> getSubscriptionStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final subscriptions = prefs.getStringList('fcm_debug_subscriptions') ?? [];
      
      return {
        'fcm_token': _fcmToken,
        'subscribed_topics': subscriptions,
        'messages_received': _receivedMessages.length,
        'last_message_time': _receivedMessages.isNotEmpty 
            ? _receivedMessages.last['timestamp'] 
            : 'No messages received',
      };
    } catch (e) {
      debugPrint('❌ Error getting subscription status: $e');
      return {'error': e.toString()};
    }
  }

  /// Clear debug data
  Future<void> clearDebugData() async {
    try {
      _receivedMessages.clear();
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('fcm_debug_messages');
      await prefs.remove('fcm_debug_subscriptions');
      debugPrint('✅ FCM debug data cleared');
    } catch (e) {
      debugPrint('❌ Error clearing debug data: $e');
    }
  }

  /// Generate debug report
  Future<String> generateDebugReport() async {
    final status = await getSubscriptionStatus();
    final messages = getReceivedMessages();
    
    final report = StringBuffer();
    report.writeln('=== FCM DEBUG REPORT ===');
    report.writeln('Generated: ${DateTime.now().toIso8601String()}');
    report.writeln('');
    report.writeln('FCM Token: ${status['fcm_token']}');
    report.writeln('Subscribed Topics: ${status['subscribed_topics']}');
    report.writeln('Messages Received: ${status['messages_received']}');
    report.writeln('Last Message: ${status['last_message_time']}');
    report.writeln('');
    
    if (messages.isNotEmpty) {
      report.writeln('=== RECENT MESSAGES ===');
      for (final message in messages.take(10)) {
        report.writeln('Message ID: ${message['messageId']}');
        report.writeln('Source: ${message['source']}');
        report.writeln('Time: ${message['timestamp']}');
        report.writeln('Data: ${message['data']}');
        report.writeln('---');
      }
    } else {
      report.writeln('No FCM messages received yet.');
    }
    
    return report.toString();
  }
}
