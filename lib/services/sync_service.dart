import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../core/api/api_service.dart';
import 'connectivity_service.dart';

/// Class representing the current sync status
class SyncStatus {
  final bool isSyncing;
  final int pendingItems;
  final DateTime? lastSyncAttempt;

  SyncStatus({
    required this.isSyncing,
    required this.pendingItems,
    this.lastSyncAttempt,
  });
}

/// Service for handling background synchronization of data to the server
class SyncService {
  // Singleton pattern
  static final SyncService _instance = SyncService._internal();
  factory SyncService() => _instance;
  SyncService._internal();

  // API service for making requests
  final ApiService _apiService = ApiService();

  // Connectivity service for connectivity checks
  final ConnectivityService _connectivityService = ConnectivityService();

  // Keys for storing sync data
  static const String _pendingProfileUpdateKey = 'pending_profile_update';
  static const String _profileDataKey = 'profile_data';
  static const String _lastSyncAttemptKey = 'last_sync_attempt';
  static const String _syncInProgressKey = 'sync_in_progress';
  static const String _userProfilePrefix = 'user_profile_';

  // Timer for periodic sync
  Timer? _syncTimer;
  bool _syncInProgress = false;

  // Stream controller for sync status
  final StreamController<SyncStatus> _syncStatusController =
      StreamController<SyncStatus>.broadcast();

  // Stream of sync status
  Stream<SyncStatus> get syncStatusStream => _syncStatusController.stream;

  /// Initialize the sync service
  Future<void> initialize() async {
    debugPrint('\n=== INITIALIZING SYNC SERVICE ===');

    // Initialize connectivity service
    _connectivityService.initialize();

    // Check if there are any pending updates
    final hasPendingUpdates = await hasPendingProfileUpdates();
    debugPrint('Has pending profile updates: $hasPendingUpdates');

    // Start periodic sync
    _startPeriodicSync();

    // Attempt immediate sync if there are pending updates
    if (hasPendingUpdates) {
      syncProfileUpdates();
    }

    // Emit initial sync status
    _emitSyncStatus();
  }

  /// Get the current sync status
  Future<SyncStatus> getSyncStatus() async {
    final prefs = await SharedPreferences.getInstance();
    final hasPendingUpdates = await hasPendingProfileUpdates();
    final isSyncing = _syncInProgress;

    // Get last sync attempt time if available
    DateTime? lastSyncAttempt;
    final lastSyncStr = prefs.getString(_lastSyncAttemptKey);
    if (lastSyncStr != null) {
      try {
        lastSyncAttempt = DateTime.parse(lastSyncStr);
      } catch (e) {
        debugPrint('Error parsing last sync time: $e');
      }
    }

    return SyncStatus(
      isSyncing: isSyncing,
      pendingItems: hasPendingUpdates ? 1 : 0,
      lastSyncAttempt: lastSyncAttempt,
    );
  }

  /// Emit current sync status to listeners
  Future<void> _emitSyncStatus() async {
    final status = await getSyncStatus();
    _syncStatusController.add(status);
  }

  /// Trigger an immediate sync
  Future<bool> syncNow() async {
    debugPrint('\n=== MANUAL SYNC TRIGGERED ===');
    return await syncProfileUpdates();
  }

  /// Start periodic sync timer
  void _startPeriodicSync() {
    // Cancel existing timer if any
    _syncTimer?.cancel();

    // Create a new timer that runs every 5 minutes
    _syncTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      syncProfileUpdates();
    });

    debugPrint('Periodic sync started (every 5 minutes)');
  }

  /// Stop periodic sync
  void stopPeriodicSync() {
    _syncTimer?.cancel();
    _syncTimer = null;
    debugPrint('Periodic sync stopped');
  }

  /// Check if there are pending profile updates
  Future<bool> hasPendingProfileUpdates() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.containsKey(_pendingProfileUpdateKey) &&
        prefs.getString(_pendingProfileUpdateKey) == 'true';
  }

  /// Queue a profile update for syncing
  Future<void> queueProfileUpdate({
    required String userId,
    required String name,
    required String email,
    String? phone,
  }) async {
    final prefs = await SharedPreferences.getInstance();

    // Save profile data as JSON
    final profileData = {
      'id': userId,
      'name': name,
      'email': email,
      'domain': 'eeil.online',
      if (phone != null && phone.isNotEmpty) 'mobile_number': phone,
    };

    // Save to shared preferences - both in the sync queue and as user-specific data
    await prefs.setString(_profileDataKey, json.encode(profileData));
    await prefs.setString(_pendingProfileUpdateKey, 'true');

    // Also save as user-specific profile data
    await prefs.setString(
        _userProfilePrefix + userId, json.encode(profileData));

    // Update standard profile fields for backward compatibility
    await prefs.setString('user_name', name);
    await prefs.setString('user_email', email);
    await prefs.setString('user_id', userId);
    if (phone != null && phone.isNotEmpty) {
      await prefs.setString('user_phone', phone);
    }

    debugPrint('\n=== QUEUED PROFILE UPDATE ===');
    debugPrint('User ID: $userId');
    debugPrint('Name: $name');
    debugPrint('Email: $email');
    if (phone != null) debugPrint('Phone: $phone');

    // Attempt to sync immediately
    syncProfileUpdates();
  }

  /// Sync profile updates to the server
  Future<bool> syncProfileUpdates() async {
    // Check if there are pending updates
    if (!await hasPendingProfileUpdates()) {
      return true; // No updates to sync
    }

    // Check if sync is already in progress
    if (_syncInProgress) {
      debugPrint('Sync already in progress, skipping');
      return false;
    }

    // Check network connectivity
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      debugPrint('No network connection, skipping sync');
      return false;
    }

    // Set sync in progress flag
    _syncInProgress = true;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_syncInProgressKey, 'true');
    await prefs.setString(
        _lastSyncAttemptKey, DateTime.now().toIso8601String());

    // Emit sync status update
    await _emitSyncStatus();

    debugPrint('\n=== SYNCING PROFILE UPDATE ===');

    try {
      // Get profile data from shared preferences
      final profileDataJson = prefs.getString(_profileDataKey);
      if (profileDataJson == null || profileDataJson.isEmpty) {
        debugPrint('No profile data found, clearing pending flag');
        await prefs.remove(_pendingProfileUpdateKey);
        _syncInProgress = false;
        await prefs.remove(_syncInProgressKey);
        return true;
      }

      // Parse profile data
      final profileData = json.decode(profileDataJson) as Map<String, dynamic>;
      debugPrint('Profile data: $profileData');

      // Check if we have a token
      final token = await _apiService.getToken();
      if (token == null || token.isEmpty) {
        debugPrint('No token available, sync failed');
        _syncInProgress = false;
        await prefs.remove(_syncInProgressKey);
        return false;
      }

      // Try both endpoints for profile update
      bool success = false;

      // Try multiple endpoints with a list of possible profile update endpoints
      final endpoints = [
        '/user/user/update', // Correct endpoint from Postman
        '/user/update',
        '/user/profile/update',
        '/user/profile',
        '/user/update-profile',
      ];

      // Try each endpoint until one succeeds
      for (final endpoint in endpoints) {
        try {
          debugPrint(
              '\n=== TRYING PROFILE UPDATE WITH ENDPOINT: $endpoint ===');

          // Make the API call - use the endpoint directly without adding /api/v1 prefix
          // since the API service already includes the full base URL with /api/v1
          final response = await _apiService.post(
            endpoint,
            data: profileData,
          );

          // Check if the update was successful
          success = response['success'] == true;

          if (success) {
            debugPrint('Profile update successful with endpoint: $endpoint');
            break; // Exit the loop if successful
          } else {
            // If we got a response but it wasn't successful
            final message = response['message'] ?? 'Unknown error';
            debugPrint(
                'Profile update failed with endpoint $endpoint: $message');

            // If this is a 404 error, continue to the next endpoint
            if (message.toString().contains('404')) {
              debugPrint('Endpoint not found (404), trying next endpoint...');
              continue;
            }

            // If it's another type of error, we might want to stop and not try other endpoints
            if (message.toString().contains('unauthorized') ||
                message.toString().contains('invalid token')) {
              debugPrint(
                  'Authorization error, stopping profile update attempts');
              break;
            }
          }
        } catch (e) {
          debugPrint('Error with endpoint $endpoint: $e');

          // If this is a 404 error, continue to the next endpoint
          if (e.toString().contains('404')) {
            debugPrint('Endpoint not found (404), trying next endpoint...');
            continue;
          }
        }
      }

      // If successful, clear pending flag
      if (success) {
        await prefs.remove(_pendingProfileUpdateKey);
        debugPrint('Profile update synced successfully, cleared pending flag');
      } else {
        debugPrint('Profile update sync failed, will retry later');
      }

      return success;
    } catch (e) {
      debugPrint('Error syncing profile update: $e');
      return false;
    } finally {
      // Clear sync in progress flag
      _syncInProgress = false;
      await prefs.remove(_syncInProgressKey);

      // Emit sync status update
      await _emitSyncStatus();
    }
  }

  /// Force sync all pending updates
  Future<bool> forceSyncAll() async {
    debugPrint('\n=== FORCE SYNCING ALL UPDATES ===');
    return await syncProfileUpdates();
  }

  /// Get user profile data
  Future<Map<String, dynamic>?> getUserProfile(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    final profileJson = prefs.getString(_userProfilePrefix + userId);

    if (profileJson != null && profileJson.isNotEmpty) {
      try {
        return json.decode(profileJson) as Map<String, dynamic>;
      } catch (e) {
        debugPrint('Error parsing user profile: $e');
      }
    }

    return null;
  }
}
