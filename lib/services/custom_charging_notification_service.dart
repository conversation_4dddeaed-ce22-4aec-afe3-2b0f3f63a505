import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:io';

/// Custom notification service that creates exact pin bar replica in Android notifications
/// Uses RemoteViews with custom layouts to match the pin bar design perfectly
class CustomChargingNotificationService {
  static final CustomChargingNotificationService _instance =
      CustomChargingNotificationService._internal();
  factory CustomChargingNotificationService() => _instance;
  CustomChargingNotificationService._internal();

  static const MethodChannel _channel = MethodChannel('custom_charging_notification');
  
  bool _isInitialized = false;
  static const int _chargingNotificationId = 1001;

  /// Initialize the custom notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🔔 ===== INITIALIZING CUSTOM CHARGING NOTIFICATION SERVICE =====');
      debugPrint('🔔 Platform.isAndroid: ${Platform.isAndroid}');
      debugPrint('🔔 Method channel: $_channel');

      if (Platform.isAndroid) {
        debugPrint('🔔 Calling initialize method on Android...');
        final result = await _channel.invokeMethod('initialize');
        debugPrint('🔔 Initialize method result: $result');
      } else {
        debugPrint('🔔 Not Android platform, skipping native initialization');
      }

      _isInitialized = true;
      debugPrint('✅ Custom Charging Notification Service initialized successfully');
      debugPrint('✅ _isInitialized = $_isInitialized');
    } catch (e) {
      debugPrint('❌ Error initializing Custom Charging Notification Service: $e');
      debugPrint('❌ Error type: ${e.runtimeType}');
      debugPrint('❌ Error details: ${e.toString()}');
    }
  }

  /// Show custom pin bar style notification with exact design match
  Future<void> showChargingNotification({
    required bool isCharging,
    required double chargePercentage,
    required String currentPower,
    required String energyDelivered,
    required String currentPrice,
    required String co2Saved,
    required String chargingTimer,
    double carbonSavings = 0.0,
    double energySourcePercentage = 85.0,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    if (!Platform.isAndroid) return;

    try {
      debugPrint('🔔 ===== SHOWING CUSTOM PIN BAR NOTIFICATION =====');
      debugPrint('🔔 Platform: ${Platform.isAndroid ? "Android" : "Other"}');
      debugPrint('🔔 Initialized: $_isInitialized');

      // Prepare data for native Android notification with exact pin bar design match
      final notificationData = {
        'notificationId': _chargingNotificationId,
        'isCharging': isCharging,
        'chargePercentage': chargePercentage,
        'currentPower': currentPower,
        'energyDelivered': energyDelivered,
        'currentPrice': currentPrice,
        'co2Saved': co2Saved,
        'chargingTimer': chargingTimer,
        'percentageText': '${(chargePercentage * 100).toInt()}%',
        'powerValue': currentPower.replaceAll(' kW', ''),
        'energyValue': energyDelivered.replaceAll(' kWh', ''),
        'co2Value': carbonSavings > 0 ? carbonSavings.toStringAsFixed(1) : co2Saved.replaceAll(' kg', ''),
        'renewableValue': energySourcePercentage.toStringAsFixed(0),
        'statusText': isCharging ? 'ACTIVE CHARGING' : 'CHARGING COMPLETE',
      };

      debugPrint('🔔 Calling native method with data: $notificationData');
      await _channel.invokeMethod('showCustomNotification', notificationData);

      debugPrint('✅ Custom pin bar notification shown successfully');
    } catch (e) {
      debugPrint('❌ Error showing custom pin bar notification: $e');
    }
  }

  /// Hide the custom notification
  Future<void> hideChargingNotification() async {
    if (!Platform.isAndroid) return;

    try {
      await _channel.invokeMethod('hideNotification', {
        'notificationId': _chargingNotificationId,
      });
      debugPrint('🔔 Custom charging notification hidden');
    } catch (e) {
      debugPrint('❌ Error hiding custom charging notification: $e');
    }
  }

  /// Update notification progress
  Future<void> updateProgress({
    required double chargePercentage,
    required String currentPower,
    required String energyDelivered,
    required String currentPrice,
    required String chargingTimer,
  }) async {
    if (!Platform.isAndroid) return;

    try {
      final updateData = {
        'notificationId': _chargingNotificationId,
        'chargePercentage': chargePercentage,
        'currentPower': currentPower,
        'energyDelivered': energyDelivered,
        'currentPrice': currentPrice,
        'chargingTimer': chargingTimer,
        'percentageText': '${(chargePercentage * 100).toInt()}%',
        'powerValue': currentPower.replaceAll(' kW', ''),
        'energyValue': energyDelivered.replaceAll(' kWh', ''),
      };

      await _channel.invokeMethod('updateNotificationProgress', updateData);
    } catch (e) {
      debugPrint('❌ Error updating notification progress: $e');
    }
  }

  /// Check if custom notifications are supported
  Future<bool> isSupported() async {
    if (!Platform.isAndroid) return false;

    try {
      final result = await _channel.invokeMethod('isSupported');
      return result as bool? ?? false;
    } catch (e) {
      debugPrint('❌ Error checking custom notification support: $e');
      return false;
    }
  }

  /// Request notification permissions
  Future<bool> requestPermissions() async {
    if (!Platform.isAndroid) return true;

    try {
      final result = await _channel.invokeMethod('requestPermissions');
      return result as bool? ?? false;
    } catch (e) {
      debugPrint('❌ Error requesting notification permissions: $e');
      return false;
    }
  }
}
