import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'connectivity_service.dart';

/// Network retry service with intelligent retry logic and exponential backoff
class NetworkRetryService {
  // Singleton pattern
  static final NetworkRetryService _instance = NetworkRetryService._internal();
  factory NetworkRetryService() => _instance;
  NetworkRetryService._internal();

  final ConnectivityService _connectivityService = ConnectivityService();

  // Configuration constants
  static const int _maxRetryAttempts = 3;
  static const Duration _baseDelay = Duration(seconds: 1);
  static const Duration _maxDelay = Duration(seconds: 30);
  static const double _backoffMultiplier = 2.0;
  static const double _jitterFactor = 0.1;

  /// Execute a function with intelligent retry logic
  ///
  /// [operation] - The async operation to execute
  /// [maxAttempts] - Maximum number of retry attempts (default: 3)
  /// [baseDelay] - Base delay between retries (default: 1 second)
  /// [onRetry] - Optional callback called before each retry
  /// [shouldRetry] - Optional function to determine if error should trigger retry
  Future<T> executeWithRetry<T>(
    Future<T> Function() operation, {
    int? maxAttempts,
    Duration? baseDelay,
    Function(int attempt, dynamic error)? onRetry,
    bool Function(dynamic error)? shouldRetry,
  }) async {
    final attempts = maxAttempts ?? _maxRetryAttempts;
    final delay = baseDelay ?? _baseDelay;

    for (int attempt = 1; attempt <= attempts; attempt++) {
      try {
        // Check connectivity before attempting operation
        if (attempt > 1) {
          final hasConnection =
              await _connectivityService.checkConnectionManually();
          if (!hasConnection) {
            debugPrint('🔄 RETRY SERVICE: No connection available, waiting...');
            await _waitForConnection();
          }
        }

        debugPrint(
            '🔄 RETRY SERVICE: Executing operation (attempt $attempt/$attempts)');
        final result = await operation();

        if (attempt > 1) {
          debugPrint(
              '🔄 RETRY SERVICE: Operation succeeded on attempt $attempt');
        }

        return result;
      } catch (error) {
        debugPrint(
            '🔄 RETRY SERVICE: Operation failed on attempt $attempt: $error');

        // Check if we should retry this error
        final shouldRetryError =
            shouldRetry?.call(error) ?? _shouldRetryError(error);

        if (attempt == attempts || !shouldRetryError) {
          debugPrint(
              '🔄 RETRY SERVICE: Max attempts reached or non-retryable error, giving up');
          rethrow;
        }

        // Calculate delay with exponential backoff and jitter
        final retryDelay = _calculateRetryDelay(attempt, delay);

        // Call retry callback if provided
        onRetry?.call(attempt, error);

        debugPrint(
            '🔄 RETRY SERVICE: Retrying in ${retryDelay.inMilliseconds}ms...');
        await Future.delayed(retryDelay);
      }
    }

    throw Exception('Retry service: Unexpected end of retry loop');
  }

  /// Execute operation with connectivity-aware retry
  /// Waits for connection to be restored before retrying
  Future<T> executeWithConnectivityRetry<T>(
    Future<T> Function() operation, {
    int? maxAttempts,
    Duration? baseDelay,
    Duration? connectionTimeout,
    Function(int attempt, dynamic error)? onRetry,
  }) async {
    return executeWithRetry<T>(
      operation,
      maxAttempts: maxAttempts,
      baseDelay: baseDelay,
      onRetry: onRetry,
      shouldRetry: (error) => _isConnectivityError(error),
    );
  }

  /// Wait for connection to be restored
  Future<void> _waitForConnection({Duration? timeout}) async {
    final maxWait = timeout ?? const Duration(minutes: 2);
    final completer = Completer<void>();
    Timer? timeoutTimer;
    StreamSubscription? statusSubscription;

    try {
      // Set up timeout
      timeoutTimer = Timer(maxWait, () {
        if (!completer.isCompleted) {
          completer.completeError(
              TimeoutException('Connection wait timeout', maxWait));
        }
      });

      // Listen for connection restoration
      statusSubscription =
          _connectivityService.connectionStatus.listen((status) {
        if (status == ConnectionStatus.connected && !completer.isCompleted) {
          debugPrint('🔄 RETRY SERVICE: Connection restored');
          completer.complete();
        }
      });

      // Check current status immediately
      if (_connectivityService.hasConnection) {
        debugPrint('🔄 RETRY SERVICE: Connection already available');
        completer.complete();
      }

      await completer.future;
    } finally {
      timeoutTimer?.cancel();
      statusSubscription?.cancel();
    }
  }

  /// Calculate retry delay with exponential backoff and jitter
  Duration _calculateRetryDelay(int attempt, Duration baseDelay) {
    // Exponential backoff: delay = baseDelay * (backoffMultiplier ^ (attempt - 1))
    final exponentialDelay =
        baseDelay.inMilliseconds * pow(_backoffMultiplier, attempt - 1).toInt();

    // Add jitter to prevent thundering herd
    final jitter =
        exponentialDelay * _jitterFactor * (Random().nextDouble() - 0.5);
    final finalDelay = exponentialDelay + jitter.toInt();

    // Cap at maximum delay
    final cappedDelay = min(finalDelay, _maxDelay.inMilliseconds);

    return Duration(milliseconds: cappedDelay.toInt());
  }

  /// Determine if an error should trigger a retry
  bool _shouldRetryError(dynamic error) {
    if (error == null) return false;

    final errorString = error.toString().toLowerCase();

    // Network-related errors that should trigger retry
    final retryableErrors = [
      'timeout',
      'connection',
      'network',
      'socket',
      'handshake',
      'failed host lookup',
      'no route to host',
      'connection refused',
      'connection reset',
      'broken pipe',
      'operation timed out',
    ];

    return retryableErrors
        .any((retryableError) => errorString.contains(retryableError));
  }

  /// Check if error is connectivity-related
  bool _isConnectivityError(dynamic error) {
    if (error == null) return false;

    final errorString = error.toString().toLowerCase();

    final connectivityErrors = [
      'no internet',
      'connection',
      'network',
      'timeout',
      'socket',
      'failed host lookup',
      'no route to host',
    ];

    return connectivityErrors
        .any((connectivityError) => errorString.contains(connectivityError));
  }

  /// Execute operation with circuit breaker pattern
  /// Prevents overwhelming the network when it's consistently failing
  Future<T> executeWithCircuitBreaker<T>(
    Future<T> Function() operation, {
    int? failureThreshold,
    Duration? recoveryTimeout,
  }) async {
    // This is a simplified circuit breaker implementation
    // In a production app, you might want a more sophisticated implementation

    final threshold = failureThreshold ?? 5;
    final timeout = recoveryTimeout ?? const Duration(minutes: 1);

    // For now, just use the regular retry mechanism
    // A full circuit breaker would track failure rates and open/close the circuit
    return executeWithRetry<T>(operation);
  }

  /// Batch retry operations with intelligent scheduling
  Future<List<T>> executeBatchWithRetry<T>(
    List<Future<T> Function()> operations, {
    int? maxConcurrent,
    Duration? delayBetweenBatches,
  }) async {
    final concurrent = maxConcurrent ?? 3;
    final delay = delayBetweenBatches ?? const Duration(milliseconds: 100);

    final results = <T>[];

    for (int i = 0; i < operations.length; i += concurrent) {
      final batch = operations.skip(i).take(concurrent);

      final batchFutures =
          batch.map((operation) => executeWithRetry<T>(operation)).toList();

      final batchResults = await Future.wait(batchFutures);
      results.addAll(batchResults);

      // Add delay between batches to prevent overwhelming the network
      if (i + concurrent < operations.length) {
        await Future.delayed(delay);
      }
    }

    return results;
  }

  /// Get retry statistics for monitoring
  Map<String, dynamic> getRetryStats() {
    // In a production implementation, you would track retry statistics
    return {
      'service': 'NetworkRetryService',
      'status': 'active',
      'connectivity_status': _connectivityService.currentStatus.toString(),
      'connectivity_quality': _connectivityService.currentQuality.toString(),
    };
  }
}
