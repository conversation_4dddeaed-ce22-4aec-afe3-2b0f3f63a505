import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:ecoplug/services/notification_manager.dart';

/// Authentication Notification Service
/// Handles notification triggers based on authentication events
/// Integrates welcome notifications with login success flow
class AuthNotificationService {
  static final AuthNotificationService _instance =
      AuthNotificationService._internal();
  factory AuthNotificationService() => _instance;
  AuthNotificationService._internal();

  final NotificationManager _notificationManager = NotificationManager();

  static const String _firstLoginKey = 'user_first_login_completed';
  static const String _lastLoginKey = 'user_last_login_time';
  static const String _loginCountKey = 'user_login_count';
  static const String _currentSessionKey = 'current_login_session';
  static const String _lastWelcomeSessionKey = 'last_welcome_session';

  /// Initialize the auth notification service
  Future<void> initialize() async {
    try {
      debugPrint('🔐 ===== INITIALIZING AUTH NOTIFICATION SERVICE =====');

      // Initialize notification manager
      await _notificationManager.initialize();

      debugPrint('✅ Auth Notification Service initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing Auth Notification Service: $e');
      rethrow;
    }
  }

  /// Handle successful login event
  /// This should be called immediately after successful authentication
  Future<void> onLoginSuccess({
    required String userId,
    String? userName,
    String? userEmail,
  }) async {
    try {
      debugPrint('🔐 ===== HANDLING LOGIN SUCCESS =====');
      debugPrint('🔐 User ID: $userId');
      debugPrint('🔐 User Name: $userName');
      debugPrint('🔐 User Email: $userEmail');

      // Generate unique session ID for this login
      final sessionId = '${userId}_${DateTime.now().millisecondsSinceEpoch}';
      debugPrint('🔐 Session ID: $sessionId');

      // Check if this is the first login
      final isFirstLogin = await _isFirstLogin(userId);
      debugPrint('🔐 Is First Login: $isFirstLogin');

      // Check if we should show welcome notification for this session
      final shouldShowWelcome =
          await _shouldShowWelcomeForSession(userId, sessionId);
      debugPrint('🔐 Should Show Welcome: $shouldShowWelcome');

      // Update login tracking BEFORE showing notification
      await _updateLoginTracking(userId, sessionId);

      // Show welcome notification (with error isolation)
      if (shouldShowWelcome) {
        await _showWelcomeNotificationForLogin(
          userId: userId,
          userName: userName,
          isFirstLogin: isFirstLogin,
          sessionId: sessionId,
        );
      } else {
        debugPrint('🔐 Welcome notification skipped for this session');
      }

      // Subscribe to user-specific notification topics (non-blocking)
      _subscribeToUserNotifications(userId).catchError((e) {
        debugPrint('⚠️ Non-critical error subscribing to notifications: $e');
      });

      debugPrint('✅ Login success handling completed');
    } catch (e) {
      debugPrint('❌ Error handling login success: $e');
      debugPrint('❌ Stack trace: ${StackTrace.current}');
      // Don't rethrow - login should succeed even if notifications fail
    }
  }

  /// Check if we should show welcome notification for this session
  Future<bool> _shouldShowWelcomeForSession(
      String userId, String sessionId) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Get current session
      final currentSession = prefs.getString(_currentSessionKey);

      // Get last welcome session
      final lastWelcomeSession = prefs.getString(_lastWelcomeSessionKey);

      // Always show for new sessions
      if (currentSession != sessionId) {
        debugPrint('🔐 New session detected - showing welcome notification');
        return true;
      }

      // Don't show if already shown for this session
      if (lastWelcomeSession == sessionId) {
        debugPrint('🔐 Welcome already shown for this session');
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('❌ Error checking welcome session: $e');
      // Default to showing notification on error
      return true;
    }
  }

  /// Show welcome notification for login
  Future<void> _showWelcomeNotificationForLogin({
    required String userId,
    String? userName,
    required bool isFirstLogin,
    required String sessionId,
  }) async {
    try {
      debugPrint('🔐 ===== SHOWING WELCOME NOTIFICATION FOR LOGIN =====');
      debugPrint('🔐 User ID: $userId');
      debugPrint('🔐 User Name: $userName');
      debugPrint('🔐 Is First Login: $isFirstLogin');
      debugPrint('🔐 Session ID: $sessionId');

      // Ensure notification manager is initialized
      await _notificationManager.initialize();

      // Add a small delay to ensure the app is fully loaded and stable
      await Future.delayed(const Duration(milliseconds: 300));

      // Show the welcome notification (local notification)
      await _notificationManager.showWelcomeNotification(
        userName: userName,
        isFirstLogin: isFirstLogin,
      );

      // Mark this session as having shown welcome notification
      await _markWelcomeShownForSession(sessionId);

      debugPrint('✅ Welcome notification shown for login successfully');
    } catch (e) {
      debugPrint('❌ Error showing welcome notification for login: $e');
      debugPrint('❌ Stack trace: ${StackTrace.current}');
      // Don't rethrow - login should succeed even if notification fails
    }
  }

  /// Mark welcome notification as shown for this session
  Future<void> _markWelcomeShownForSession(String sessionId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastWelcomeSessionKey, sessionId);
      debugPrint(
          '🔐 Welcome notification marked as shown for session: $sessionId');
    } catch (e) {
      debugPrint('❌ Error marking welcome shown for session: $e');
    }
  }

  /// Subscribe user to notification topics after login
  Future<void> _subscribeToUserNotifications(String userId) async {
    try {
      debugPrint('🔐 Subscribing user to notification topics...');

      // Subscribe to user-specific topics - Only charging-related
      await _notificationManager.subscribeUserToDefaultTopics(userId, {
        'charging_updates': true,
      });

      // Subscribe to test_1 topic using FirebaseMessaging.instance singleton pattern
      await _subscribeToTestTopic();

      // Send FCM token to backend
      await _notificationManager.sendTokenToBackend(userId);

      debugPrint('✅ User subscribed to notification topics');
    } catch (e) {
      debugPrint('❌ Error subscribing user to notifications: $e');
    }
  }

  /// Subscribe to test_1 FCM topic with comprehensive debugging and tracking
  Future<void> _subscribeToTestTopic() async {
    const topicName = 'all';
    final startTime = DateTime.now();

    try {
      debugPrint('🔔 ===== FCM TEST_1 TOPIC SUBSCRIPTION START =====');
      debugPrint('🔔 Timestamp: ${startTime.toIso8601String()}');
      debugPrint('🔔 Topic Name: $topicName');
      debugPrint('🔔 Process: Starting FCM topic subscription...');

      // Check if Firebase Messaging is available
      debugPrint('🔔 Step 1: Checking Firebase Messaging availability...');
      final messaging = FirebaseMessaging.instance;
      debugPrint('🔔 ✅ Firebase Messaging instance obtained');

      // Get current FCM token for debugging
      debugPrint('🔔 Step 2: Getting FCM token for verification...');
      try {
        final token = await messaging.getToken();
        if (token != null) {
          debugPrint('🔔 ✅ FCM Token available: ${token.substring(0, 20)}...');
        } else {
          debugPrint('🔔 ⚠️ FCM Token is null - this may affect subscription');
        }
      } catch (tokenError) {
        debugPrint('🔔 ⚠️ Error getting FCM token: $tokenError');
      }

      // Check current subscription status before attempting
      debugPrint('🔔 Step 3: Checking existing subscription status...');
      final existingStatus = await getTestTopicSubscriptionStatus();
      debugPrint('🔔 Existing status: $existingStatus');

      // Attempt FCM topic subscription with detailed logging
      debugPrint('🔔 Step 4: Attempting FCM topic subscription...');
      debugPrint(
          '🔔 Calling: FirebaseMessaging.instance.subscribeToTopic("$topicName")');
      debugPrint('🔔 Timeout: 15 seconds');

      final subscriptionStartTime = DateTime.now();

      await messaging.subscribeToTopic(topicName).timeout(
        const Duration(seconds: 15),
        onTimeout: () {
          final timeoutDuration =
              DateTime.now().difference(subscriptionStartTime);
          debugPrint(
              '🔔 ❌ TIMEOUT: FCM subscription timeout after ${timeoutDuration.inSeconds} seconds');
          throw Exception(
              'FCM topic subscription timeout for $topicName after 15 seconds');
        },
      );

      final subscriptionEndTime = DateTime.now();
      final subscriptionDuration =
          subscriptionEndTime.difference(subscriptionStartTime);

      debugPrint('🔔 ✅ FCM subscription call completed successfully');
      debugPrint(
          '🔔 Subscription duration: ${subscriptionDuration.inMilliseconds}ms');

      // Verify subscription success
      debugPrint('🔔 Step 5: Verifying subscription success...');
      await _verifyTopicSubscription(topicName);

      // Save successful subscription status with detailed tracking
      debugPrint('🔔 Step 6: Saving subscription status...');
      await _saveTestTopicSubscriptionStatus(true, {
        'subscription_time': subscriptionEndTime.millisecondsSinceEpoch,
        'subscription_duration_ms': subscriptionDuration.inMilliseconds,
        'fcm_token_available': await messaging.getToken() != null,
        'retry_attempt': false,
        'error_occurred': false,
      });

      final totalDuration = DateTime.now().difference(startTime);
      debugPrint('🔔 ===== FCM TEST_1 TOPIC SUBSCRIPTION SUCCESS =====');
      debugPrint('🔔 ✅ Successfully subscribed to FCM topic: $topicName');
      debugPrint(
          '🔔 ✅ Total process duration: ${totalDuration.inMilliseconds}ms');
      debugPrint('🔔 ✅ Subscription verified and status saved');
      debugPrint('🔔 ===== SUBSCRIPTION PROCESS COMPLETE =====');
    } catch (e) {
      final errorTime = DateTime.now();
      final errorDuration = errorTime.difference(startTime);

      debugPrint('🔔 ===== FCM TEST_1 TOPIC SUBSCRIPTION ERROR =====');
      debugPrint('🔔 ❌ Error occurred at: ${errorTime.toIso8601String()}');
      debugPrint('🔔 ❌ Time since start: ${errorDuration.inMilliseconds}ms');
      debugPrint('🔔 ❌ Error type: ${e.runtimeType}');
      debugPrint('🔔 ❌ Error message: $e');
      debugPrint('🔔 ❌ Stack trace: ${StackTrace.current}');

      // Attempt retry with comprehensive logging
      debugPrint('🔔 ===== STARTING RETRY ATTEMPT =====');
      await _retryTopicSubscription(topicName, e);
    }
  }

  /// Retry FCM topic subscription with detailed logging
  Future<void> _retryTopicSubscription(
      String topicName, dynamic originalError) async {
    try {
      debugPrint('🔔 🔄 RETRY: Starting retry attempt for $topicName');
      debugPrint('🔔 🔄 Original error: $originalError');
      debugPrint('🔔 🔄 Waiting 3 seconds before retry...');

      await Future.delayed(const Duration(seconds: 3));

      debugPrint('🔔 🔄 Retry delay completed, attempting subscription...');
      final retryStartTime = DateTime.now();

      await FirebaseMessaging.instance.subscribeToTopic(topicName).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          debugPrint('🔔 🔄 ❌ RETRY TIMEOUT: Subscription retry timed out');
          throw Exception(
              'FCM topic subscription retry timeout for $topicName');
        },
      );

      final retryEndTime = DateTime.now();
      final retryDuration = retryEndTime.difference(retryStartTime);

      debugPrint('🔔 🔄 ✅ RETRY SUCCESS: Subscription retry completed');
      debugPrint('🔔 🔄 ✅ Retry duration: ${retryDuration.inMilliseconds}ms');

      // Verify retry success
      await _verifyTopicSubscription(topicName);

      // Save successful retry status
      await _saveTestTopicSubscriptionStatus(true, {
        'subscription_time': retryEndTime.millisecondsSinceEpoch,
        'subscription_duration_ms': retryDuration.inMilliseconds,
        'fcm_token_available':
            await FirebaseMessaging.instance.getToken() != null,
        'retry_attempt': true,
        'original_error': originalError.toString(),
        'error_occurred': false,
      });

      debugPrint(
          '🔔 🔄 ✅ RETRY COMPLETE: test_1 FCM topic subscription retry successful');
    } catch (retryError) {
      debugPrint('🔔 🔄 ❌ RETRY FAILED: Subscription retry failed');
      debugPrint('🔔 🔄 ❌ Retry error type: ${retryError.runtimeType}');
      debugPrint('🔔 🔄 ❌ Retry error message: $retryError');

      // Save failed status with comprehensive error details
      await _saveTestTopicSubscriptionStatus(false, {
        'subscription_time': DateTime.now().millisecondsSinceEpoch,
        'fcm_token_available':
            await FirebaseMessaging.instance.getToken() != null,
        'retry_attempt': true,
        'original_error': originalError.toString(),
        'retry_error': retryError.toString(),
        'error_occurred': true,
        'final_status': 'failed_after_retry',
      });

      debugPrint(
          '🔔 🔄 ❌ FINAL FAILURE: Both initial and retry attempts failed');
      debugPrint('🔔 🔄 ❌ Error details saved for debugging');
    }
  }

  /// Verify FCM topic subscription success
  Future<void> _verifyTopicSubscription(String topicName) async {
    try {
      debugPrint(
          '🔔 🔍 VERIFY: Starting subscription verification for $topicName');

      // Note: FCM doesn't provide a direct way to verify topic subscription
      // We'll implement indirect verification methods

      // Check 1: Verify FCM token is still available
      final token = await FirebaseMessaging.instance.getToken();
      if (token != null) {
        debugPrint('🔔 🔍 ✅ VERIFY: FCM token is available for subscription');
      } else {
        debugPrint(
            '🔔 🔍 ⚠️ VERIFY: FCM token is null - subscription may not be effective');
      }

      // Check 2: Verify Firebase Messaging is supported
      final isSupported = await FirebaseMessaging.instance.isSupported();
      debugPrint('🔔 🔍 FCM supported: $isSupported');

      // Check 3: Log subscription attempt completion
      debugPrint('🔔 🔍 ✅ VERIFY: Subscription verification completed');
      debugPrint(
          '🔔 🔍 Note: FCM topic subscription is asynchronous and may take time to propagate');
    } catch (e) {
      debugPrint('🔔 🔍 ❌ VERIFY: Error during subscription verification: $e');
    }
  }

  /// Save test_1 topic subscription status with comprehensive tracking data
  Future<void> _saveTestTopicSubscriptionStatus(bool isSubscribed,
      [Map<String, dynamic>? additionalData]) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      // Save basic subscription status
      await prefs.setBool('test_1_topic_subscribed', isSubscribed);
      await prefs.setInt('test_1_subscription_time', timestamp);

      // Save additional tracking data if provided
      if (additionalData != null) {
        debugPrint(
            '🔔 💾 SAVE: Saving comprehensive subscription tracking data...');

        // Save subscription duration if available
        if (additionalData['subscription_duration_ms'] != null) {
          await prefs.setInt('test_1_subscription_duration_ms',
              additionalData['subscription_duration_ms']);
        }

        // Save FCM token availability status
        if (additionalData['fcm_token_available'] != null) {
          await prefs.setBool('test_1_fcm_token_available',
              additionalData['fcm_token_available']);
        }

        // Save retry attempt status
        if (additionalData['retry_attempt'] != null) {
          await prefs.setBool(
              'test_1_retry_attempt', additionalData['retry_attempt']);
        }

        // Save error status
        if (additionalData['error_occurred'] != null) {
          await prefs.setBool(
              'test_1_error_occurred', additionalData['error_occurred']);
        }

        // Save error details as strings
        if (additionalData['original_error'] != null) {
          await prefs.setString(
              'test_1_original_error', additionalData['original_error']);
        }

        if (additionalData['retry_error'] != null) {
          await prefs.setString(
              'test_1_retry_error', additionalData['retry_error']);
        }

        if (additionalData['final_status'] != null) {
          await prefs.setString(
              'test_1_final_status', additionalData['final_status']);
        }

        // Use custom subscription time if provided
        if (additionalData['subscription_time'] != null) {
          await prefs.setInt(
              'test_1_subscription_time', additionalData['subscription_time']);
        }

        debugPrint(
            '🔔 💾 ✅ SAVE: Comprehensive tracking data saved successfully');
        debugPrint('🔔 💾 Data keys saved: ${additionalData.keys.toList()}');
      }

      debugPrint('🔔 💾 SAVE: Basic subscription status saved: $isSubscribed');
      debugPrint(
          '🔔 💾 SAVE: Timestamp: ${DateTime.fromMillisecondsSinceEpoch(timestamp).toIso8601String()}');
    } catch (e) {
      debugPrint(
          '🔔 💾 ❌ SAVE: Error saving test topic subscription status: $e');
      debugPrint('🔔 💾 ❌ SAVE: Error type: ${e.runtimeType}');
    }
  }

  /// Check if this is the user's first login
  Future<bool> _isFirstLogin(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final firstLoginKey = '${_firstLoginKey}_$userId';

      final hasLoggedInBefore = prefs.getBool(firstLoginKey) ?? false;
      return !hasLoggedInBefore;
    } catch (e) {
      debugPrint('❌ Error checking first login status: $e');
      // Default to false (not first login) on error
      return false;
    }
  }

  /// Update login tracking information
  Future<void> _updateLoginTracking(String userId, String sessionId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now().millisecondsSinceEpoch;

      // Mark first login as completed
      final firstLoginKey = '${_firstLoginKey}_$userId';
      await prefs.setBool(firstLoginKey, true);

      // Update last login time
      final lastLoginKey = '${_lastLoginKey}_$userId';
      await prefs.setInt(lastLoginKey, now);

      // Increment login count
      final loginCountKey = '${_loginCountKey}_$userId';
      final currentCount = prefs.getInt(loginCountKey) ?? 0;
      await prefs.setInt(loginCountKey, currentCount + 1);

      // Update current session
      await prefs.setString(_currentSessionKey, sessionId);

      debugPrint(
          '🔐 Login tracking updated - count: ${currentCount + 1}, session: $sessionId');
    } catch (e) {
      debugPrint('❌ Error updating login tracking: $e');
    }
  }

  /// Handle logout event
  Future<void> onLogout({required String userId}) async {
    try {
      debugPrint('🔐 ===== HANDLING LOGOUT =====');
      debugPrint('🔐 User ID: $userId');

      // Clear welcome notification if still showing
      await _notificationManager.clearWelcomeNotification();

      // Clear session data
      await _clearSessionData();

      // Unsubscribe from user-specific topics (non-blocking)
      _notificationManager.unsubscribeUserFromAllTopics(userId).catchError((e) {
        debugPrint(
            '⚠️ Non-critical error unsubscribing from notifications: $e');
      });

      debugPrint('✅ Logout handling completed');
    } catch (e) {
      debugPrint('❌ Error handling logout: $e');
      // Don't rethrow - logout should succeed even if notifications fail
    }
  }

  /// Clear session data on logout
  Future<void> _clearSessionData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_currentSessionKey);
      await prefs.remove(_lastWelcomeSessionKey);
      debugPrint('🔐 Session data cleared');
    } catch (e) {
      debugPrint('❌ Error clearing session data: $e');
    }
  }

  /// Get login statistics for a user
  Future<Map<String, dynamic>> getLoginStats(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final firstLoginKey = '${_firstLoginKey}_$userId';
      final lastLoginKey = '${_lastLoginKey}_$userId';
      final loginCountKey = '${_loginCountKey}_$userId';

      final hasLoggedInBefore = prefs.getBool(firstLoginKey) ?? false;
      final lastLoginTime = prefs.getInt(lastLoginKey) ?? 0;
      final loginCount = prefs.getInt(loginCountKey) ?? 0;

      return {
        'user_id': userId,
        'has_logged_in_before': hasLoggedInBefore,
        'last_login_time': lastLoginTime > 0
            ? DateTime.fromMillisecondsSinceEpoch(lastLoginTime)
                .toIso8601String()
            : null,
        'login_count': loginCount,
        'is_first_time_user': !hasLoggedInBefore,
      };
    } catch (e) {
      debugPrint('❌ Error getting login stats: $e');
      return {'error': e.toString()};
    }
  }

  /// Get welcome notification statistics
  Future<Map<String, dynamic>> getWelcomeNotificationStats() async {
    return await _notificationManager.getWelcomeStats();
  }

  /// Get comprehensive test_1 topic subscription status for debugging
  Future<Map<String, dynamic>> getTestTopicSubscriptionStatus() async {
    try {
      debugPrint(
          '🔔 📊 STATUS: Retrieving comprehensive subscription status...');

      final prefs = await SharedPreferences.getInstance();

      // Basic subscription data
      final isSubscribed = prefs.getBool('test_1_topic_subscribed') ?? false;
      final subscriptionTime = prefs.getInt('test_1_subscription_time') ?? 0;

      // Performance tracking data
      final subscriptionDuration =
          prefs.getInt('test_1_subscription_duration_ms');

      // FCM token availability
      final fcmTokenAvailable = prefs.getBool('test_1_fcm_token_available');

      // Retry and error tracking
      final retryAttempt = prefs.getBool('test_1_retry_attempt');
      final errorOccurred = prefs.getBool('test_1_error_occurred');
      final originalError = prefs.getString('test_1_original_error');
      final retryError = prefs.getString('test_1_retry_error');
      final finalStatus = prefs.getString('test_1_final_status');

      final status = {
        'topic_name': 'test_1',
        'is_subscribed': isSubscribed,
        'subscription_time': subscriptionTime > 0
            ? DateTime.fromMillisecondsSinceEpoch(subscriptionTime)
                .toIso8601String()
            : null,
        'subscription_timestamp': subscriptionTime,
        'subscription_duration_ms': subscriptionDuration,
        'fcm_token_available': fcmTokenAvailable,
        'retry_attempt': retryAttempt,
        'error_occurred': errorOccurred,
        'original_error': originalError,
        'retry_error': retryError,
        'final_status': finalStatus,
        'status_retrieved_at': DateTime.now().toIso8601String(),
      };

      debugPrint('🔔 📊 ✅ STATUS: Comprehensive status retrieved successfully');
      debugPrint('🔔 📊 Subscription status: $isSubscribed');
      debugPrint('🔔 📊 Error occurred: ${errorOccurred ?? false}');
      debugPrint('🔔 📊 Retry attempted: ${retryAttempt ?? false}');

      return status;
    } catch (e) {
      debugPrint(
          '🔔 📊 ❌ STATUS: Error getting test topic subscription status: $e');
      debugPrint('🔔 📊 ❌ STATUS: Error type: ${e.runtimeType}');

      return {
        'topic_name': 'test_1',
        'error': e.toString(),
        'error_type': e.runtimeType.toString(),
        'status_retrieved_at': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Reset login tracking for a user (debug mode only)
  Future<void> resetLoginTracking(String userId) async {
    if (kDebugMode) {
      try {
        final prefs = await SharedPreferences.getInstance();

        final firstLoginKey = '${_firstLoginKey}_$userId';
        final lastLoginKey = '${_lastLoginKey}_$userId';
        final loginCountKey = '${_loginCountKey}_$userId';

        await prefs.remove(firstLoginKey);
        await prefs.remove(lastLoginKey);
        await prefs.remove(loginCountKey);

        // Also reset welcome notification tracking
        await _notificationManager.resetWelcomeTracking();

        debugPrint('🔐 Login tracking reset for user: $userId');
      } catch (e) {
        debugPrint('❌ Error resetting login tracking: $e');
      }
    }
  }

  /// Test welcome notification (debug mode only)
  Future<void> testWelcomeNotification({
    String? userName,
    bool isFirstLogin = false,
  }) async {
    if (kDebugMode) {
      try {
        debugPrint('🔐 Testing welcome notification...');

        await _notificationManager.showWelcomeNotification(
          userName: userName,
          isFirstLogin: isFirstLogin,
        );

        debugPrint('✅ Test welcome notification sent');
      } catch (e) {
        debugPrint('❌ Error testing welcome notification: $e');
      }
    }
  }

  /// Test FCM test_1 topic subscription (debug mode only)
  Future<void> testFCMTopicSubscription() async {
    if (kDebugMode) {
      try {
        debugPrint('🔔 Testing FCM test_1 topic subscription...');

        await _subscribeToTestTopic();

        final status = await getTestTopicSubscriptionStatus();
        debugPrint('🔔 Test topic subscription status: $status');

        debugPrint('✅ Test FCM topic subscription completed');
      } catch (e) {
        debugPrint('❌ Error testing FCM topic subscription: $e');
      }
    }
  }

  /// Clear test_1 topic subscription tracking data (debug mode only)
  Future<void> clearTestTopicSubscriptionData() async {
    if (kDebugMode) {
      try {
        debugPrint(
            '🔔 🧹 CLEAR: Clearing test_1 topic subscription tracking data...');

        final prefs = await SharedPreferences.getInstance();

        // List of all test_1 related keys
        final keysToRemove = [
          'test_1_topic_subscribed',
          'test_1_subscription_time',
          'test_1_subscription_duration_ms',
          'test_1_fcm_token_available',
          'test_1_retry_attempt',
          'test_1_error_occurred',
          'test_1_original_error',
          'test_1_retry_error',
          'test_1_final_status',
        ];

        for (final key in keysToRemove) {
          await prefs.remove(key);
          debugPrint('🔔 🧹 Removed key: $key');
        }

        debugPrint('🔔 🧹 ✅ CLEAR: All test_1 topic subscription data cleared');
        debugPrint('🔔 🧹 Keys cleared: ${keysToRemove.length}');
      } catch (e) {
        debugPrint(
            '🔔 🧹 ❌ CLEAR: Error clearing test topic subscription data: $e');
      }
    }
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    return await _notificationManager.areNotificationsEnabled();
  }

  /// Get comprehensive auth notification status
  Future<Map<String, dynamic>> getAuthNotificationStatus() async {
    try {
      final notificationStatus = _notificationManager.getServiceStatus();
      final welcomeStats = await getWelcomeNotificationStats();
      final testTopicStatus = await getTestTopicSubscriptionStatus();

      return {
        'auth_notification_service': 'initialized',
        'notification_services': notificationStatus,
        'welcome_stats': welcomeStats,
        'test_topic_subscription': testTopicStatus,
        'notifications_enabled': await areNotificationsEnabled(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('❌ Error getting auth notification status: $e');
      return {'error': e.toString()};
    }
  }
}
