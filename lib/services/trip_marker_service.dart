import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

/// Service for creating custom trip location markers (start/destination pins)
class TripMarkerService {
  static const double _markerWidth = 56.0;
  static const double _markerHeight = 72.0;

  /// Create a custom start location pin marker (green)
  Future<BitmapDescriptor> createStartLocationMarker() async {
    return await _createLocationPin(
      color: const Color(0xFF4CAF50), // Green
      label: 'START',
      iconData: Icons.play_arrow_rounded,
    );
  }

  /// Create a custom destination location pin marker (red)
  Future<BitmapDescriptor> createDestinationMarker() async {
    return await _createLocationPin(
      color: const Color(0xFFF44336), // Red
      label: 'DEST',
      iconData: Icons.location_on_rounded,
    );
  }

  /// Create a simple location pin without complex decorations
  Future<BitmapDescriptor> _createLocationPin({
    required Color color,
    required String label,
    required IconData iconData,
  }) async {
    try {
      final ui.PictureRecorder recorder = ui.PictureRecorder();
      final Canvas canvas = Canvas(recorder);

      // Draw simple circle marker
      await _drawSimpleCircle(canvas, color);

      // Draw the icon inside the circle
      await _drawSimpleIcon(canvas, iconData, Colors.white);

      // Convert to image
      final ui.Image img = await recorder.endRecording().toImage(
        _markerWidth.toInt(),
        _markerHeight.toInt(),
      );

      final ByteData? data = await img.toByteData(format: ui.ImageByteFormat.png);

      if (data == null) {
        throw Exception('Failed to create marker image data');
      }

      return BitmapDescriptor.bytes(data.buffer.asUint8List());
    } catch (e) {
      debugPrint('❌ TRIP MARKER: Error creating location pin: $e');
      // Fallback to simple marker
      return await _createSimpleFallbackMarker(color);
    }
  }

  /// Draw simple circle marker
  Future<void> _drawSimpleCircle(Canvas canvas, Color color) async {
    final double centerX = _markerWidth / 2;
    final double centerY = _markerHeight / 2;
    final double radius = 20.0;

    // Draw shadow
    final Paint shadowPaint = Paint()
      ..color = Colors.black.withValues(alpha: 0.3)
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2.0);

    canvas.drawCircle(
      Offset(centerX + 2, centerY + 2),
      radius,
      shadowPaint,
    );

    // Draw circle fill
    final Paint fillPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    canvas.drawCircle(
      Offset(centerX, centerY),
      radius,
      fillPaint,
    );

    // Draw circle border
    final Paint borderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3.0;

    canvas.drawCircle(
      Offset(centerX, centerY),
      radius,
      borderPaint,
    );
  }

  /// Draw simple icon inside the circle
  Future<void> _drawSimpleIcon(Canvas canvas, IconData iconData, Color iconColor) async {
    final double centerX = _markerWidth / 2;
    final double centerY = _markerHeight / 2;
    final double iconSize = 18.0;

    // Create text painter for the icon
    final TextPainter textPainter = TextPainter(
      text: TextSpan(
        text: String.fromCharCode(iconData.codePoint),
        style: TextStyle(
          fontSize: iconSize,
          fontFamily: iconData.fontFamily,
          color: iconColor,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        centerX - textPainter.width / 2,
        centerY - textPainter.height / 2,
      ),
    );
  }

  /// Create a simple fallback marker if custom creation fails
  Future<BitmapDescriptor> _createSimpleFallbackMarker(Color color) async {
    try {
      final ui.PictureRecorder recorder = ui.PictureRecorder();
      final Canvas canvas = Canvas(recorder);

      final Paint paint = Paint()
        ..color = color
        ..style = PaintingStyle.fill;

      // Draw simple circle
      canvas.drawCircle(
        Offset(_markerWidth / 2, _markerHeight / 2),
        20,
        paint,
      );

      // Draw border
      final Paint borderPaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.stroke
        ..strokeWidth = 3;

      canvas.drawCircle(
        Offset(_markerWidth / 2, _markerHeight / 2),
        20,
        borderPaint,
      );

      final ui.Image img = await recorder.endRecording().toImage(
        _markerWidth.toInt(),
        _markerHeight.toInt(),
      );

      final ByteData? data = await img.toByteData(format: ui.ImageByteFormat.png);

      if (data != null) {
        return BitmapDescriptor.bytes(data.buffer.asUint8List());
      }
    } catch (e) {
      debugPrint('❌ TRIP MARKER: Error creating fallback marker: $e');
    }

    // Ultimate fallback
    return BitmapDescriptor.defaultMarkerWithHue(
      color == const Color(0xFF4CAF50)
          ? BitmapDescriptor.hueGreen
          : BitmapDescriptor.hueRed,
    );
  }

  /// Create a marker for the given location type
  Future<BitmapDescriptor> createLocationMarker({
    required bool isStartLocation,
    String? locationName,
  }) async {
    if (isStartLocation) {
      return await createStartLocationMarker();
    } else {
      return await createDestinationMarker();
    }
  }
}
