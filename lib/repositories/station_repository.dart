import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart' show debugPrint;
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../core/api/api_config.dart';
import '../models/api_response.dart';
import '../models/station/station_models.dart' hide Review;
import '../models/station/station_marker_response.dart';
import '../models/station/station_details_response.dart';
import '../models/station/paginated_stations_response.dart';
import '../models/nearest_station_response.dart';
import '../core/api/api_service.dart'; // Use centralized API service
import '../utils/api_exception.dart';
import '../services/station_data_cache.dart';
import '../models/review.dart';

import '../utils/connector_icon_debugger.dart';

// Using ConnectorDetail from station_models.dart

/// Repository for station-related API calls
class StationRepository {
  final ApiService _apiService = ApiService(); // Use centralized API service
  final StationDataCache _stationDataCache = StationDataCache();

  /// Get all station markers for map
  Future<ApiResponse<List<StationMarker>>> getMarkers() async {
    try {
      final response = await _apiService.get(ApiConfig.markers);

      final List<StationMarker> markers = (response['data'] as List<dynamic>)
          .map((e) => StationMarker.fromJson(e as Map<String, dynamic>))
          .toList();

      return ApiResponse<List<StationMarker>>(
        success: true,
        message: 'Markers fetched successfully',
        data: markers,
      );
    } on ApiException catch (e) {
      return ApiResponse<List<StationMarker>>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      return ApiResponse<List<StationMarker>>(
        success: false,
        message: 'Failed to fetch markers: $e',
      );
    }
  }

  /// Get station markers from the new API endpoint with optimized loading
  Future<ApiResponse<List<StationMarkerData>>> getStationMarkers() async {
    // Check if user is logged in first
    final isLoggedIn = await _apiService.isLoggedIn();
    if (!isLoggedIn) {
      debugPrint(
          'User is not logged in. Please log in to view station markers.');
      return ApiResponse<List<StationMarkerData>>(
        success: false,
        message: 'Please log in to view station markers.',
        data: [], // Empty list
      );
    }

    try {
      // Make a single API call with increased timeout for reliability
      final response = await _apiService.get(
        ApiConfig.apiUrl + ApiConfig.markers,
        // Use customOptions parameter for longer timeouts
        // Removed customOptions as centralized ApiService handles timeouts
      );

      // Parse the response using the StationMarkerResponse model
      debugPrint('🔍 Raw API response: $response');
      debugPrint('🔍 Response type: ${response.runtimeType}');
      debugPrint('🔍 Response keys: ${response.keys.join(', ')}');

      late StationMarkerResponse stationMarkerResponse;
      try {
        stationMarkerResponse = StationMarkerResponse.fromJson(response);
        debugPrint('✅ Successfully parsed station marker response');
      } catch (parseError) {
        debugPrint('❌ Error parsing station marker response: $parseError');

        // Try to extract data directly if parsing fails
        if (response['data'] != null && response['data'] is List) {
          final List<dynamic> rawData = response['data'];
          final List<StationMarkerData> markers = [];

          for (int i = 0; i < rawData.length; i++) {
            try {
              final markerData = rawData[i] as Map<String, dynamic>;
              final marker = StationMarkerData.fromJson(markerData);
              markers.add(marker);
            } catch (markerError) {
              // Continue with next marker
            }
          }

          if (markers.isNotEmpty) {
            return ApiResponse<List<StationMarkerData>>(
              success: true,
              message:
                  "Successfully loaded station markers (direct extraction)",
              data: markers,
            );
          }
        }

        // If all parsing fails, return error
        throw Exception('Failed to parse station marker data: $parseError');
      }

      // If we have data, use it regardless of status
      if (stationMarkerResponse.data.isNotEmpty) {
        final markers = stationMarkerResponse.data;
        debugPrint('Successfully fetched ${markers.length} station markers');

        // Cache the successful response for future use
        await _stationDataCache.saveStationMarkers(markers);

        return ApiResponse<List<StationMarkerData>>(
          success: true,
          message: "Successfully loaded station markers",
          data: markers,
        );
      }

      // If we have no data, check for cached data
      final cachedMarkers = await _stationDataCache.getCachedStationMarkers();
      if (cachedMarkers != null && cachedMarkers.isNotEmpty) {
        debugPrint('Using ${cachedMarkers.length} cached station markers');
        return ApiResponse<List<StationMarkerData>>(
          success: true,
          message: 'Using cached station markers',
          data: cachedMarkers,
        );
      }

      // If all else fails, return an error response
      return ApiResponse<List<StationMarkerData>>(
        success: false,
        message: 'Failed to load station data. Please try again later.',
        data: [],
      );
    } catch (e) {
      debugPrint('Error fetching station markers: $e');

      // Try to use cached data on API error
      final cachedMarkers = await _stationDataCache.getCachedStationMarkers();
      if (cachedMarkers != null && cachedMarkers.isNotEmpty) {
        debugPrint(
            'Using ${cachedMarkers.length} cached station markers due to API error');
        return ApiResponse<List<StationMarkerData>>(
          success: true,
          message: 'Using cached station markers',
          data: cachedMarkers,
        );
      }

      // If no cached data, return error
      return ApiResponse<List<StationMarkerData>>(
        success: false,
        message: 'Error loading station data: $e',
        data: [],
      );
    }
  }

  /// Get nearest stations based on location with optional filtering
  Future<ApiResponse<List<NearestStation>>> getNearestStations(
      double latitude, double longitude,
      {double radius = 10.0,
      String? powerOutput,
      List<String>? connectorStandards}) async {
    try {
      // Build query parameters
      Map<String, String> queryParams = {
        'lat': latitude.toString(),
        'lng': longitude.toString(),
        'radius': radius.toString(),
      };

      // Add power output filter if specified
      if (powerOutput != null && powerOutput != 'All') {
        queryParams['power_output'] = powerOutput;
      }

      // Add connector standards filter if specified
      if (connectorStandards != null && connectorStandards.isNotEmpty) {
        queryParams['standard'] = connectorStandards.join(',');
      }

      // Use the correct parameter names (lat/lng) as shown in the URL
      final response = await _apiService.get(
        ApiConfig.nearestStations,
        queryParams: queryParams,
      );

      // Debug the raw response to check for connector icons
      debugPrint(
          'Checking nearest stations API response for connector icons...');
      ConnectorIconDebugger.traceConnectorIcons(response,
          source: 'NearestStationsResponse');

      // Check if the response is successful
      if (response['success'] == true && response['data'] != null) {
        final List<dynamic> rawStations = response['data'] as List<dynamic>;

        // Enhanced logging to understand the API response structure
        if (rawStations.isNotEmpty) {
          final Map<String, dynamic> firstStation =
              rawStations[0] as Map<String, dynamic>;
          debugPrint(
              'First nearest station fields: ${firstStation.keys.join(', ')}');

          // Check for ID field which might be used as UID
          if (firstStation.containsKey('id')) {
            debugPrint(
                'Nearest station API uses "id" field which will be used as UID');
          }
        }

        // Before parsing, check if UIDs are present in the data
        for (var i = 0; i < rawStations.length; i++) {
          final Map<String, dynamic> stationData =
              rawStations[i] as Map<String, dynamic>;
          debugPrint(
              'Nearest station $i - UID: ${stationData['uid']}, station_id: ${stationData['station_id']}');

          // Verify that the UID field exists and is not empty
          if (stationData['uid'] == null ||
              stationData['uid'].toString().isEmpty) {
            debugPrint(
                'WARNING: Nearest station $i is missing UID field. This station will be skipped.');
          }
        }

        final List<NearestStation> stations = rawStations
            .map((e) => NearestStation.fromJson(e as Map<String, dynamic>))
            .toList();

        // Debug the parsed stations to check if connector icons are preserved
        if (stations.isNotEmpty) {
          debugPrint('Checking parsed nearest stations for connector icons...');
          ConnectorIconDebugger.traceConnectorIcons(
              stations.map((s) => s.toJson()).toList(),
              source: 'ParsedNearestStations');

          // Also check if UIDs were properly extracted
          for (var i = 0; i < stations.length; i++) {
            debugPrint(
                'Nearest station $i UID after parsing: ${stations[i].uid}');
          }
        }

        return ApiResponse<List<NearestStation>>(
          success: true,
          message:
              response['message'] ?? 'Nearest stations fetched successfully',
          data: stations,
        );
      } else {
        return ApiResponse<List<NearestStation>>(
          success: false,
          message: response['message'] ?? 'Failed to fetch nearest stations',
          data: [],
        );
      }
    } on ApiException catch (e) {
      return ApiResponse<List<NearestStation>>(
        success: false,
        message: e.message,
        data: [],
      );
    } catch (e) {
      return ApiResponse<List<NearestStation>>(
        success: false,
        message: 'Failed to fetch nearest stations: $e',
        data: [],
      );
    }
  }

  /// Get station details by ID
  Future<ApiResponse<StationDetail>> getStationDetail(String stationId) async {
    try {
      final response = await _apiService.get(
        '${ApiConfig.stationDetail}/$stationId', // Use station detail endpoint
      );

      final StationDetail station = StationDetail.fromJson(response['data']);

      return ApiResponse<StationDetail>(
        success: true,
        message: 'Station details fetched successfully',
        data: station,
      );
    } on ApiException catch (e) {
      return ApiResponse<StationDetail>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      return ApiResponse<StationDetail>(
        success: false,
        message: 'Failed to fetch station details: $e',
      );
    }
  }

  /// Get station details by UID using the new API endpoint
  Future<ApiResponse<StationDetailsResponse>> getStationDetailsByUid(
      String uid) async {
    // Debug print to track UID in the repository
    debugPrint(
        'StationRepository: getStationDetailsByUid called with UID: "$uid"');

    if (uid.isEmpty) {
      debugPrint(
          'StationRepository: UID is empty, cannot fetch station details');
      return ApiResponse<StationDetailsResponse>(
        success: false,
        message: 'Cannot fetch station details: UID is empty',
      );
    }

    try {
      // This endpoint should always use real API data, not mock data
      debugPrint(
          'StationRepository: Making API call to fetch station details with UID: "$uid"');

      // IMPORTANT: The correct endpoint for station details by UID is /user/stations/details
      // NOT /user/station/details (singular) which is a different endpoint
      final fullEndpoint =
          'https://api2.eeil.online/api/v1/user/stations/details?uid=$uid';
      debugPrint(
          'StationRepository: Using DIRECT full endpoint: $fullEndpoint');

      // Make a direct HTTP request to ensure we're using the exact endpoint
      final http.Client client = http.Client();

      try {
        // Get the auth token using SharedPreferences directly for reliability
        final prefs = await SharedPreferences.getInstance();
        final token = prefs.getString('auth_token');

        if (token == null || token.isEmpty) {
          debugPrint('StationRepository: No auth token available');
          return ApiResponse<StationDetailsResponse>(
            success: false,
            message: 'Authentication token is missing',
          );
        }

        // Create headers with auth token
        final headers = {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        };

        debugPrint(
            'StationRepository: Making direct HTTP request with auth token');
        final response = await client
            .get(
              Uri.parse(fullEndpoint),
              headers: headers,
            )
            .timeout(const Duration(seconds: 30));

        debugPrint(
            'StationRepository: Response status code: ${response.statusCode}');

        if (response.statusCode == 200) {
          final jsonData = jsonDecode(response.body);
          debugPrint('StationRepository: Response body: ${response.body}');

          // Parse the response using the StationDetailsResponse model
          final stationDetailsResponse =
              StationDetailsResponse.fromJson(jsonData);

          // Log the parsed response
          debugPrint(
              'StationRepository: Parsed response success: ${stationDetailsResponse.success}');
          if (stationDetailsResponse.data != null) {
            debugPrint(
                'StationRepository: Station name: ${stationDetailsResponse.data?.name}');
            debugPrint(
                'StationRepository: Station address: ${stationDetailsResponse.data?.address}');
            debugPrint(
                'StationRepository: Station EVSEs count: ${stationDetailsResponse.data?.evses?.length ?? 0}');
          } else {
            debugPrint('StationRepository: Parsed response data is null');
          }

          return ApiResponse<StationDetailsResponse>(
            success: stationDetailsResponse.success ?? false,
            message:
                stationDetailsResponse.message ?? 'Station details fetched',
            data: stationDetailsResponse,
          );
        } else {
          debugPrint('StationRepository: Error response: ${response.body}');
          return ApiResponse<StationDetailsResponse>(
            success: false,
            message:
                'Failed to fetch station details: HTTP ${response.statusCode}',
          );
        }
      } finally {
        client.close();
      }
    } on ApiException catch (e) {
      debugPrint('StationRepository: API Exception: ${e.message}');
      return ApiResponse<StationDetailsResponse>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('StationRepository: Exception: $e');
      return ApiResponse<StationDetailsResponse>(
        success: false,
        message: 'Failed to fetch station details: $e',
      );
    }
  }

  /// Get station details by ID using the new API format
  Future<ApiResponse<StationDetailsResponse>> getStationDetailsById(
      String stationId) async {
    try {
      debugPrint('Fetching detailed information for station ID: $stationId');

      // Use the API service to fetch station details
      final responseData = await _apiService.fetchStationDetails(stationId);

      // Debug the raw response to check for connector icons
      debugPrint('Checking raw API response for connector icons...');
      ConnectorIconDebugger.traceConnectorIcons(responseData,
          source: 'StationDetailsRawResponse');

      // Parse the response using the StationDetailsResponse model
      final stationDetailsResponse =
          StationDetailsResponse.fromJson(responseData);

      debugPrint(
          'Successfully fetched station details with ${stationDetailsResponse.data?.evses?.length ?? 0} EVSEs');

      // Debug the parsed response to check if connector icons are preserved
      ConnectorIconDebugger.traceConnectorIcons(
          stationDetailsResponse.data?.toJson() ?? {},
          source: 'ParsedStationDetailsData');

      return ApiResponse<StationDetailsResponse>(
        success: stationDetailsResponse.success ?? false,
        message: stationDetailsResponse.message ??
            'Station details fetched successfully',
        data: stationDetailsResponse,
      );
    } on ApiException catch (e) {
      debugPrint('API Exception when fetching station details: ${e.message}');
      return ApiResponse<StationDetailsResponse>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error fetching station details: $e');
      return ApiResponse<StationDetailsResponse>(
        success: false,
        message: 'Failed to fetch station details: $e',
      );
    }
  }

  /// Search stations by query
  Future<ApiResponse<List<NearestStation>>> searchStations(String query) async {
    try {
      debugPrint('Searching stations with query: "$query"');
      final response = await _apiService.get(
        ApiConfig.stationSearch,
        queryParams: {'search': query},
      );

      // Debug log the raw response to check for UIDs
      debugPrint('Search API raw response: ${jsonEncode(response)}');

      // Check if the response is successful
      if (response['success'] == true && response['data'] != null) {
        final List<dynamic> rawStations = response['data'] as List<dynamic>;
        debugPrint('Found ${rawStations.length} stations in search response');

        // Enhanced logging to understand the API response structure
        if (rawStations.isNotEmpty) {
          final Map<String, dynamic> firstStation =
              rawStations[0] as Map<String, dynamic>;
          debugPrint('First station fields: ${firstStation.keys.join(', ')}');

          // Check for ID field which might be used as UID
          if (firstStation.containsKey('id')) {
            debugPrint('API uses "id" field which will be used as UID');
          }
        }

        // Before parsing, check if UIDs are present in the data
        for (var i = 0; i < rawStations.length; i++) {
          final Map<String, dynamic> stationData =
              rawStations[i] as Map<String, dynamic>;

          // Verify that the UID field exists and is not empty
          if (stationData['uid'] == null ||
              stationData['uid'].toString().isEmpty) {
            // Station will be skipped during parsing
          }
        }

        List<NearestStation> stations = rawStations
            .map((e) => NearestStation.fromJson(e as Map<String, dynamic>))
            .toList();

        final List<NearestStation> validStations = [];
        for (var i = 0; i < stations.length; i++) {
          NearestStation station = stations[i];

          // Only include stations with valid UIDs
          if (station.uid.isNotEmpty) {
            validStations.add(station);
          } else {
            debugPrint('Skipping station $i with missing UID: ${station.name}');
          }
        }

        // Replace the original stations list with the valid ones
        stations = validStations;

        return ApiResponse<List<NearestStation>>(
          success: true,
          message: response['message'] ?? 'Stations searched successfully',
          data: stations,
        );
      } else {
        debugPrint('Search API failed: ${response['message']}');
        return ApiResponse<List<NearestStation>>(
          success: false,
          message: response['message'] ?? 'Failed to search stations',
          data: [],
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during search: ${e.message}');
      return ApiResponse<List<NearestStation>>(
        success: false,
        message: e.message,
        data: [],
      );
    } catch (e) {
      debugPrint('Error during search: $e');
      return ApiResponse<List<NearestStation>>(
        success: false,
        message: 'Failed to search stations: $e',
        data: [],
      );
    }
  }

  /// Get paginated stations with optional filtering
  Future<ApiResponse<PaginatedStationsResponse>> getPaginatedStations(
      int page, int limit,
      {double? latitude,
      double? longitude,
      String? powerOutput,
      List<String>? connectorStandards}) async {
    try {
      // Build query parameters for the API call
      final Map<String, String> queryParams = {
        'page': page.toString(),
        'limit': limit.toString(),
      };

      // Add location parameters if available
      if (latitude != null && longitude != null) {
        queryParams['latitude'] = latitude.toString();
        queryParams['longitude'] = longitude.toString();
        debugPrint(
            'StationRepository: Adding location params - latitude: $latitude, longitude: $longitude');
      } else {
        debugPrint('StationRepository: No location parameters provided');
      }

      // Add power output filter if specified and not 'All'
      if (powerOutput != null && powerOutput != 'All') {
        queryParams['power_output'] = powerOutput;
        debugPrint(
            'StationRepository: Adding power output filter: $powerOutput');
      }

      // Add connector standards filter if specified
      if (connectorStandards != null && connectorStandards.isNotEmpty) {
        queryParams['standard'] = connectorStandards.join(',');
        debugPrint(
            'StationRepository: Adding connector standards filter: $connectorStandards');
      }

      // Debug log the complete query parameters
      debugPrint('StationRepository: Final query params: $queryParams');

      // Construct and log the complete URL for comparison with Postman
      final baseUrl = '${ApiConfig.apiUrl}${ApiConfig.stationPaginate}';
      final queryString = queryParams.entries
          .map((e) =>
              '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
          .join('&');
      final completeUrl = '$baseUrl?$queryString';
      debugPrint(
          '🌐 StationRepository: Complete URL being called: $completeUrl');

      // Use the direct API call with query parameters
      final response = await _apiService.get(
        ApiConfig.stationPaginate,
        queryParams: queryParams,
      );

      // Parse the response using the PaginatedStationsResponse model
      final paginatedStationsResponse =
          PaginatedStationsResponse.fromJson(response);

      // Enhanced logging for debugging data discrepancies
      debugPrint(
          '📊 StationRepository: Response success: ${paginatedStationsResponse.success}');
      debugPrint(
          '📊 StationRepository: Response message: ${paginatedStationsResponse.message}');
      debugPrint(
          '📊 StationRepository: Data count: ${paginatedStationsResponse.data?.length ?? 0}');
      debugPrint(
          '📊 StationRepository: Current page: ${paginatedStationsResponse.currentPage}');
      debugPrint(
          '📊 StationRepository: Total pages: ${paginatedStationsResponse.totalPages}');

      // Log detailed station information for debugging
      if (paginatedStationsResponse.data != null &&
          paginatedStationsResponse.data!.isNotEmpty) {
        debugPrint(
            '📊 StationRepository: Total stations parsed: ${paginatedStationsResponse.data!.length}');

        // Log each station's UID and ID for debugging
        for (int i = 0; i < paginatedStationsResponse.data!.length; i++) {
          final station = paginatedStationsResponse.data![i];
          debugPrint(
              '📊 StationRepository: Station $i - Name: ${station.name}, UID: "${station.uid}", Station ID: ${station.stationId}');
        }
      } else {
        debugPrint('📊 StationRepository: No stations in parsed response');
      }

      return ApiResponse<PaginatedStationsResponse>(
        success: paginatedStationsResponse.success ?? false,
        message: paginatedStationsResponse.message ??
            'Paginated stations fetched successfully',
        data: paginatedStationsResponse,
      );
    } on ApiException catch (e) {
      return ApiResponse<PaginatedStationsResponse>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      return ApiResponse<PaginatedStationsResponse>(
        success: false,
        message: 'Failed to fetch paginated stations: $e',
      );
    }
  }

  /// Get reviews for a station
  Future<ApiResponse<List<Review>>> getReviews(String stationId) async {
    try {
      final response = await _apiService.get(
        ApiConfig.getReviews,
        queryParams: {'stationId': stationId},
      );

      final List<Review> reviews = (response['data'] as List<dynamic>)
          .map((e) => Review.fromJson(e as Map<String, dynamic>))
          .toList();

      return ApiResponse<List<Review>>(
        success: true,
        message: 'Reviews fetched successfully',
        data: reviews,
      );
    } on ApiException catch (e) {
      return ApiResponse<List<Review>>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      return ApiResponse<List<Review>>(
        success: false,
        message: 'Failed to fetch reviews: $e',
      );
    }
  }

  /// Save a review for a station
  Future<ApiResponse<Review>> saveReview(
      String stationId, double rating, String comment) async {
    try {
      final response = await _apiService.post(
        ApiConfig.saveReview,
        data: {
          'stationId': stationId,
          'rating': rating,
          'comment': comment,
        },
      );

      final Review review = Review.fromJson(response['data']);

      return ApiResponse<Review>(
        success: true,
        message: 'Review saved successfully',
        data: review,
      );
    } on ApiException catch (e) {
      return ApiResponse<Review>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      return ApiResponse<Review>(
        success: false,
        message: 'Failed to save review: $e',
      );
    }
  }

  /// Save a bookmark for a station
  Future<ApiResponse<bool>> saveBookmark(String stationId) async {
    try {
      await _apiService.post(
        ApiConfig.saveBookmark,
        data: {'stationId': stationId},
      );

      return ApiResponse<bool>(
        success: true,
        message: 'Bookmark saved successfully',
        data: true,
      );
    } on ApiException catch (e) {
      return ApiResponse<bool>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      return ApiResponse<bool>(
        success: false,
        message: 'Failed to save bookmark: $e',
      );
    }
  }

  /// Get bookmarked stations
  Future<ApiResponse<List<StationDetail>>> getBookmarkedStations() async {
    try {
      final response = await _apiService.get(ApiConfig.getBookmarks);

      final List<StationDetail> stations = (response['data'] as List<dynamic>)
          .map((e) => StationDetail.fromJson(e as Map<String, dynamic>))
          .toList();

      return ApiResponse<List<StationDetail>>(
        success: true,
        message: 'Bookmarked stations fetched successfully',
        data: stations,
      );
    } on ApiException catch (e) {
      return ApiResponse<List<StationDetail>>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      return ApiResponse<List<StationDetail>>(
        success: false,
        message: 'Failed to fetch bookmarked stations: $e',
      );
    }
  }
}
