import 'package:flutter/foundation.dart';
import '../models/api_response.dart';
import '../models/wallet/wallet_response.dart';
import '../core/api/api_service.dart'; // Use centralized API service
import '../utils/api_exception.dart';

/// Repository for wallet-related API calls
class WalletRepositoryImpl {
  final ApiService _apiService; // Use centralized API service

  WalletRepositoryImpl(this._apiService); // Use centralized API service

  /// Get wallet information including transaction history with enhanced error handling
  /// This method always fetches fresh data from the API.
  Future<ApiResponse<WalletResponse>> getWalletInfo() async {
    try {
      debugPrint('Fetching wallet data from API...');
      // Make the API call with enhanced retry logic
      final response = await _apiService.get('/user/wallet/info');

      final walletResponse = WalletResponse.fromJson(response);

      debugPrint('Successfully fetched wallet data from API');

      return ApiResponse<WalletResponse>(
        success: walletResponse.success ?? false,
        message: 'Wallet info fetched successfully',
        data: walletResponse,
      );
    } on ApiException catch (e) {
      debugPrint('API Exception in getWalletInfo: ${e.message}');
      return ApiResponse<WalletResponse>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error in getWalletInfo: $e');
      return ApiResponse<WalletResponse>(
        success: false,
        message: 'Network error. Please try again.',
      );
    }
  }

  /// Add money to wallet (placeholder for future implementation)
  Future<ApiResponse<bool>> addMoneyToWallet(
      double amount, String paymentMethod) async {
    try {
      // This would be implemented when the add money API is available
      // For now, return a placeholder response
      return ApiResponse<bool>(
        success: false,
        message: 'Add money functionality will be implemented soon',
        data: false,
      );
    } catch (e) {
      debugPrint('Error in addMoneyToWallet: $e');
      return ApiResponse<bool>(
        success: false,
        message: 'Failed to add money to wallet: $e',
      );
    }
  }
}
