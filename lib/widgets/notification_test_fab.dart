import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../utils/notification_test_utility.dart';

/// Debug Floating Action Button for testing notifications
/// Only visible in debug mode
/// Can be added to any screen to test notification functionality
class NotificationTestFAB extends StatelessWidget {
  const NotificationTestFAB({super.key});

  @override
  Widget build(BuildContext context) {
    // Only show in debug mode
    if (!kDebugMode) {
      return const SizedBox.shrink();
    }

    return Positioned(
      bottom: 100,
      right: 16,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Test Permission Request Button
          FloatingActionButton(
            heroTag: "permission_test",
            mini: true,
            backgroundColor: Colors.orange,
            onPressed: () async {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('🔔 Testing notification permissions...'),
                  duration: Duration(seconds: 2),
                ),
              );
              await NotificationTestUtility.testPermissionRequest();
            },
            child: const Icon(Icons.security, color: Colors.white),
          ),
          const SizedBox(height: 8),

          // Test Welcome Notification Button
          FloatingActionButton(
            heroTag: "welcome_test",
            mini: true,
            backgroundColor: Colors.green,
            onPressed: () async {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('🎉 Testing welcome notification...'),
                  duration: Duration(seconds: 2),
                ),
              );
              await NotificationTestUtility.testWelcomeNotification();
            },
            child: const Icon(Icons.waving_hand, color: Colors.white),
          ),
          const SizedBox(height: 8),

          // Test Charging Notification Button
          FloatingActionButton(
            heroTag: "charging_test",
            mini: true,
            backgroundColor: Colors.blue,
            onPressed: () async {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('🔋 Testing charging notification...'),
                  duration: Duration(seconds: 2),
                ),
              );
              await NotificationTestUtility.testChargingNotification();
            },
            child: const Icon(Icons.battery_charging_full, color: Colors.white),
          ),
          const SizedBox(height: 8),

          // Test All Notifications Button
          FloatingActionButton(
            heroTag: "all_test",
            mini: true,
            backgroundColor: Colors.purple,
            onPressed: () async {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('🧪 Testing all notifications...'),
                  duration: Duration(seconds: 3),
                ),
              );
              await NotificationTestUtility.testAllNotifications();
            },
            child: const Icon(Icons.notifications_active, color: Colors.white),
          ),
          const SizedBox(height: 8),

          // Test FCM Service Button
          FloatingActionButton(
            heroTag: "fcm_test",
            mini: true,
            backgroundColor: Colors.red,
            onPressed: () async {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('🔥 Testing FCM service...'),
                  duration: Duration(seconds: 2),
                ),
              );
              await NotificationTestUtility.testFCMService();
            },
            child: const Icon(Icons.cloud_queue, color: Colors.white),
          ),
          const SizedBox(height: 8),

          // Test All Systems Button
          FloatingActionButton(
            heroTag: "all_systems_test",
            mini: true,
            backgroundColor: Colors.indigo,
            onPressed: () async {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('🧪 Testing all notification systems...'),
                  duration: Duration(seconds: 4),
                ),
              );
              await NotificationTestUtility.testAllNotificationSystems();
            },
            child: const Icon(Icons.settings_system_daydream, color: Colors.white),
          ),
        ],
      ),
    );
  }
}

/// Simple debug button for testing notifications
/// Can be added to any screen's app bar or body
class NotificationTestButton extends StatelessWidget {
  final String label;
  final VoidCallback? onPressed;

  const NotificationTestButton({
    super.key,
    this.label = 'Test Notifications',
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    // Only show in debug mode
    if (!kDebugMode) {
      return const SizedBox.shrink();
    }

    return ElevatedButton.icon(
      onPressed: onPressed ?? () async {
        await NotificationTestUtility.testAllNotifications();
      },
      icon: const Icon(Icons.notifications_active),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
    );
  }
}
