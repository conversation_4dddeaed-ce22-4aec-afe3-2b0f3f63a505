import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/messaging_service.dart';

/// Widget to integrate messaging service following the tutorial pattern
/// Provides UI to test FCM functionality and display token information
class MessagingIntegrationWidget extends StatefulWidget {
  const MessagingIntegrationWidget({super.key});

  @override
  State<MessagingIntegrationWidget> createState() => _MessagingIntegrationWidgetState();
}

class _MessagingIntegrationWidgetState extends State<MessagingIntegrationWidget> {
  final MessagingService _messagingService = MessagingService();
  
  bool _isInitialized = false;
  String? _fcmToken;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeMessaging();
  }

  /// Initialize messaging service
  Future<void> _initializeMessaging() async {
    try {
      await _messagingService.init();
      setState(() {
        _isInitialized = _messagingService.isInitialized;
        _fcmToken = _messagingService.fcmToken;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to initialize messaging: $e';
      });
    }
  }

  /// Copy FCM token to clipboard
  void _copyTokenToClipboard() {
    if (_fcmToken != null) {
      Clipboard.setData(ClipboardData(text: _fcmToken!));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('FCM Token copied to clipboard'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  /// Subscribe to a test topic
  Future<void> _subscribeToTestTopic() async {
    try {
      await _messagingService.subscribeToTopic('test_topic');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Subscribed to test_topic'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to subscribe: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Unsubscribe from test topic
  Future<void> _unsubscribeFromTestTopic() async {
    try {
      await _messagingService.unsubscribeFromTopic('test_topic');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Unsubscribed from test_topic'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to unsubscribe: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.message,
                  color: _isInitialized ? Colors.green : Colors.grey,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Firebase Messaging Integration',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Status
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _isInitialized ? Colors.green.shade50 : Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _isInitialized ? Colors.green : Colors.red,
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    _isInitialized ? Icons.check_circle : Icons.error,
                    color: _isInitialized ? Colors.green : Colors.red,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _isInitialized 
                        ? 'Messaging Service Initialized' 
                        : 'Messaging Service Not Initialized',
                    style: TextStyle(
                      color: _isInitialized ? Colors.green.shade700 : Colors.red.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            
            if (_errorMessage != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red, width: 1),
                ),
                child: Text(
                  'Error: $_errorMessage',
                  style: TextStyle(color: Colors.red.shade700),
                ),
              ),
            ],
            
            const SizedBox(height: 16),
            
            // FCM Token Section
            const Text(
              'FCM Token:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _fcmToken != null 
                        ? '${_fcmToken!.substring(0, 50)}...' 
                        : 'Token not available',
                    style: const TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      ElevatedButton.icon(
                        onPressed: _fcmToken != null ? _copyTokenToClipboard : null,
                        icon: const Icon(Icons.copy, size: 16),
                        label: const Text('Copy Token'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton.icon(
                        onPressed: _initializeMessaging,
                        icon: const Icon(Icons.refresh, size: 16),
                        label: const Text('Refresh'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Topic Subscription Section
            const Text(
              'Topic Subscription Test:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isInitialized ? _subscribeToTestTopic : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Subscribe to Test Topic'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isInitialized ? _unsubscribeFromTestTopic : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Unsubscribe'),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Instructions
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Testing Instructions:',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Colors.blue.shade700,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '1. Copy the FCM token above\n'
                    '2. Use Firebase Console to send a test notification\n'
                    '3. Or ask your backend team to send a test message\n'
                    '4. Subscribe to test_topic to receive topic-based notifications',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
