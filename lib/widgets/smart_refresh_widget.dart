import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/data_sync_providers.dart';
import '../services/data_sync_service.dart';

/// Smart refresh widget that provides pull-to-refresh functionality with data sync
class SmartRefreshWidget extends ConsumerWidget {
  final Widget child;
  final String dataType;
  final VoidCallback? onRefresh;
  final bool showRefreshIndicator;

  const SmartRefreshWidget({
    super.key,
    required this.child,
    required this.dataType,
    this.onRefresh,
    this.showRefreshIndicator = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (!showRefreshIndicator) {
      return child;
    }

    return RefreshIndicator(
      onRefresh: () => _handleRefresh(ref),
      child: child,
    );
  }

  /// Handle refresh action
  Future<void> _handleRefresh(WidgetRef ref) async {
    debugPrint('🔄 Smart refresh triggered for $dataType');

    // Trigger refresh based on data type
    switch (dataType) {
      case DataTypes.wallet:
        await ref.read(walletDataSyncProvider.notifier).refreshData(force: true);
        break;
      case DataTypes.profile:
        await ref.read(profileDataSyncProvider.notifier).refreshData(force: true);
        break;
      case DataTypes.dashboard:
        await ref.read(dashboardDataSyncProvider.notifier).refreshData(force: true);
        break;
      default:
        debugPrint('🔄 Unknown data type for refresh: $dataType');
    }

    // Call custom refresh callback if provided
    if (onRefresh != null) {
      onRefresh!();
    }

    debugPrint('🔄 Smart refresh completed for $dataType');
  }
}

/// Data freshness indicator widget
class DataFreshnessIndicator extends ConsumerWidget {
  final String dataType;
  final bool showAsChip;

  const DataFreshnessIndicator({
    super.key,
    required this.dataType,
    this.showAsChip = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Watch data sync state for the specific data type
    DataSyncState? syncState;
    switch (dataType) {
      case DataTypes.wallet:
        syncState = ref.watch(walletDataSyncProvider);
        break;
      case DataTypes.profile:
        syncState = ref.watch(profileDataSyncProvider);
        break;
      case DataTypes.dashboard:
        syncState = ref.watch(dashboardDataSyncProvider);
        break;
    }

    if (syncState == null) return const SizedBox.shrink();

    // Don't show indicator if data is fresh and not loading
    if (syncState.isFresh && !syncState.isLoading) {
      return const SizedBox.shrink();
    }

    if (showAsChip) {
      return _buildChipIndicator(syncState, isDarkMode);
    } else {
      return _buildBadgeIndicator(syncState, isDarkMode);
    }
  }

  /// Build chip-style indicator
  Widget _buildChipIndicator(DataSyncState syncState, bool isDarkMode) {
    IconData icon;
    Color color;
    String text;

    if (syncState.isLoading) {
      icon = Icons.sync;
      color = Colors.blue;
      text = 'Syncing...';
    } else if (!syncState.isFresh) {
      icon = Icons.refresh;
      color = Colors.orange;
      text = 'Tap to refresh';
    } else {
      return const SizedBox.shrink();
    }

    return Chip(
      avatar: syncState.isLoading
          ? SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(color),
              ),
            )
          : Icon(icon, size: 16, color: color),
      label: Text(
        text,
        style: TextStyle(
          fontSize: 12,
          color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
        ),
      ),
      backgroundColor: isDarkMode ? Colors.grey[800] : Colors.grey[100],
      side: BorderSide(color: color.withValues(alpha: 0.3)),
    );
  }

  /// Build badge-style indicator
  Widget _buildBadgeIndicator(DataSyncState syncState, bool isDarkMode) {
    if (syncState.isLoading) {
      return Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Colors.blue.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: const SizedBox(
          width: 12,
          height: 12,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
          ),
        ),
      );
    } else if (!syncState.isFresh) {
      return Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Colors.orange.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Icon(
          Icons.refresh,
          size: 12,
          color: Colors.orange,
        ),
      );
    }

    return const SizedBox.shrink();
  }
}

/// Shimmer loading widget for data sync
class DataSyncShimmer extends StatelessWidget {
  final Widget child;
  final bool isLoading;

  const DataSyncShimmer({
    super.key,
    required this.child,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    if (!isLoading) return child;

    return Stack(
      children: [
        child,
        Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.grey.withValues(alpha: 0.1),
                  Colors.grey.withValues(alpha: 0.3),
                  Colors.grey.withValues(alpha: 0.1),
                ],
                stops: const [0.0, 0.5, 1.0],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// Smart data consumer widget that automatically handles data sync
class SmartDataConsumer<T> extends ConsumerWidget {
  final String dataType;
  final Widget Function(BuildContext context, T? data, bool isLoading) builder;
  final T? Function(Map<String, dynamic>? rawData) dataParser;

  const SmartDataConsumer({
    super.key,
    required this.dataType,
    required this.builder,
    required this.dataParser,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch data sync state for the specific data type
    DataSyncState? syncState;
    switch (dataType) {
      case DataTypes.wallet:
        syncState = ref.watch(walletDataSyncProvider);
        break;
      case DataTypes.profile:
        syncState = ref.watch(profileDataSyncProvider);
        break;
      case DataTypes.dashboard:
        syncState = ref.watch(dashboardDataSyncProvider);
        break;
    }

    if (syncState == null) {
      return builder(context, null, false);
    }

    // Parse data using the provided parser
    final parsedData = dataParser(syncState.data);

    return builder(context, parsedData, syncState.isLoading);
  }
}

/// Extension to add smart refresh capabilities to any widget
extension SmartRefreshExtension on Widget {
  Widget withSmartRefresh({
    required String dataType,
    VoidCallback? onRefresh,
    bool showRefreshIndicator = true,
  }) {
    return SmartRefreshWidget(
      dataType: dataType,
      onRefresh: onRefresh,
      showRefreshIndicator: showRefreshIndicator,
      child: this,
    );
  }

  Widget withDataSyncShimmer({required bool isLoading}) {
    return DataSyncShimmer(
      isLoading: isLoading,
      child: this,
    );
  }
}
