import 'package:flutter/material.dart';

/// Custom Firebase-like icon widget
/// Creates a Firebase-themed icon using available Material icons
class FirebaseIconWidget extends StatelessWidget {
  final double size;
  final Color? color;

  const FirebaseIconWidget({
    super.key,
    this.size = 24.0,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final iconColor = color ?? Theme.of(context).colorScheme.primary;

    return Stack(
      alignment: Alignment.center,
      children: [
        // Background flame/fire effect
        Icon(
          Icons.local_fire_department,
          size: size,
          color: iconColor,
        ),
        // Cloud overlay for Firebase cloud services
        Positioned(
          top: size * 0.1,
          right: size * 0.1,
          child: Icon(
            Icons.cloud,
            size: size * 0.4,
            color: iconColor.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }
}

/// Alternative simple Firebase-like icon
class SimpleFirebaseIcon extends StatelessWidget {
  final double size;
  final Color? color;

  const SimpleFirebaseIcon({
    super.key,
    this.size = 24.0,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final iconColor = color ?? Theme.of(context).colorScheme.primary;

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            iconColor,
            iconColor.withValues(alpha: 0.7),
          ],
        ),
        borderRadius: BorderRadius.circular(size * 0.2),
      ),
      child: Icon(
        Icons.whatshot,
        size: size * 0.7,
        color: Colors.white,
      ),
    );
  }
}

/// Firebase service status icon
class FirebaseStatusIcon extends StatelessWidget {
  final bool isConnected;
  final double size;

  const FirebaseStatusIcon({
    super.key,
    required this.isConnected,
    this.size = 24.0,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Icon(
          Icons.cloud_circle,
          size: size,
          color: isConnected ? Colors.green : Colors.grey,
        ),
        if (isConnected)
          Positioned(
            bottom: size * 0.1,
            right: size * 0.1,
            child: Container(
              width: size * 0.3,
              height: size * 0.3,
              decoration: const BoxDecoration(
                color: Colors.green,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.check,
                size: size * 0.2,
                color: Colors.white,
              ),
            ),
          )
        else
          Positioned(
            bottom: size * 0.1,
            right: size * 0.1,
            child: Container(
              width: size * 0.3,
              height: size * 0.3,
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.close,
                size: size * 0.2,
                color: Colors.white,
              ),
            ),
          ),
      ],
    );
  }
}
