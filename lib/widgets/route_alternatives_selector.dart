import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/route_alternative.dart';
import '../providers/route_provider.dart';
import '../utils/app_theme.dart';
import '../utils/app_themes.dart';

/// Minimalist and compressed widget for selecting between multiple route alternatives
///
/// **Design Features:**
/// - Compressed layout matching charging options page dimensions
/// - Reduced padding and margins for space efficiency
/// - Consistent styling with charging options UI patterns
/// - Responsive design that works across different screen sizes
/// - Maintains readability while maximizing content density
///
/// **Layout Specifications:**
/// - Container margins: 20px horizontal, 8px top (matches charging options)
/// - Border radius: 16px (consistent with app design language)
/// - Card padding: 12px horizontal, 10px vertical (compressed)
/// - Icon size: 32x32px (reduced from 40x40px)
/// - Font sizes: 13px title, 11px details (optimized for readability)
class RouteAlternativesSelector extends ConsumerWidget {
  final VoidCallback? onRouteSelected;

  const RouteAlternativesSelector({
    super.key,
    this.onRouteSelected,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final routeAlternatives = ref.watch(routeAlternativesProvider);
    final hasAlternatives = ref.watch(hasRouteAlternativesProvider);
    final selectedAlternative = ref.watch(selectedRouteAlternativeProvider);
    final isLoading = ref.watch(routeAlternativesLoadingProvider);

    // Debug logging
    if (kDebugMode && routeAlternatives != null) {
      debugPrint(
          '🛣️ SELECTOR: Displaying ${routeAlternatives.alternatives.length} route alternatives');
    }

    if (!hasAlternatives || routeAlternatives == null) {
      return const SizedBox.shrink();
    }

    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.fromLTRB(
          20, 6, 20, 18), // Extreme bottom margin to eliminate all overflow
      decoration: BoxDecoration(
        color: isDarkMode ? AppThemes.darkCard : Colors.white,
        borderRadius: BorderRadius.circular(
            12), // Reduced from 16 to match charging options
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withAlpha(40)
                : Colors.black.withAlpha(15), // Reduced shadow intensity
            blurRadius: 6, // Reduced from 8
            spreadRadius: 0,
            offset: const Offset(0, 1), // Reduced from (0, 2)
          ),
        ],
        border: Border.all(
          color: isDarkMode
              ? AppThemes.darkBorder
              : Colors.grey.shade200, // Lighter border
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Compressed header
          Padding(
            padding: const EdgeInsets.fromLTRB(
                16, 8, 16, 4), // Minimum padding for header
            child: Row(
              children: [
                Icon(
                  Icons.alt_route,
                  color: AppTheme.primaryColor,
                  size: 18, // Reduced from 20
                ),
                const SizedBox(width: 6), // Reduced from 8
                Text(
                  'Route Options',
                  style: TextStyle(
                    fontSize: 15, // Reduced from 16
                    fontWeight: FontWeight.w600,
                    color:
                        isDarkMode ? Colors.white : AppTheme.textPrimaryColor,
                    letterSpacing: -0.3, // Match charging options
                  ),
                ),
                const Spacer(),
                if (isLoading)
                  SizedBox(
                    width: 14, // Reduced from 16
                    height: 14,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppTheme.primaryColor,
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // Horizontal route alternatives row
          SizedBox(
            height: 75, // Increased height to accommodate content properly
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              itemCount: routeAlternatives.alternatives.length,
              itemBuilder: (context, index) {
                final alternative = routeAlternatives.alternatives[index];
                final isSelected = selectedAlternative?.id == alternative.id;

                return Container(
                  width: MediaQuery.of(context).size.width *
                      0.28, // ~1/3 of screen width
                  margin: EdgeInsets.only(
                    right: index < routeAlternatives.alternatives.length - 1
                        ? 6
                        : 0,
                  ),
                  child: _buildHorizontalRouteCard(
                    context,
                    ref,
                    alternative,
                    isSelected,
                    isDarkMode,
                  ),
                );
              },
            ),
          ),

          const SizedBox(height: 8), // Reduced bottom padding
        ],
      ),
    );
  }

  /// Horizontal route card for single row layout
  Widget _buildHorizontalRouteCard(
    BuildContext context,
    WidgetRef ref,
    RouteAlternative alternative,
    bool isSelected,
    bool isDarkMode,
  ) {
    return Container(
      height: 70, // Increased height to accommodate content properly
      decoration: BoxDecoration(
        color: isSelected
            ? AppTheme.primaryColor.withAlpha(20)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(10), // Smaller radius
        border: Border.all(
          color: isSelected
              ? AppTheme.primaryColor
              : (isDarkMode
                  ? AppThemes.darkBorder.withAlpha(60)
                  : Colors.grey.shade200),
          width: isSelected ? 1.5 : 1, // Slightly thinner border
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            debugPrint('🛣️ SELECTOR: Selecting route ${alternative.id}');
            ref
                .read(routeProvider.notifier)
                .selectRouteAlternative(alternative.id);
            onRouteSelected?.call();
          },
          borderRadius: BorderRadius.circular(10), // Smaller radius
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6), // Increased horizontal padding for better spacing
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly, // Better distribution
              mainAxisSize: MainAxisSize.min, // Minimize vertical space
              children: [
                // Route optimization icon
                Container(
                  width: 18, // Slightly larger icon size for better visibility
                  height: 18, // Slightly larger icon size for better visibility
                  decoration: BoxDecoration(
                    color: isSelected
                        ? AppTheme.primaryColor.withAlpha(30)
                        : (isDarkMode
                            ? Colors.grey.withAlpha(20)
                            : AppTheme.primaryColor.withAlpha(15)),
                    borderRadius: BorderRadius.circular(4), // Smaller radius
                  ),
                  child: Center(
                    child: Text(
                      alternative.optimization.icon,
                      style: const TextStyle(fontSize: 12), // Icon text size
                    ),
                  ),
                ),
                const SizedBox(height: 5), // Slightly increased spacing for better separation

                // Route name
                Flexible(
                  child: Text(
                    alternative.optimization.displayName,
                    style: TextStyle(
                      fontSize: 10, // Slightly larger font for better readability
                      fontWeight: FontWeight.w600,
                      color: isSelected
                          ? AppTheme.primaryColor
                          : (isDarkMode ? Colors.white : Colors.black87),
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                const SizedBox(height: 3), // Increased spacing for better separation

                // Route metrics - increased font size for better visibility
                Flexible(
                  child: Text(
                    '${alternative.route.distance} • ${alternative.route.duration}',
                    style: TextStyle(
                      fontSize: 9, // Slightly larger font for better readability
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                      color: isSelected
                          ? AppTheme.primaryColor
                          : (isDarkMode
                              ? Colors.grey.shade400
                              : Colors.grey.shade600),
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                // Removed tick icon to save space
              ],
            ),
          ),
        ),
      ),
    );
  }
}
