import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ecoplug/tests/fcm_token_test_service.dart';
import 'package:ecoplug/widgets/firebase_icon_widget.dart';

/// FCM Token Test Widget
/// Provides UI to test FCM token generation and display results
class FCMTokenTestWidget extends StatefulWidget {
  const FCMTokenTestWidget({super.key});

  @override
  State<FCMTokenTestWidget> createState() => _FCMTokenTestWidgetState();
}

class _FCMTokenTestWidgetState extends State<FCMTokenTestWidget> {
  final FCMTokenTestService _testService = FCMTokenTestService();
  
  Map<String, dynamic>? _testResults;
  String? _currentToken;
  bool _isLoading = false;
  bool _autoRunCompleted = false;

  @override
  void initState() {
    super.initState();
    // Auto-run quick test on widget initialization
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _runQuickTest();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              children: [
                FirebaseIconWidget(
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'FCM Token Generation Test 🔥',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: _runQuickTest,
                  icon: const Icon(Icons.refresh),
                  tooltip: 'Refresh Token Test',
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else ...[
              // Quick Status
              if (_currentToken != null) ...[
                _buildTokenStatus(_currentToken!, theme),
                const SizedBox(height: 16),
              ],

              // Test Results
              if (_testResults != null) ...[
                _buildTestResults(_testResults!, theme),
                const SizedBox(height: 16),
              ],

              // Action Buttons
              _buildActionButtons(theme),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTokenStatus(String token, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 20),
              const SizedBox(width: 8),
              Text(
                'FCM Token Generated Successfully! ✅',
                style: theme.textTheme.titleSmall?.copyWith(
                  color: Colors.green,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Token Length: ${token.length} characters',
            style: theme.textTheme.bodySmall,
          ),
          const SizedBox(height: 4),
          Text(
            'Preview: ${token.substring(0, 50)}...',
            style: theme.textTheme.bodySmall?.copyWith(
              fontFamily: 'monospace',
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              ElevatedButton.icon(
                onPressed: () => _copyTokenToClipboard(token),
                icon: const Icon(Icons.copy, size: 16),
                label: const Text('Copy Full Token'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
              const SizedBox(width: 8),
              OutlinedButton.icon(
                onPressed: () => _showFullToken(token),
                icon: const Icon(Icons.visibility, size: 16),
                label: const Text('View Full'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTestResults(Map<String, dynamic> results, ThemeData theme) {
    final summary = results['test_summary'] as Map<String, dynamic>?;
    if (summary == null) return const SizedBox.shrink();

    final overallStatus = summary['overall_status'] as String? ?? 'unknown';
    final Color statusColor;
    final IconData statusIcon;

    switch (overallStatus) {
      case 'passed':
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case 'warning':
        statusColor = Colors.orange;
        statusIcon = Icons.warning;
        break;
      case 'failed':
        statusColor = Colors.red;
        statusIcon = Icons.error;
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.help;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Comprehensive Test Results',
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        
        // Overall Status
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: statusColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: statusColor.withValues(alpha: 0.3)),
          ),
          child: Row(
            children: [
              Icon(statusIcon, color: statusColor),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Overall Status: ${overallStatus.toUpperCase()}',
                      style: theme.textTheme.titleSmall?.copyWith(
                        color: statusColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Tests: ${summary['passed']}/${summary['total_tests']} passed (${summary['success_rate']}%)',
                      style: theme.textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),

        // Individual Test Results
        ...results.entries.where((e) => e.key.endsWith('_test')).map((entry) {
          return _buildIndividualTestResult(entry.key, entry.value as Map<String, dynamic>, theme);
        }),
      ],
    );
  }

  Widget _buildIndividualTestResult(String testName, Map<String, dynamic> testData, ThemeData theme) {
    final status = testData['status'] as String? ?? 'unknown';
    final Color statusColor = status == 'passed' ? Colors.green : 
                             status == 'warning' ? Colors.orange : Colors.red;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: ExpansionTile(
        leading: Icon(
          status == 'passed' ? Icons.check_circle : 
          status == 'warning' ? Icons.warning : Icons.error,
          color: statusColor,
          size: 20,
        ),
        title: Text(
          _formatTestName(testName),
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          'Status: ${status.toUpperCase()}',
          style: theme.textTheme.bodySmall?.copyWith(
            color: statusColor,
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: testData.entries.where((e) => e.key != 'status').map((entry) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          _formatKey(entry.key),
                          style: theme.textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        flex: 3,
                        child: Text(
                          _formatValue(entry.value),
                          style: theme.textTheme.bodySmall,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Test Actions',
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            ElevatedButton.icon(
              onPressed: _runQuickTest,
              icon: const Icon(Icons.speed),
              label: const Text('Quick Test'),
            ),
            ElevatedButton.icon(
              onPressed: _runComprehensiveTest,
              icon: const Icon(Icons.verified),
              label: const Text('Full Test'),
            ),
            OutlinedButton.icon(
              onPressed: _getCurrentToken,
              icon: const Icon(Icons.token),
              label: const Text('Get Token'),
            ),
            if (_currentToken != null)
              OutlinedButton.icon(
                onPressed: () => _copyTokenToClipboard(_currentToken!),
                icon: const Icon(Icons.copy),
                label: const Text('Copy Token'),
              ),
          ],
        ),
      ],
    );
  }

  Future<void> _runQuickTest() async {
    setState(() => _isLoading = true);
    
    try {
      final result = await _testService.quickTokenTest();
      
      if (result['success'] == true) {
        setState(() => _currentToken = result['token'] as String?);
        _showSnackBar('Quick test passed! FCM token generated ✅', Colors.green);
      } else {
        _showSnackBar('Quick test failed: ${result['message']}', Colors.red);
      }
      
      setState(() => _autoRunCompleted = true);
    } catch (e) {
      _showSnackBar('Quick test error: $e', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _runComprehensiveTest() async {
    setState(() => _isLoading = true);
    
    try {
      final results = await _testService.runFCMTokenTests();
      setState(() => _testResults = results);
      
      final summary = results['test_summary'] as Map<String, dynamic>?;
      final overallStatus = summary?['overall_status'] as String? ?? 'unknown';
      
      final Color snackBarColor = overallStatus == 'passed' ? Colors.green : 
                                 overallStatus == 'warning' ? Colors.orange : Colors.red;
      
      _showSnackBar(
        'Comprehensive test completed: ${overallStatus.toUpperCase()}',
        snackBarColor,
      );
      
      // Update current token if available
      final tokenTest = results['token_generation_test'] as Map<String, dynamic>?;
      if (tokenTest?['status'] == 'passed') {
        setState(() => _currentToken = tokenTest!['token'] as String?);
      }
    } catch (e) {
      _showSnackBar('Comprehensive test error: $e', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _getCurrentToken() async {
    try {
      final token = await _testService.getCurrentFCMToken();
      
      if (token != null) {
        setState(() => _currentToken = token);
        _showSnackBar('FCM token retrieved successfully!', Colors.green);
      } else {
        _showSnackBar('Failed to retrieve FCM token', Colors.red);
      }
    } catch (e) {
      _showSnackBar('Error getting token: $e', Colors.red);
    }
  }

  void _copyTokenToClipboard(String token) {
    Clipboard.setData(ClipboardData(text: token));
    _showSnackBar('FCM token copied to clipboard! 📋', Colors.green);
  }

  void _showFullToken(String token) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Full FCM Token'),
        content: SizedBox(
          width: double.maxFinite,
          child: SingleChildScrollView(
            child: SelectableText(
              token,
              style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          TextButton(
            onPressed: () {
              _copyTokenToClipboard(token);
              Navigator.of(context).pop();
            },
            child: const Text('Copy'),
          ),
        ],
      ),
    );
  }

  void _showSnackBar(String message, Color backgroundColor) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  String _formatTestName(String testName) {
    return testName
        .replaceAll('_test', '')
        .split('_')
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ');
  }

  String _formatKey(String key) {
    return key.split('_').map((word) => 
        word[0].toUpperCase() + word.substring(1)).join(' ');
  }

  String _formatValue(dynamic value) {
    if (value == null) return 'null';
    if (value is bool) return value ? 'Yes' : 'No';
    if (value is Map) return 'Object (${value.length} items)';
    if (value is List) return 'Array (${value.length} items)';
    if (value is String && value.length > 50) return '${value.substring(0, 50)}...';
    return value.toString();
  }
}
