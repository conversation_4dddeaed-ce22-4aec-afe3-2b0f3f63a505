import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A custom circular percent indicator widget
/// This is a simplified version of the percent_indicator package's CircularPercentIndicator
class CircularPercentIndicator extends StatelessWidget {
  final double radius;
  final double lineWidth;
  final double percent;
  final Widget center;
  final Color backgroundColor;
  final Color? progressColor;
  final LinearGradient? linearGradient;
  final bool animation;
  final int animationDuration;
  final bool animateFromLastPercent;
  final Widget? widgetIndicator;
  final bool rotateLinearGradient;
  final StrokeCap circularStrokeCap;

  const CircularPercentIndicator({
    super.key,
    required this.radius,
    required this.lineWidth,
    required this.percent,
    required this.center,
    this.backgroundColor = Colors.transparent,
    this.progressColor,
    this.linearGradient,
    this.animation = false,
    this.animationDuration = 500,
    this.animateFromLastPercent = false,
    this.widgetIndicator,
    this.rotateLinearGradient = false,
    this.circularStrokeCap = StrokeCap.round,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: radius * 2,
      width: radius * 2,
      child: CustomPaint(
        painter: _CircularProgressPainter(
          progress: percent,
          progressColor: progressColor,
          backgroundColor: backgroundColor,
          lineWidth: lineWidth,
          gradient: linearGradient,
          circularStrokeCap: circularStrokeCap,
          rotateGradient: rotateLinearGradient,
        ),
        child: Center(
          child: Stack(
            alignment: Alignment.center,
            children: [
              center,
              if (widgetIndicator != null)
                Positioned(
                  top: radius - (lineWidth / 2),
                  left: radius +
                      (radius * sin(2 * 3.14159 * percent) - (lineWidth / 2)),
                  child: Transform.rotate(
                    angle: 2 * 3.14159 * percent,
                    child: widgetIndicator!,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper method to calculate position
  double sin(double angle) {
    return angle.sin();
  }
}

/// Custom painter for the circular progress indicator
class _CircularProgressPainter extends CustomPainter {
  final double progress;
  final Color? progressColor;
  final Color backgroundColor;
  final double lineWidth;
  final LinearGradient? gradient;
  final StrokeCap circularStrokeCap;
  final bool rotateGradient;

  _CircularProgressPainter({
    required this.progress,
    this.progressColor,
    required this.backgroundColor,
    required this.lineWidth,
    this.gradient,
    required this.circularStrokeCap,
    required this.rotateGradient,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - lineWidth / 2;

    // Draw background circle
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..strokeWidth = lineWidth
      ..style = PaintingStyle.stroke;

    canvas.drawCircle(center, radius, backgroundPaint);

    // Draw progress arc
    final progressPaint = Paint()
      ..strokeWidth = lineWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = circularStrokeCap;

    if (gradient != null) {
      // Create a shader from the gradient
      final rect = Rect.fromCircle(center: center, radius: radius);
      progressPaint.shader = gradient!.createShader(rect);
    } else if (progressColor != null) {
      progressPaint.color = progressColor!;
    } else {
      progressPaint.color = Colors.blue; // Default color
    }

    // Calculate the sweep angle based on progress (0.0 to 1.0)
    // calucluat the sweep angle also .
    final sweepAngle = 2 * 3.14159 * progress;

    // Draw the progress arc
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -3.14159 / 2, // Start from the top (270 degrees)
      sweepAngle,
      false,
      progressPaint,
    );
  }

  @override
  bool shouldRepaint(_CircularProgressPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.progressColor != progressColor ||
        oldDelegate.backgroundColor != backgroundColor ||
        oldDelegate.lineWidth != lineWidth ||
        oldDelegate.gradient != gradient ||
        oldDelegate.circularStrokeCap != circularStrokeCap ||
        oldDelegate.rotateGradient != rotateGradient;
  }
}

/// Extension to add sin method to double
extension DoubleExtension on double {
  double sin() {
    return math.sin(this);
  }
}

/// Math utility class
class Math {
  static double sin(double angle) {
    return math.sin(angle);
  }
}
