import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

/// Simple GoogleMapWidget wrapper for compatibility
class GoogleMapWidget extends StatefulWidget {
  final List<Map<String, dynamic>>? stations;
  final Function(Map<String, dynamic>)? onStationSelected;
  final Function(CameraPosition)? onCameraPositionChanged;
  final String? selectedStationId;

  const GoogleMapWidget({
    super.key,
    this.stations,
    this.onStationSelected,
    this.onCameraPositionChanged,
    this.selectedStationId,
  });

  @override
  State<GoogleMapWidget> createState() => GoogleMapWidgetState();
}

class GoogleMapWidgetState extends State<GoogleMapWidget> {
  GoogleMapController? _mapController;
  final Set<Marker> _markers = {};

  @override
  Widget build(BuildContext context) {
    return GoogleMap(
      onMapCreated: (GoogleMapController controller) {
        _mapController = controller;
      },
      initialCameraPosition: const CameraPosition(
        target: LatLng(20.5937, 78.9629), // Center of India
        zoom: 5.0,
      ),
      markers: _markers,
      onCameraMove: widget.onCameraPositionChanged,
    );
  }

  /// Focus the map on a specific location with zoom
  void focusOnLocation(double latitude, double longitude, double zoom) {
    if (_mapController != null) {
      _mapController!.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: LatLng(latitude, longitude),
            zoom: zoom,
          ),
        ),
      );
    }
  }

  /// Reset auto-centering behavior
  void resetAutoCentering() {
    debugPrint('🗺️ Auto-centering reset');
  }
}
