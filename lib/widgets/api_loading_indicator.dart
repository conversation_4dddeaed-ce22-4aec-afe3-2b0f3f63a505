import 'package:flutter/material.dart';

/// A reusable loading indicator for API calls
/// Shows a loading indicator with an optional message
/// Can be configured to show a retry button after a timeout
class ApiLoadingIndicator extends StatefulWidget {
  final String? message;
  final bool showRetry;
  final VoidCallback? onRetry;
  final Duration timeout;
  final bool isTransparent;

  const ApiLoadingIndicator({
    super.key,
    this.message,
    this.showRetry = false,
    this.onRetry,
    this.timeout = const Duration(seconds: 15),
    this.isTransparent = false,
  });

  @override
  State<ApiLoadingIndicator> createState() => _ApiLoadingIndicatorState();
}

class _ApiLoadingIndicatorState extends State<ApiLoadingIndicator> {
  bool _showRetry = false;

  @override
  void initState() {
    super.initState();
    if (widget.showRetry) {
      Future.delayed(widget.timeout, () {
        if (mounted) {
          setState(() {
            _showRetry = true;
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: widget.isTransparent
          ? Colors.black.withAlpha(77) // 0.3 * 255 = ~77
          : Theme.of(context).scaffoldBackgroundColor,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (!_showRetry) ...[
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF67C44C)),
              ),
              if (widget.message != null) ...[
                const SizedBox(height: 16),
                Text(
                  widget.message!,
                  style: TextStyle(
                    color: Colors.grey.shade700,
                    fontSize: 16,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ] else if (widget.onRetry != null) ...[
              Icon(
                Icons.error_outline,
                color: Colors.orange.shade700,
                size: 48,
              ),
              const SizedBox(height: 16),
              Text(
                'Taking longer than expected',
                style: TextStyle(
                  color: Colors.grey.shade700,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'The server is taking too long to respond',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: widget.onRetry,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF67C44C),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('Retry'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// A loading overlay that can be shown on top of the current screen
/// Uses a transparent background to show the content behind it
class ApiLoadingOverlay extends StatelessWidget {
  final String? message;
  final bool isLoading;
  final Widget child;
  final VoidCallback? onRetry;
  final Duration timeout;

  const ApiLoadingOverlay({
    super.key,
    required this.isLoading,
    required this.child,
    this.message,
    this.onRetry,
    this.timeout = const Duration(seconds: 15),
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Positioned.fill(
            child: ApiLoadingIndicator(
              message: message,
              showRetry: onRetry != null,
              onRetry: onRetry,
              timeout: timeout,
              isTransparent: true,
            ),
          ),
      ],
    );
  }
}
