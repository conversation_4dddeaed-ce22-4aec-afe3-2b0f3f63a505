import 'package:flutter/material.dart';
import '../models/wallet_transaction_model.dart';
import '../utils/transaction_icon_utils.dart';

class TransactionTile extends StatelessWidget {
  final TransactionModel transaction;

  const TransactionTile({super.key, required this.transaction});

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Use the new transaction icon utility for dynamic icon mapping
    final transactionIcon = TransactionIconUtils.getIconFromTransaction(
      type: transaction.type,
      remark: transaction.remark,
      source: transaction.source,
      status: transaction.status,
    );

    final iconColor = TransactionIconUtils.getColorFromTransaction(
      type: transaction.type,
      remark: transaction.remark,
      source: transaction.source,
      status: transaction.status,
      isDarkMode: isDarkMode,
    );

    final iconBackgroundColor = TransactionIconUtils.getBackgroundColorFromTransaction(
      type: transaction.type,
      remark: transaction.remark,
      source: transaction.source,
      status: transaction.status,
      isDarkMode: isDarkMode,
    );

    // Get status icon and colors
    IconData statusIcon;
    Color statusColor;

    if (transaction.isCompleted) {
      // Completed status: green checkmark
      statusIcon = Icons.check_circle;
      statusColor = const Color(0xFF22C55E);
    } else if (transaction.isRejected) {
      // Rejected status: red X indicator
      statusIcon = Icons.cancel;
      statusColor = const Color(0xFFEF4444);
    } else {
      // Other statuses: default icon
      statusIcon = Icons.info_outline;
      statusColor = Colors.orange;
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      elevation: isDarkMode ? 0 : 2,
      color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isDarkMode
            ? BorderSide(color: Colors.grey.shade800, width: 1)
            : BorderSide.none,
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            // Transaction type icon
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: iconBackgroundColor,
                border: Border.all(
                  color: iconColor.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Icon(
                transactionIcon,
                color: iconColor,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            // Transaction details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    transaction.formattedRemark,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: isDarkMode ? Colors.white : Colors.black87,
                        ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 6),
                  Text(
                    transaction.userFriendlyDate,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: isDarkMode
                              ? Colors.grey.shade400
                              : Colors.grey.shade600,
                          fontSize: 13,
                        ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'ID: ${transaction.id}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: isDarkMode
                              ? Colors.grey.shade500
                              : Colors.grey.shade500,
                          fontSize: 12,
                        ),
                  ),
                ],
              ),
            ),
            // Amount and status
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  transaction.displayAmount,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: transaction.isCredit
                            ? const Color(0xFF22C55E)
                            : const Color(0xFFEF4444),
                        fontSize: 16,
                      ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: statusColor.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        statusIcon,
                        color: statusColor,
                        size: 14,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        transaction.statusDisplayText,
                        style: TextStyle(
                          color: statusColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// Wallet Transactions List
class TransactionHistoryList extends StatelessWidget {
  final List<TransactionModel> transactions;

  const TransactionHistoryList({super.key, required this.transactions});

  @override
  Widget build(BuildContext context) {
    if (transactions.isEmpty) {
      return const Padding(
        padding: EdgeInsets.all(16.0),
        child: Center(
          child: Text('No transactions found'),
        ),
      );
    }

    return ListView.builder(
      itemCount: transactions.length,
      itemBuilder: (context, index) {
        return TransactionTile(transaction: transactions[index]);
      },
    );
  }
}
