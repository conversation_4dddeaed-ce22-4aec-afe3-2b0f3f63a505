import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ecoplug/services/firebase_config_verification_service.dart';
import 'package:ecoplug/widgets/firebase_icon_widget.dart';

/// Firebase Configuration Test Widget
/// Provides UI to test and verify Firebase configuration
class FirebaseConfigTestWidget extends StatefulWidget {
  const FirebaseConfigTestWidget({super.key});

  @override
  State<FirebaseConfigTestWidget> createState() => _FirebaseConfigTestWidgetState();
}

class _FirebaseConfigTestWidgetState extends State<FirebaseConfigTestWidget> {
  final FirebaseConfigVerificationService _verificationService =
      FirebaseConfigVerificationService();

  Map<String, dynamic>? _verificationResults;
  Map<String, dynamic>? _statusReport;
  String? _fcmToken;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _runInitialVerification();
  }

  Future<void> _runInitialVerification() async {
    await _verifyConfiguration();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              children: [
                FirebaseIconWidget(
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Firebase Configuration Test 🔥',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: _verifyConfiguration,
                  icon: const Icon(Icons.refresh),
                  tooltip: 'Refresh Verification',
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else ...[
              // Overall Status
              if (_verificationResults != null) ...[
                _buildOverallStatus(_verificationResults!, theme),
                const SizedBox(height: 16),
              ],

              // Verification Results
              if (_verificationResults != null) ...[
                _buildVerificationResults(_verificationResults!, theme),
                const SizedBox(height: 16),
              ],

              // FCM Token
              if (_fcmToken != null) ...[
                _buildFCMTokenSection(_fcmToken!, theme),
                const SizedBox(height: 16),
              ],

              // Action Buttons
              _buildActionButtons(theme),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildOverallStatus(Map<String, dynamic> results, ThemeData theme) {
    final status = results['overall_status'] as String? ?? 'unknown';
    final Color statusColor;
    final IconData statusIcon;
    final String statusText;

    switch (status) {
      case 'success':
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        statusText = 'Configuration Valid ✅';
        break;
      case 'warning':
        statusColor = Colors.orange;
        statusIcon = Icons.warning;
        statusText = 'Configuration Warning ⚠️';
        break;
      case 'failed':
        statusColor = Colors.red;
        statusIcon = Icons.error;
        statusText = 'Configuration Failed ❌';
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.help;
        statusText = 'Status Unknown ❓';
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(statusIcon, color: statusColor),
          const SizedBox(width: 8),
          Text(
            statusText,
            style: theme.textTheme.titleSmall?.copyWith(
              color: statusColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVerificationResults(Map<String, dynamic> results, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Verification Results',
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ...results.entries.where((e) => e.key != 'overall_status' && e.key != 'verification_timestamp').map((entry) {
          if (entry.value is Map<String, dynamic>) {
            return _buildVerificationItem(entry.key, entry.value as Map<String, dynamic>, theme);
          }
          return const SizedBox.shrink();
        }),
      ],
    );
  }

  Widget _buildVerificationItem(String title, Map<String, dynamic> data, ThemeData theme) {
    final status = data['status'] as String? ?? 'unknown';
    final Color statusColor = status == 'success' ? Colors.green :
                             status == 'warning' ? Colors.orange : Colors.red;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: ExpansionTile(
        leading: Icon(
          status == 'success' ? Icons.check_circle :
          status == 'warning' ? Icons.warning : Icons.error,
          color: statusColor,
          size: 20,
        ),
        title: Text(
          _formatTitle(title),
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          'Status: ${status.toUpperCase()}',
          style: theme.textTheme.bodySmall?.copyWith(
            color: statusColor,
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: data.entries.where((e) => e.key != 'status').map((entry) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          _formatKey(entry.key),
                          style: theme.textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        flex: 3,
                        child: Text(
                          _formatValue(entry.value),
                          style: theme.textTheme.bodySmall,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFCMTokenSection(String token, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'FCM Token',
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Token Preview: ${token.substring(0, 30)}...',
                style: theme.textTheme.bodySmall?.copyWith(
                  fontFamily: 'monospace',
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Length: ${token.length} characters',
                style: theme.textTheme.bodySmall,
              ),
              const SizedBox(height: 8),
              ElevatedButton.icon(
                onPressed: () => _copyTokenToClipboard(token),
                icon: const Icon(Icons.copy),
                label: const Text('Copy Full Token'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Test Actions',
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            ElevatedButton.icon(
              onPressed: _verifyConfiguration,
              icon: const Icon(Icons.verified),
              label: const Text('Verify Config'),
            ),
            ElevatedButton.icon(
              onPressed: _getFCMToken,
              icon: const Icon(Icons.token),
              label: const Text('Get FCM Token'),
            ),
            ElevatedButton.icon(
              onPressed: _testTokenRefresh,
              icon: const Icon(Icons.refresh),
              label: const Text('Test Refresh'),
            ),
            OutlinedButton.icon(
              onPressed: _generateStatusReport,
              icon: const Icon(Icons.report),
              label: const Text('Full Report'),
            ),
          ],
        ),
      ],
    );
  }

  Future<void> _verifyConfiguration() async {
    setState(() => _isLoading = true);

    try {
      final results = await _verificationService.verifyFirebaseConfiguration();
      setState(() => _verificationResults = results);

      _showSnackBar(
        'Firebase configuration verified!',
        results['overall_status'] == 'success' ? Colors.green : Colors.orange,
      );
    } catch (e) {
      _showSnackBar('Verification failed: $e', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _getFCMToken() async {
    try {
      final token = await _verificationService.getFCMTokenForTesting();
      setState(() => _fcmToken = token);

      if (token != null) {
        _showSnackBar('FCM token retrieved successfully!', Colors.green);
      } else {
        _showSnackBar('Failed to retrieve FCM token', Colors.red);
      }
    } catch (e) {
      _showSnackBar('Error getting FCM token: $e', Colors.red);
    }
  }

  Future<void> _testTokenRefresh() async {
    setState(() => _isLoading = true);

    try {
      final result = await _verificationService.testTokenRefresh();

      if (result['status'] == 'success') {
        _showSnackBar('Token refresh test successful!', Colors.green);
        // Refresh the token display
        await _getFCMToken();
      } else {
        _showSnackBar('Token refresh test failed: ${result['error']}', Colors.red);
      }
    } catch (e) {
      _showSnackBar('Token refresh test error: $e', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _generateStatusReport() async {
    try {
      final report = await _verificationService.generateStatusReport();
      setState(() => _statusReport = report);

      if (mounted) {
        _showStatusReportDialog(report);
      }
    } catch (e) {
      _showSnackBar('Error generating report: $e', Colors.red);
    }
  }

  void _showStatusReportDialog(Map<String, dynamic> report) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Firebase Status Report'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: SingleChildScrollView(
            child: Text(
              _formatReportForDisplay(report),
              style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          TextButton(
            onPressed: () {
              Clipboard.setData(ClipboardData(text: report.toString()));
              Navigator.of(context).pop();
              _showSnackBar('Report copied to clipboard!', Colors.green);
            },
            child: const Text('Copy'),
          ),
        ],
      ),
    );
  }

  void _copyTokenToClipboard(String token) {
    Clipboard.setData(ClipboardData(text: token));
    _showSnackBar('FCM token copied to clipboard!', Colors.green);
  }

  void _showSnackBar(String message, Color backgroundColor) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  String _formatTitle(String title) {
    return title.split('_').map((word) =>
        word[0].toUpperCase() + word.substring(1)).join(' ');
  }

  String _formatKey(String key) {
    return key.split('_').map((word) =>
        word[0].toUpperCase() + word.substring(1)).join(' ');
  }

  String _formatValue(dynamic value) {
    if (value == null) return 'null';
    if (value is bool) return value ? 'Yes' : 'No';
    if (value is Map) return 'Object (${value.length} items)';
    if (value is List) return 'Array (${value.length} items)';
    return value.toString();
  }

  String _formatReportForDisplay(Map<String, dynamic> report) {
    final buffer = StringBuffer();

    void writeMap(Map<String, dynamic> map, int indent) {
      map.forEach((key, value) {
        final prefix = '  ' * indent;
        if (value is Map<String, dynamic>) {
          buffer.writeln('$prefix$key:');
          writeMap(value, indent + 1);
        } else if (value is List) {
          buffer.writeln('$prefix$key: [${value.length} items]');
        } else {
          buffer.writeln('$prefix$key: $value');
        }
      });
    }

    writeMap(report, 0);
    return buffer.toString();
  }
}
