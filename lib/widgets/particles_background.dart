import 'package:flutter/material.dart';

class ParticleBackgroundPainter extends CustomPainter {
  final List<Map<String, dynamic>> particles;
  final Color baseColor;

  ParticleBackgroundPainter({
    required this.particles,
    required this.baseColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    for (var particle in particles) {
      final paint = Paint()
        ..color = baseColor.withAlpha((particle['opacity'] * 255).round())
        ..style = PaintingStyle.fill;

      final x = particle['x'] * size.width;
      final y = particle['y'] * size.height;
      final particleSize = particle['size'];

      canvas.drawCircle(
        Offset(x, y),
        particleSize,
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant ParticleBackgroundPainter oldDelegate) {
    return true; // Always repaint for animation
  }
}
