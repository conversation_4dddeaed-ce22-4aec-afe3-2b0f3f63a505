import 'package:flutter/material.dart';

class CancelSliderWidget extends StatefulWidget {
  final Function onCancelled;

  const CancelSliderWidget({
    super.key,
    required this.onCancelled,
  });

  @override
  CancelSliderWidgetState createState() => CancelSliderWidgetState();
}

class CancelSliderWidgetState extends State<CancelSliderWidget>
    with SingleTickerProviderStateMixin {
  double _position = 0;
  final double _threshold = 0.9;
  late AnimationController _resetController;
  late Animation<double> _resetAnimation;

  @override
  void initState() {
    super.initState();
    _resetController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _resetAnimation = Tween<double>(
      begin: 0,
      end: 0,
    ).animate(CurvedAnimation(
      parent: _resetController,
      curve: Curves.easeOut,
    ));

    _resetController.addListener(() {
      setState(() {
        _position = _resetAnimation.value;
      });
    });
  }

  @override
  void dispose() {
    _resetController.dispose();
    super.dispose();
  }

  void _reset() {
    _resetAnimation = Tween<double>(
      begin: _position,
      end: 0,
    ).animate(CurvedAnimation(
      parent: _resetController,
      curve: Curves.easeOut,
    ));

    _resetController.reset();
    _resetController.forward();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final sliderWidth = size.width - 40; // Padding on both sides

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Color(0xFF0E2438),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'Slide to Cancel Charging',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          Container(
            height: 60,
            width: double.infinity,
            decoration: BoxDecoration(
              color: const Color(0xFF102A40),
              borderRadius: BorderRadius.circular(30),
            ),
            child: Stack(
              children: [
                // Slider track with animation
                Container(
                  padding: const EdgeInsets.all(5),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: List.generate(10, (index) {
                      return AnimatedOpacity(
                        duration: const Duration(milliseconds: 300),
                        opacity: _position > index / 10 ? 1 : 0.3,
                        child: Container(
                          width: 3,
                          height: 50,
                          decoration: BoxDecoration(
                            color: _position > _threshold
                                ? Colors.red
                                : const Color(0xFF3A5F7E),
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      );
                    }),
                  ),
                ),

                // Slider thumb
                Positioned(
                  left: _position * (sliderWidth - 50),
                  top: 5,
                  child: GestureDetector(
                    onHorizontalDragUpdate: (details) {
                      setState(() {
                        // Calculate new position based on drag
                        _position += details.delta.dx / (sliderWidth - 50);
                        // Clamp position between 0 and 1
                        _position = _position.clamp(0.0, 1.0);
                      });
                    },
                    onHorizontalDragEnd: (details) {
                      if (_position >= _threshold) {
                        widget.onCancelled();
                      } else {
                        _reset();
                      }
                    },
                    child: Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: _position > _threshold
                            ? Colors.red
                            : const Color(0xFF00A3FF),
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: (_position > _threshold
                                    ? Colors.red
                                    : const Color(0xFF00A3FF))
                                .withOpacity(0.3),
                            blurRadius: 10,
                            spreadRadius: 3,
                          ),
                        ],
                      ),
                      child: Icon(
                        _position > _threshold
                            ? Icons.close
                            : Icons.arrow_forward,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),

                // Slide text
                Positioned.fill(
                  child: Align(
                    alignment: Alignment.center,
                    child: Padding(
                      padding: const EdgeInsets.only(left: 70, right: 20),
                      child: Text(
                        _position > _threshold
                            ? 'Release to Cancel'
                            : 'Slide Right to Cancel',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: _position > _threshold
                              ? Colors.red
                              : Colors.white70,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
