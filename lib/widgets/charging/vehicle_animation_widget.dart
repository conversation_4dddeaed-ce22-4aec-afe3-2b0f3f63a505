import 'package:flutter/material.dart';

class VehicleAnimationWidget extends StatelessWidget {
  final String vehicleNumber;
  final AnimationController animationController;

  const VehicleAnimationWidget({
    super.key,
    required this.vehicleNumber,
    required this.animationController,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: animationController,
      builder: (context, child) {
        return Container(
          width: double.infinity,
          height: 160,
          decoration: BoxDecoration(
            color: const Color(0xFF102A40),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Stack(
            children: [
              // Vehicle silhouette - static, professional positioning
              Center(
                child: SizedBox(
                  width: 200,
                  height: 100,
                  child: CustomPaint(
                    painter: VehiclePainter(),
                  ),
                ),
              ),

              // Charging effect - smooth, professional animation
              Positioned(
                right: 90,
                top: 65,
                child: _buildChargingEffect(),
              ),

              // Vehicle number - clean, static positioning
              Positioned(
                bottom: 15,
                left: 0,
                right: 0,
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 15,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFF1A3A54),
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.2),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Text(
                      vehicleNumber,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildChargingEffect() {
    // Professional, smooth charging effect with gentle pulsing
    final pulseOpacity = 0.6 + (0.4 * animationController.value);
    final pulseScale = 0.9 + (0.1 * animationController.value);

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      child: Transform.scale(
        scale: pulseScale,
        child: Opacity(
          opacity: pulseOpacity,
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: const Color(0xFF00A3FF).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: const Color(0xFF00A3FF).withValues(alpha: 0.3),
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF00A3FF).withValues(alpha: 0.3),
                  blurRadius: 8,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: const Icon(
              Icons.bolt,
              color: Color(0xFF00A3FF),
              size: 20,
            ),
          ),
        ),
      ),
    );
  }
}

class VehiclePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;

    final paint = Paint()
      ..color = const Color(0xFF3A5F7E)
      ..style = PaintingStyle.fill;

    // Car body path
    final path = Path();

    // Bottom of car
    path.moveTo(0, height * 0.7);
    path.lineTo(width, height * 0.7);

    // Right side
    path.lineTo(width, height * 0.5);
    path.lineTo(width * 0.9, height * 0.3);
    path.lineTo(width * 0.7, height * 0.15);

    // Top of car
    path.lineTo(width * 0.3, height * 0.15);

    // Left side
    path.lineTo(width * 0.1, height * 0.3);
    path.lineTo(0, height * 0.5);

    // Complete the shape
    path.close();

    // Draw the car body
    canvas.drawPath(path, paint);

    // Draw windows
    final windowPaint = Paint()
      ..color = const Color(0xFF102A40)
      ..style = PaintingStyle.fill;

    // Windshield
    final windshield = Path();
    windshield.moveTo(width * 0.3, height * 0.20);
    windshield.lineTo(width * 0.7, height * 0.20);
    windshield.lineTo(width * 0.65, height * 0.35);
    windshield.lineTo(width * 0.35, height * 0.35);
    windshield.close();
    canvas.drawPath(windshield, windowPaint);

    // Side windows
    final sideWindow = Path();
    sideWindow.moveTo(width * 0.75, height * 0.25);
    sideWindow.lineTo(width * 0.85, height * 0.35);
    sideWindow.lineTo(width * 0.85, height * 0.5);
    sideWindow.lineTo(width * 0.7, height * 0.5);
    sideWindow.close();
    canvas.drawPath(sideWindow, windowPaint);

    // Draw wheels
    final wheelPaint = Paint()
      ..color = const Color(0xFF1A1A1A)
      ..style = PaintingStyle.fill;

    canvas.drawCircle(
      Offset(width * 0.25, height * 0.7),
      height * 0.15,
      wheelPaint,
    );

    canvas.drawCircle(
      Offset(width * 0.75, height * 0.7),
      height * 0.15,
      wheelPaint,
    );

    // Draw wheel rims
    final rimPaint = Paint()
      ..color = const Color(0xFFCCCCCC)
      ..style = PaintingStyle.fill;

    canvas.drawCircle(
      Offset(width * 0.25, height * 0.7),
      height * 0.08,
      rimPaint,
    );

    canvas.drawCircle(
      Offset(width * 0.75, height * 0.7),
      height * 0.08,
      rimPaint,
    );

    // Draw headlights
    final headlightPaint = Paint()
      ..color = const Color(0xFFFFD700)
      ..style = PaintingStyle.fill;

    canvas.drawCircle(
      Offset(width * 0.05, height * 0.45),
      height * 0.05,
      headlightPaint,
    );

    canvas.drawCircle(
      Offset(width * 0.95, height * 0.45),
      height * 0.05,
      headlightPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
