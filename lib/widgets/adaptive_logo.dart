import 'package:flutter/material.dart';

/// Adaptive logo widget that automatically switches between light and dark variants
/// based on the current theme and background context.
///
/// This widget ensures optimal logo visibility across all app screens and themes
/// by intelligently selecting the appropriate logo variant.
class AdaptiveLogo extends StatelessWidget {
  /// Width of the logo
  final double? width;

  /// Height of the logo
  final double? height;

  /// BoxFit for the logo image
  final BoxFit fit;

  /// Force a specific logo variant (overrides automatic detection)
  final LogoVariant? forceVariant;

  /// Background color context for better logo selection
  final Color? backgroundColor;

  /// Whether to show a fallback icon if logo fails to load
  final bool showFallback;

  const AdaptiveLogo({
    super.key,
    this.width,
    this.height,
    this.fit = BoxFit.contain,
    this.forceVariant,
    this.backgroundColor,
    this.showFallback = true,
  });

  /// Named constructor for small logo sizes (typically for headers/navigation)
  const AdaptiveLogo.small({
    super.key,
    this.fit = BoxFit.contain,
    this.forceVariant,
    this.backgroundColor,
    this.showFallback = true,
  }) : width = 40, height = 40;

  /// Named constructor for medium logo sizes (typically for cards/lists)
  const AdaptiveLogo.medium({
    super.key,
    this.fit = BoxFit.contain,
    this.forceVariant,
    this.backgroundColor,
    this.showFallback = true,
  }) : width = 80, height = 80;

  /// Named constructor for large logo sizes (typically for splash/onboarding)
  const AdaptiveLogo.large({
    super.key,
    this.fit = BoxFit.contain,
    this.forceVariant,
    this.backgroundColor,
    this.showFallback = true,
  }) : width = 150, height = 150;

  @override
  Widget build(BuildContext context) {
    final logoPath = _getLogoPath(context);

    // Calculate the size for perfect circle
    final size = width ?? height ?? 40;

    return Container(
      width: size,
      height: size,
      decoration: const BoxDecoration(
        shape: BoxShape.circle, // Force perfect circle shape
      ),
      child: ClipOval(
        child: Image.asset(
          logoPath,
          width: size,
          height: size,
          fit: BoxFit.cover, // Cover ensures the image fills the circle completely
          errorBuilder: showFallback ? (context, error, stackTrace) {
            return _buildFallbackLogo(context);
          } : null,
        ),
      ),
    );
  }

  /// Determines the appropriate logo path based on theme and context
  String _getLogoPath(BuildContext context) {
    // If a specific variant is forced, use it
    if (forceVariant != null) {
      return _getPathForVariant(forceVariant!);
    }

    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // Check background color context if provided
    if (backgroundColor != null) {
      final backgroundLuminance = backgroundColor!.computeLuminance();
      // If background is dark (luminance < 0.5), use light logo
      if (backgroundLuminance < 0.5) {
        return _getPathForVariant(LogoVariant.light);
      } else {
        return _getPathForVariant(LogoVariant.dark);
      }
    }

    // Default theme-based selection
    if (isDarkMode) {
      return _getPathForVariant(LogoVariant.light);
    } else {
      return _getPathForVariant(LogoVariant.dark);
    }
  }

  /// Gets the asset path for a specific logo variant
  String _getPathForVariant(LogoVariant variant) {
    switch (variant) {
      case LogoVariant.light:
        return 'assets/images/ecoplug_logo_dark_fixed.png'; // Light logo for dark backgrounds
      case LogoVariant.dark:
        return 'assets/images/ecoplug_logo_dark.png'; // Dark logo for light backgrounds
      case LogoVariant.original:
        return 'assets/images/ecoplug_logo.png'; // Original logo
    }
  }

  /// Builds a fallback logo when the image fails to load
  Widget _buildFallbackLogo(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;
    final size = width ?? height ?? 40;

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: primaryColor.withValues(alpha: 0.1),
        shape: BoxShape.circle, // Perfect circle for fallback too
      ),
      child: Icon(
        Icons.electric_car_rounded,
        size: size * 0.6,
        color: primaryColor,
      ),
    );
  }
}

/// Available logo variants
enum LogoVariant {
  /// Light logo variant (for dark backgrounds)
  light,

  /// Dark logo variant (for light backgrounds)
  dark,

  /// Original logo variant
  original,
}

/// Extension to provide easy access to logo variants
extension LogoVariantExtension on LogoVariant {
  /// Get the asset path for this variant
  String get assetPath {
    switch (this) {
      case LogoVariant.light:
        return 'assets/images/ecoplug_logo_dark_fixed.png';
      case LogoVariant.dark:
        return 'assets/images/ecoplug_logo_dark.png';
      case LogoVariant.original:
        return 'assets/images/ecoplug_logo.png';
    }
  }
}
