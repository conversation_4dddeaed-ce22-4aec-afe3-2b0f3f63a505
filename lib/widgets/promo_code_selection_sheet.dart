import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/promo_code.dart';
import '../utils/app_themes.dart';

/// Enhanced full-screen promo code selection sheet with modern design
class PromoCodeSelectionSheet extends StatefulWidget {
  final List<PromoCode> availablePromoCodes;
  final bool isLoadingPromoCodes;
  final String? promoCodeError;
  final String searchQuery;
  final Function(PromoCode) onPromoSelected;
  final VoidCallback onRetry;
  final Function(String) onSearchChanged;

  const PromoCodeSelectionSheet({
    super.key,
    required this.availablePromoCodes,
    required this.isLoadingPromoCodes,
    required this.promoCodeError,
    required this.searchQuery,
    required this.onPromoSelected,
    required this.onRetry,
    required this.onSearchChanged,
  });

  @override
  State<PromoCodeSelectionSheet> createState() => _PromoCodeSelectionSheetState();
}

class _PromoCodeSelectionSheetState extends State<PromoCodeSelectionSheet>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
    _searchController.text = widget.searchQuery;
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final screenHeight = MediaQuery.of(context).size.height;
    final primaryColor = AppThemes.primaryColor;
    final secondaryColor = AppThemes.secondaryColor;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimation.value * screenHeight),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              height: screenHeight,
              decoration: BoxDecoration(
                color: isDarkMode ? AppThemes.darkSurface : Colors.white,
                borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
              ),
              child: SafeArea(
                child: Column(
                  children: [
                    // Header with title and close button
                    _buildHeader(isDarkMode, primaryColor),

                    // Content area
                    Expanded(
                      child: _buildContent(isDarkMode, primaryColor, secondaryColor),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(bool isDarkMode, Color primaryColor) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: isDarkMode ? AppThemes.darkBorder : Colors.grey.shade200,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Title
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Available Promo Codes',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: isDarkMode ? AppThemes.darkTextPrimary : const Color(0xFF1A1A1A),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Select a promo code to get extra savings',
                  style: TextStyle(
                    fontSize: 14,
                    color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),

          // Close button
          Semantics(
            label: 'Close promo code selection',
            button: true,
            child: GestureDetector(
              onTap: () => Navigator.pop(context),
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: isDarkMode ? AppThemes.darkCard : Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.close,
                  color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade600,
                  size: 20,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(bool isDarkMode, Color primaryColor, Color secondaryColor) {
    if (widget.isLoadingPromoCodes) {
      return _buildLoadingState(isDarkMode);
    }

    if (widget.promoCodeError != null) {
      return _buildErrorState(isDarkMode, primaryColor);
    }

    if (widget.availablePromoCodes.isEmpty) {
      return _buildEmptyState(isDarkMode);
    }

    // Filter promo codes based on search query
    final filteredPromoCodes = widget.availablePromoCodes.where((promo) {
      return promo.matchesSearch(widget.searchQuery);
    }).toList();

    return Column(
      children: [
        // Search bar
        if (widget.availablePromoCodes.length > 3) _buildSearchBar(isDarkMode),

        // Promo codes list
        Expanded(
          child: filteredPromoCodes.isEmpty
              ? _buildNoSearchResults(isDarkMode)
              : _buildPromoCodesList(filteredPromoCodes, isDarkMode, primaryColor, secondaryColor),
        ),
      ],
    );
  }

  Widget _buildLoadingState(bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppThemes.primaryColor),
          ),
          const SizedBox(height: 16),
          Text(
            'Loading promo codes...',
            style: TextStyle(
              color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade600,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(bool isDarkMode, Color primaryColor) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load promo codes',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: isDarkMode ? AppThemes.darkTextPrimary : const Color(0xFF1A1A1A),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.promoCodeError!,
              style: TextStyle(
                color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade600,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Semantics(
              label: 'Retry loading promo codes',
              button: true,
              child: ElevatedButton.icon(
                onPressed: widget.onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Retry'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(bool isDarkMode) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.local_offer_outlined,
              size: 64,
              color: isDarkMode ? Colors.grey.shade600 : Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'No Promo Codes Available',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: isDarkMode ? AppThemes.darkTextPrimary : const Color(0xFF1A1A1A),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Check back later for exciting offers and discounts!',
              style: TextStyle(
                color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade600,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar(bool isDarkMode) {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Semantics(
        label: 'Search promo codes',
        textField: true,
        child: TextField(
          controller: _searchController,
          onChanged: widget.onSearchChanged,
          decoration: InputDecoration(
            hintText: 'Search promo codes...',
            prefixIcon: Icon(
              Icons.search,
              color: isDarkMode ? Colors.grey.shade500 : Colors.grey.shade600,
            ),
            suffixIcon: widget.searchQuery.isNotEmpty
                ? Semantics(
                    label: 'Clear search',
                    button: true,
                    child: IconButton(
                      icon: Icon(
                        Icons.clear,
                        color: isDarkMode ? Colors.grey.shade500 : Colors.grey.shade600,
                      ),
                      onPressed: () {
                        _searchController.clear();
                        widget.onSearchChanged('');
                      },
                    ),
                  )
                : null,
            filled: true,
            fillColor: isDarkMode ? AppThemes.darkCard : Colors.grey.shade50,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          style: TextStyle(
            color: isDarkMode ? AppThemes.darkTextPrimary : const Color(0xFF1A1A1A),
          ),
        ),
      ),
    );
  }

  Widget _buildNoSearchResults(bool isDarkMode) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 48,
              color: isDarkMode ? Colors.grey.shade600 : Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'No matching promo codes found',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: isDarkMode ? AppThemes.darkTextPrimary : const Color(0xFF1A1A1A),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try searching with different keywords',
              style: TextStyle(
                color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade600,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPromoCodesList(List<PromoCode> promoCodes, bool isDarkMode, Color primaryColor, Color secondaryColor) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: promoCodes.length,
      itemBuilder: (context, index) {
        final promo = promoCodes[index];
        return _buildPromoCodeCard(promo, isDarkMode, primaryColor, secondaryColor);
      },
    );
  }

  Widget _buildPromoCodeCard(PromoCode promo, bool isDarkMode, Color primaryColor, Color secondaryColor) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Semantics(
        label: 'Promo code ${promo.code}, save ${promo.savingsText}, ${promo.description}',
        button: true,
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () => widget.onPromoSelected(promo),
            borderRadius: BorderRadius.circular(16),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isDarkMode ? AppThemes.darkCard : Colors.white,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: primaryColor.withValues(alpha: 0.3),
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: (isDarkMode ? Colors.black : Colors.grey).withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header row with code and savings badge
                  Row(
                    children: [
                      // Promo code with icon
                      Expanded(
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: primaryColor.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                Icons.local_offer,
                                color: primaryColor,
                                size: 20,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    promo.code.toUpperCase(),
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w700,
                                      color: isDarkMode ? AppThemes.darkTextPrimary : const Color(0xFF1A1A1A),
                                    ),
                                  ),
                                  if (promo.minimumAmountApplicable > 0)
                                    Text(
                                      promo.minimumAmountText,
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade600,
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Savings badge
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [primaryColor, secondaryColor],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          'SAVE ${promo.savingsText}',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w700,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // Description
                  Text(
                    promo.description,
                    style: TextStyle(
                      fontSize: 14,
                      color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade700,
                      height: 1.3,
                    ),
                  ),

                  const SizedBox(height: 12),

                  // Footer with copy button and expiry
                  Row(
                    children: [
                      // Copy code button
                      Semantics(
                        label: 'Copy promo code ${promo.code}',
                        button: true,
                        child: GestureDetector(
                          onTap: () => _copyPromoCode(promo.code),
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: primaryColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.copy,
                                  size: 12,
                                  color: primaryColor,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'Copy',
                                  style: TextStyle(
                                    fontSize: 11,
                                    fontWeight: FontWeight.w600,
                                    color: primaryColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),

                      const Spacer(),

                      // Expiry date
                      if (promo.endDate.isNotEmpty)
                        Text(
                          'Expires: ${promo.formattedExpiryDate}',
                          style: TextStyle(
                            fontSize: 11,
                            color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade500,
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _copyPromoCode(String code) {
    Clipboard.setData(ClipboardData(text: code));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Promo code "$code" copied to clipboard'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
