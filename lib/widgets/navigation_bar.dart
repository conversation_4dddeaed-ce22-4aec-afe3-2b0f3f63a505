import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_nav_bar/google_nav_bar.dart';
import 'package:line_icons/line_icons.dart';
import 'package:liquid_glass_renderer/liquid_glass_renderer.dart';
import '../screens/wallet/wallet_screen.dart';
import '../utils/app_themes.dart';
import '../screens/dashboard/dashboard_horizontal_cards.dart';
import '../screens/Trip/trip_page.dart';
import '../screens/Profile/Profilescreen/profile_screen_riverpod.dart';
import '../services/service_locator.dart';
import '../providers/data_sync_providers.dart';
import '../providers/theme_provider.dart';
import '../services/notification_navigation_service.dart';
import '../providers/places_provider.dart';
import '../services/connectivity_monitor.dart';

class MainNavigation extends ConsumerStatefulWidget {
  const MainNavigation({super.key});

  @override
  ConsumerState<MainNavigation> createState() => _MainNavigationState();
}

class _MainNavigationState extends ConsumerState<MainNavigation> {
  int _selectedIndex = 0;

  // Create page instances that react to theme changes
  // Using keys to ensure proper rebuilding when theme changes
  List<Widget> _buildPages(bool isDarkMode) {
    return [
      DashboardHorizontalCards(
          key: ValueKey('dashboard_$isDarkMode')), // Home tab
      WalletPage(
          key: ValueKey(
              'wallet_$isDarkMode')), // Wallet tab - will rebuild with theme changes
      TripPage(key: ValueKey('trip_$isDarkMode')), // Trip tab
      ProfileScreenRiverpod(
          key: ValueKey('profile_$isDarkMode')), // Profile tab
    ];
  }

  @override
  void initState() {
    super.initState();

    // Initialize smart data synchronization
    _initializeDataSync();

    // Process pending notification intents
    _processPendingNotificationIntents();

    // Set context for global connectivity monitor
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ConnectivityMonitor().setContext(context);
    });
  }

  /// Process pending notification navigation intents
  void _processPendingNotificationIntents() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        debugPrint('🔔 Processing pending notification intents...');
        final navigationService = NotificationNavigationService();
        await navigationService.processPendingNavigationIntents();
      } catch (e) {
        debugPrint('❌ Error processing notification intents: $e');
      }
    });
  }

  /// Initialize smart data synchronization for all pages
  void _initializeDataSync() {
    debugPrint('🔄 Initializing smart data synchronization');

    // Initialize data sync providers - they will automatically load cached data
    // and check freshness in the background
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Trigger initial data sync for all providers
      ref.read(walletDataSyncProvider.notifier);
      ref.read(profileDataSyncProvider.notifier);
      ref.read(dashboardDataSyncProvider.notifier);

      debugPrint('🔄 Data sync providers initialized');
    });
  }

  /// Handle smart data refresh when switching tabs
  void _handleTabSwitch(int newIndex) {
    debugPrint('🔄 Tab switch: $_selectedIndex → $newIndex');

    // Clear search states when switching tabs to prevent persistent search keywords
    _clearSearchStates();

    // Trigger smart refresh based on the tab being switched to
    switch (newIndex) {
      case 0: // Dashboard
        _refreshDashboardData();
        break;
      case 1: // Wallet
        _refreshWalletData();
        break;
      case 2: // Trip
        // Trip page doesn't need frequent refresh
        break;
      case 3: // Profile
        _refreshProfileData();
        break;
    }
  }

  /// Clear search states to prevent persistent search keywords
  void _clearSearchStates() {
    try {
      // Clear global places search provider state
      ref.read(placeSearchProvider.notifier).clearSearch();
      debugPrint('🔍 Cleared search states during tab switch');
    } catch (e) {
      debugPrint('❌ Error clearing search states: $e');
    }
  }

  /// Smart refresh for dashboard data
  void _refreshDashboardData() {
    final dashboardNotifier = ref.read(dashboardDataSyncProvider.notifier);
    dashboardNotifier.requestDebouncedRefresh();
    debugPrint('🔄 Requested dashboard data refresh');
  }

  /// Smart refresh for wallet data
  void _refreshWalletData() {
    final walletNotifier = ref.read(walletDataSyncProvider.notifier);
    walletNotifier.requestDebouncedRefresh();

    // Also trigger legacy wallet data preload for backward compatibility
    ServiceLocator().walletRepositoryImpl.getWalletInfo();
    debugPrint('🔄 Requested wallet data refresh');
  }

  /// Smart refresh for profile data
  void _refreshProfileData() {
    final profileNotifier = ref.read(profileDataSyncProvider.notifier);
    profileNotifier.requestDebouncedRefresh();
    debugPrint('🔄 Requested profile data refresh');
  }

  /// Handle back button press - exit app directly from main navigation
  Future<bool> _handleBackButtonPress() async {
    debugPrint('🔙 NAVIGATION: Back button pressed from main navigation');

    // Show exit confirmation dialog
    final shouldExit = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Exit App'),
        content: const Text('Are you sure you want to exit the app?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Exit'),
          ),
        ],
      ),
    );

    if (shouldExit == true) {
      debugPrint('🔙 NAVIGATION: User confirmed app exit');
      // Exit the app
      return true;
    } else {
      debugPrint('🔙 NAVIGATION: User cancelled app exit');
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = ref.watch(themeNotifierProvider.notifier).isDarkMode;

    return PopScope(
      canPop: false, // Prevent default back button behavior
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          final shouldExit = await _handleBackButtonPress();
          if (shouldExit && context.mounted) {
            // Exit the app using SystemNavigator
            debugPrint('🔙 NAVIGATION: Exiting app via SystemNavigator');
            SystemNavigator.pop();
          }
        }
      },
      child: Scaffold(
        extendBody:
            true, // Allow content to flow underneath for true floating effect
        body: IndexedStack(
          index: _selectedIndex,
          children:
              _buildPages(isDarkMode), // Pages that react to theme changes
        ),
        bottomNavigationBar: _buildOverlayNavigationBar(isDarkMode),
      ),
    );
  }

  /// Build the liquid glass navigation bar
  Widget _buildOverlayNavigationBar(bool isDarkMode) {
    return Container(
      height: 80.0, // Fixed height for consistent layout
      margin: const EdgeInsets.only(
        left: 12.0,
        right: 12.0,
        bottom: 8.0, // Small bottom margin for floating effect
      ),
      child: ClipRRect(
        borderRadius:
            BorderRadius.circular(28), // Match the LiquidGlass border radius
        child: BackdropFilter(
          filter: ImageFilter.blur(
              sigmaX: 15.0, sigmaY: 15.0), // Enhanced blur effect
          child: LiquidGlass(
            settings: LiquidGlassSettings(
              thickness: 20, // Increased thickness for more pronounced effect
              glassColor: isDarkMode
                  ? const Color(0x15FFFFFF) // Subtle white tint for dark mode
                  : const Color(0x15000000), // Subtle dark tint for light mode
              lightIntensity: 2.0, // High light intensity for visibility
              ambientStrength: 0.5, // Enhanced ambient lighting
              blend: 50, // Maximum blending for smooth glass effect
              lightAngle: 1.0, // Optimal light angle
            ),
            shape: LiquidRoundedSuperellipse(
              borderRadius:
                  Radius.circular(28), // Rounded for modern floating look
            ),
            glassContainsChild: true, // Child rendered within glass
            child: Container(
              // Semi-transparent container for the navigation content
              decoration: BoxDecoration(
                color: isDarkMode
                    ? Colors.black
                        .withAlpha(40) // Very subtle background for contrast
                    : Colors.white.withAlpha(40),
                borderRadius: BorderRadius.circular(28),
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: 8.0,
                vertical: 8.0, // Balanced padding for proper touch targets
              ),
              child: GNav(
                rippleColor: isDarkMode
                    ? Colors.white.withAlpha(30)
                    : Colors.black.withAlpha(30),
                hoverColor: isDarkMode
                    ? Colors.white.withAlpha(20)
                    : Colors.black.withAlpha(20),
                gap: 8,
                activeColor: Colors.white,
                iconSize: 24,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                duration: const Duration(milliseconds: 200),
                tabBackgroundColor: AppThemes
                    .primaryColor, // Solid active background for better visibility
                color: isDarkMode
                    ? Colors.white70
                    : Colors.black87, // High contrast for visibility
                textStyle: const TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                  fontSize: 14,
                ),
                curve: Curves.easeOutCubic,
                tabs: const [
                  GButton(icon: LineIcons.home, text: 'Home'),
                  GButton(icon: LineIcons.wallet, text: 'Wallet'),
                  GButton(icon: LineIcons.car, text: 'Trips'),
                  GButton(icon: LineIcons.user, text: 'Profile'),
                ],
                selectedIndex: _selectedIndex,
                onTabChange: (index) {
                  // Handle smart data refresh when switching tabs
                  _handleTabSwitch(index);

                  setState(() {
                    _selectedIndex = index;
                  });
                },
              ),
            ),
          ), // Close LiquidGlass
        ), // Close BackdropFilter
      ), // Close ClipRRect
    );
  }
}
