import 'package:flutter/material.dart';

/// A centralized class to store all the colors used in your application.
class AppColors {
  /// Primary color used throughout the app (e.g., for buttons, icons, etc.)
  static const Color primary = Color(0xFF8cc051);

  /// Accent color (e.g., used in app bars, headings, etc.)
  static const Color accent = Color(0xFF4A46F7);

  /// Background color for general surfaces.
  static const Color background = Colors.white;

  /// Scaffold background color.
  static const Color scaffoldBackground = Colors.white;

  /// Error color.
  static const Color error = Colors.red;

  /// Wallet Card Colors
  static const Color walletCardBackground = Color(0xFFEDF4FF);
  static const Color walletIconBackground = Color(0xFFBDD6FF);

  /// Vehicles Card Colors
  static const Color vehiclesCardBackground = Color(0xFFE7FAEB);
  static const Color vehiclesIconBackground = Color(0xFFC0ECCA);

  /// FAQ Page Colors
  static const Color faqHeaderBackground = Color(0xFF4A46F7);
  static const Color faqIconColor = Colors.white;
  static const Color faqTextColor = Colors.white;

  /// General text colors
  static const Color textBlack = Colors.black;
  static const Color textGrey = Colors.grey;
}
