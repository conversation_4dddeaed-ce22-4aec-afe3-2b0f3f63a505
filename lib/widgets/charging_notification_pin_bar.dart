 
import 'package:flutter/material.dart';

class ChargingNotificationPinBar extends StatefulWidget {
  final bool isCharging;
  final double chargePercentage;
  final String currentPower;
  final String energyDelivered;
  final String currentPrice;
  final double carbonSavings;
  final double energySourcePercentage;
  final String chargingTimer;
  final VoidCallback? onClose;
  final bool isVisible;
  final VoidCallback? onTap;

  const ChargingNotificationPinBar({
    super.key,
    required this.isCharging,
    required this.chargePercentage,
    required this.currentPower,
    required this.energyDelivered,
    required this.currentPrice,
    required this.carbonSavings,
    required this.energySourcePercentage,
    required this.chargingTimer,
    this.onClose,
    this.isVisible = true,
    this.onTap,
  });

  @override
  State<ChargingNotificationPinBar> createState() =>
      _ChargingNotificationPinBarState();
}

class _ChargingNotificationPinBarState extends State<ChargingNotificationPinBar>
    with TickerProviderStateMixin {
  late AnimationController _slideAnimationController;
  late AnimationController _pulseAnimationController;
  late AnimationController _progressAnimationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();

    // Animation for slide in/out
    _slideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: -1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _slideAnimationController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _slideAnimationController,
      curve: Curves.easeInOut,
    ));

    // Pulse animation for active indicator
    _pulseAnimationController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    )..repeat(reverse: true);

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseAnimationController,
      curve: Curves.easeInOut,
    ));

    // Progress animation
    _progressAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0,
      end: widget.chargePercentage,
    ).animate(CurvedAnimation(
      parent: _progressAnimationController,
      curve: Curves.easeInOut,
    ));

    if (widget.isVisible) {
      _slideAnimationController.forward();
    }

    if (widget.isCharging) {
      _progressAnimationController.forward();
    }
  }

  @override
  void didUpdateWidget(ChargingNotificationPinBar oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _slideAnimationController.forward();
      } else {
        _slideAnimationController.reverse();
      }
    }

    if (widget.chargePercentage != oldWidget.chargePercentage) {
      _progressAnimation = Tween<double>(
        begin: oldWidget.chargePercentage,
        end: widget.chargePercentage,
      ).animate(CurvedAnimation(
        parent: _progressAnimationController,
        curve: Curves.easeInOut,
      ));
      _progressAnimationController.forward(from: 0);
    }
  }

  @override
  void dispose() {
    _slideAnimationController.dispose();
    _pulseAnimationController.dispose();
    _progressAnimationController.dispose();
    super.dispose();
  }

  String get _statusText {
    if (!widget.isCharging) return "CHARGING COMPLETE";
    return "ACTIVE CHARGING";
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _slideAnimationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimation.value * 100),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: GestureDetector(
              onTap: widget.onTap,
              child: Container(
                width: 320,
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.08),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildTopSection(),
                    _buildProgressSection(),
                    _buildMetricsSection(),
                    _buildEnvironmentalSection(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTopSection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Active charging indicator with pulse animation
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: widget.isCharging ? _pulseAnimation.value : 1.0,
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: widget.isCharging 
                        ? const Color(0xFF4CAF50)
                        : const Color(0xFF9E9E9E),
                    shape: BoxShape.circle,
                    boxShadow: widget.isCharging
                        ? [
                            BoxShadow(
                              color: const Color(0xFF4CAF50).withValues(alpha: 0.5),
                              blurRadius: 4,
                              spreadRadius: 1,
                            ),
                          ]
                        : null,
                  ),
                ),
              );
            },
          ),
          const SizedBox(width: 8),
          Text(
            _statusText,
            style: TextStyle(
              color: widget.isCharging 
                  ? const Color(0xFF4CAF50)
                  : const Color(0xFF9E9E9E),
              fontSize: 12,
              fontWeight: FontWeight.bold,
              letterSpacing: 0.5,
            ),
          ),
          const Spacer(),
          
          // Battery percentage container
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
            decoration: BoxDecoration(
              color: const Color(0xFF4CAF50).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: const Color(0xFF4CAF50), width: 1),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Stack(
                  alignment: Alignment.center,
                  children: [
                    Icon(
                      Icons.battery_full,
                      color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
                      size: 18,
                    ),
                    ClipRect(
                      child: Align(
                        alignment: Alignment.centerLeft,
                        widthFactor: widget.chargePercentage,
                        child: const Icon(
                          Icons.battery_full,
                          color: Color(0xFF4CAF50),
                          size: 18,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(width: 4),
                Text(
                  '${(widget.chargePercentage * 100).toInt()}%',
                  style: const TextStyle(
                    color: Color(0xFF4CAF50),
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          const Spacer(),
          
          // Duration
          Text(
            widget.chargingTimer,
            style: const TextStyle(
              color: Color(0xFF666666),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 8),
          
          // Close button
          GestureDetector(
            onTap: widget.onClose,
            child: Container(
              padding: const EdgeInsets.all(4),
              child: const Icon(
                Icons.close,
                color: Color(0xFF999999),
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // Progress bar with car icon
          LayoutBuilder(
            builder: (context, constraints) {
              final barWidth = constraints.maxWidth;
              
              return Stack(
                clipBehavior: Clip.none,
                children: [
                  Container(
                    height: 8,
                    decoration: BoxDecoration(
                      color: const Color(0xFFE8F5E9),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: AnimatedBuilder(
                      animation: _progressAnimation,
                      builder: (context, child) {
                        return Row(
                          children: [
                            Expanded(
                              flex: (_progressAnimation.value * 100).toInt(),
                              child: Container(
                                decoration: BoxDecoration(
                                  gradient: const LinearGradient(
                                    colors: [Color(0xFF4CAF50), Color(0xFF66BB6A)],
                                  ),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                              ),
                            ),
                            Expanded(
                              flex: (100 - (_progressAnimation.value * 100)).toInt(),
                              child: Container(),
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                  // Car icon positioned at current progress
                  AnimatedBuilder(
                    animation: _progressAnimation,
                    builder: (context, child) {
                      return Positioned(
                        left: (_progressAnimation.value * barWidth) - 12,
                        top: -6,
                        child: Container(
                          width: 24,
                          height: 20,
                          decoration: BoxDecoration(
                            color: const Color(0xFF4CAF50),
                            borderRadius: BorderRadius.circular(4),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.2),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.directions_car,
                            color: Colors.white,
                            size: 14,
                          ),
                        ),
                      );
                    },
                  ),
                ],
              );
            },
          ),
          const SizedBox(height: 12),
          
          // Progress labels
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${(widget.chargePercentage * 100).toInt()}%',
                style: const TextStyle(
                  color: Color(0xFF4CAF50),
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Text(
                '100%',
                style: TextStyle(
                  color: Color(0xFF999999),
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMetricsSection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Power
          Expanded(
            child: _buildMetricItem(
              value: widget.currentPower.replaceAll(' kW', ''),
              unit: 'kW',
              label: 'Power',
              showBar: true,
            ),
          ),
          
          // Energy
          Expanded(
            child: _buildMetricItem(
              value: widget.energyDelivered.replaceAll(' kWh', ''),
              unit: 'kWh',
              label: 'Energy',
              showBar: true,
            ),
          ),
          
          // Cost
          Expanded(
            child: _buildCostItem(),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricItem({
    required String value,
    required String unit,
    required String label,
    bool showBar = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.baseline,
          textBaseline: TextBaseline.alphabetic,
          children: [
            Text(
              value,
              style: const TextStyle(
                color: Color(0xFF333333),
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 2),
            Text(
              unit,
              style: const TextStyle(
                color: Color(0xFF666666),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        if (showBar) ...[
          const SizedBox(height: 4),
          Container(
            width: 24,
            height: 3,
            decoration: BoxDecoration(
              color: const Color(0xFF4CAF50),
              borderRadius: BorderRadius.circular(1.5),
            ),
          ),
        ],
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            color: Color(0xFF999999),
            fontSize: 12,
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }

  Widget _buildCostItem() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.currentPrice,
          style: const TextStyle(
            color: Color(0xFF333333),
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'Cost',
          style: TextStyle(
            color: Color(0xFF999999),
            fontSize: 12,
            fontWeight: FontWeight.w400,
          ),
        ),
        const Text(
          '\$0.28/kWh',
          style: TextStyle(
            color: Color(0xFFBBBBBB),
            fontSize: 10,
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }

  Widget _buildEnvironmentalSection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Environmental Impact',
            style: TextStyle(
              color: Color(0xFF666666),
              fontSize: 14,
              fontWeight: FontWeight.w600,
              letterSpacing: 0.5,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildMetricItem(
                  value: widget.carbonSavings.toStringAsFixed(1),
                  unit: 'kg CO2',
                  label: 'Carbon Savings',
                  showBar: false,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMetricItem(
                  value: widget.energySourcePercentage.toStringAsFixed(0),
                  unit: '%',
                  label: 'Renewable Energy',
                  showBar: true,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}