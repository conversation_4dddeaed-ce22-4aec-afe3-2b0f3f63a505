import 'package:flutter/material.dart';
import 'package:ecoplug/services/auth_notification_service.dart';

/// FCM Test Topic Subscription Test Widget
/// Tests the "test_1" topic subscription functionality implemented in AuthNotificationService
class FCMTestTopicSubscriptionTest extends StatefulWidget {
  const FCMTestTopicSubscriptionTest({super.key});

  @override
  State<FCMTestTopicSubscriptionTest> createState() =>
      _FCMTestTopicSubscriptionTestState();
}

class _FCMTestTopicSubscriptionTestState
    extends State<FCMTestTopicSubscriptionTest> {
  final AuthNotificationService _authService = AuthNotificationService();
  bool _isLoading = false;
  Map<String, dynamic>? _subscriptionStatus;
  String? _testResult;

  @override
  void initState() {
    super.initState();
    _loadSubscriptionStatus();
  }

  /// Load current subscription status
  Future<void> _loadSubscriptionStatus() async {
    try {
      final status = await _authService.getTestTopicSubscriptionStatus();
      setState(() {
        _subscriptionStatus = status;
      });
    } catch (e) {
      debugPrint('❌ Error loading subscription status: $e');
    }
  }

  /// Test FCM topic subscription
  Future<void> _testTopicSubscription() async {
    setState(() {
      _isLoading = true;
      _testResult = null;
    });

    try {
      debugPrint('🔔 Testing FCM test_1 topic subscription...');

      // Test the subscription
      await _authService.testFCMTopicSubscription();

      // Reload status
      await _loadSubscriptionStatus();

      setState(() {
        _testResult =
            '✅ FCM test_1 topic subscription test completed successfully!';
      });

      debugPrint('✅ FCM topic subscription test completed');
    } catch (e) {
      setState(() {
        _testResult = '❌ FCM topic subscription test failed: $e';
      });
      debugPrint('❌ FCM topic subscription test failed: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Simulate login success to test automatic subscription
  Future<void> _simulateLoginSuccess() async {
    setState(() {
      _isLoading = true;
      _testResult = null;
    });

    try {
      debugPrint('🔐 Simulating login success...');

      // Simulate login success which should trigger test_1 topic subscription
      await _authService.onLoginSuccess(
        userId: 'test_user_${DateTime.now().millisecondsSinceEpoch}',
        userName: 'Test User',
        userEmail: '<EMAIL>',
      );

      // Wait a moment for subscription to complete
      await Future.delayed(const Duration(seconds: 2));

      // Reload status
      await _loadSubscriptionStatus();

      setState(() {
        _testResult =
            '✅ Login success simulation completed! Check subscription status below.';
      });

      debugPrint('✅ Login success simulation completed');
    } catch (e) {
      setState(() {
        _testResult = '❌ Login success simulation failed: $e';
      });
      debugPrint('❌ Login success simulation failed: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Clear subscription tracking data for testing
  Future<void> _clearSubscriptionData() async {
    setState(() {
      _isLoading = true;
      _testResult = null;
    });

    try {
      debugPrint('🧹 Clearing subscription data...');

      // Clear the subscription data
      await _authService.clearTestTopicSubscriptionData();

      // Reload status to show cleared state
      await _loadSubscriptionStatus();

      setState(() {
        _testResult = '✅ Subscription tracking data cleared successfully!';
      });

      debugPrint('✅ Subscription data cleared');
    } catch (e) {
      setState(() {
        _testResult = '❌ Failed to clear subscription data: $e';
      });
      debugPrint('❌ Failed to clear subscription data: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('FCM Test Topic Subscription Test'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            const Text(
              'FCM "test_1" Topic Subscription Test',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'This test verifies that the FCM topic subscription for "test_1" works correctly during login.',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 24),

            // Test Buttons Row 1
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testTopicSubscription,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Test Subscription'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _simulateLoginSuccess,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Simulate Login'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Test Buttons Row 2
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _loadSubscriptionStatus,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Refresh Status'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _clearSubscriptionData,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Clear Data'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Loading Indicator
            if (_isLoading)
              const Center(
                child: Column(
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 8),
                    Text('Processing...'),
                  ],
                ),
              ),

            // Test Result
            if (_testResult != null) ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _testResult!.startsWith('✅')
                      ? Colors.green.shade50
                      : Colors.red.shade50,
                  border: Border.all(
                    color: _testResult!.startsWith('✅')
                        ? Colors.green
                        : Colors.red,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _testResult!,
                  style: TextStyle(
                    color: _testResult!.startsWith('✅')
                        ? Colors.green.shade800
                        : Colors.red.shade800,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Subscription Status
            const Text(
              'Comprehensive Subscription Status:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: _subscriptionStatus != null
                  ? SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Basic Status
                          _buildStatusRow(
                              'Topic', _subscriptionStatus!['topic_name']),
                          _buildStatusRow('Subscribed',
                              _subscriptionStatus!['is_subscribed'],
                              isBoolean: true),

                          if (_subscriptionStatus!['subscription_time'] !=
                              null) ...[
                            const SizedBox(height: 8),
                            const Text('📅 Timing Information:',
                                style: TextStyle(fontWeight: FontWeight.bold)),
                            _buildStatusRow('Time',
                                _subscriptionStatus!['subscription_time']),
                            if (_subscriptionStatus![
                                    'subscription_duration_ms'] !=
                                null)
                              _buildStatusRow('Duration',
                                  '${_subscriptionStatus!['subscription_duration_ms']}ms'),
                          ],

                          if (_subscriptionStatus!['fcm_token_available'] !=
                              null) ...[
                            const SizedBox(height: 8),
                            const Text('🔑 FCM Information:',
                                style: TextStyle(fontWeight: FontWeight.bold)),
                            _buildStatusRow('FCM Token Available',
                                _subscriptionStatus!['fcm_token_available'],
                                isBoolean: true),
                          ],

                          if (_subscriptionStatus!['retry_attempt'] != null ||
                              _subscriptionStatus!['error_occurred'] !=
                                  null) ...[
                            const SizedBox(height: 8),
                            const Text('🔄 Error & Retry Information:',
                                style: TextStyle(fontWeight: FontWeight.bold)),
                            if (_subscriptionStatus!['retry_attempt'] != null)
                              _buildStatusRow('Retry Attempted',
                                  _subscriptionStatus!['retry_attempt'],
                                  isBoolean: true),
                            if (_subscriptionStatus!['error_occurred'] != null)
                              _buildStatusRow('Error Occurred',
                                  _subscriptionStatus!['error_occurred'],
                                  isBoolean: true),
                            if (_subscriptionStatus!['original_error'] != null)
                              _buildStatusRow('Original Error',
                                  _subscriptionStatus!['original_error'],
                                  isError: true),
                            if (_subscriptionStatus!['retry_error'] != null)
                              _buildStatusRow('Retry Error',
                                  _subscriptionStatus!['retry_error'],
                                  isError: true),
                            if (_subscriptionStatus!['final_status'] != null)
                              _buildStatusRow('Final Status',
                                  _subscriptionStatus!['final_status']),
                          ],

                          if (_subscriptionStatus!['error'] != null) ...[
                            const SizedBox(height: 8),
                            const Text('❌ System Error:',
                                style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.red)),
                            _buildStatusRow(
                                'Error', _subscriptionStatus!['error'],
                                isError: true),
                            if (_subscriptionStatus!['error_type'] != null)
                              _buildStatusRow('Error Type',
                                  _subscriptionStatus!['error_type'],
                                  isError: true),
                          ],

                          const SizedBox(height: 8),
                          const Text('ℹ️ Debug Information:',
                              style: TextStyle(fontWeight: FontWeight.bold)),
                          _buildStatusRow('Status Retrieved',
                              _subscriptionStatus!['status_retrieved_at']),
                        ],
                      ),
                    )
                  : const Text('Loading comprehensive subscription status...'),
            ),
            const SizedBox(height: 16),

            // Instructions
            const Text(
              'Instructions:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '1. Tap "Test Subscription" to manually test FCM subscription\n'
              '2. Tap "Simulate Login" to test automatic subscription during login\n'
              '3. Tap "Refresh Status" to update subscription status\n'
              '4. Tap "Clear Data" to reset tracking data\n'
              '5. Check the debug console for detailed logs',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  /// Helper method to build status rows with consistent formatting
  Widget _buildStatusRow(String label, dynamic value,
      {bool isBoolean = false, bool isError = false}) {
    Color? textColor;
    String displayValue;

    if (value == null) {
      displayValue = 'null';
      textColor = Colors.grey;
    } else if (isBoolean) {
      displayValue = value.toString();
      textColor = value == true ? Colors.green : Colors.red;
    } else if (isError) {
      displayValue = value.toString();
      textColor = Colors.red;
    } else {
      displayValue = value.toString();
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              displayValue,
              style: TextStyle(
                color: textColor,
                fontFamily: isError ? 'monospace' : null,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
