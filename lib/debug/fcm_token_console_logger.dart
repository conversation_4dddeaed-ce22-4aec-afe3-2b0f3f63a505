import 'package:flutter/foundation.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:ecoplug/services/fcm_service.dart';

/// FCM Token Console Logger
/// Logs FCM tokens to debug console only - no UI components
/// Only works in debug mode (kDebugMode)
class FCMTokenConsoleLogger {
  static final FCMService _fcmService = FCMService();

  /// Log current FCM token to console
  /// Only works in debug mode
  static Future<void> logCurrentToken() async {
    if (!kDebugMode) return;

    try {
      debugPrint('🔥 ===== FCM TOKEN DEBUG CONSOLE =====');

      // Try to get token from existing FCM service
      final existingToken = await _fcmService.getToken();

      if (existingToken != null && existingToken.isNotEmpty) {
        debugPrint('🔥 EXISTING FCM TOKEN (from FCMService):');
        debugPrint('🔥 Length: ${existingToken.length} characters');
        debugPrint('🔥 Token: $existingToken');
        debugPrint('🔥 Preview: ${existingToken.substring(0, 50)}...');
      } else {
        debugPrint('🔥 No existing token found in FCMService');
      }

      debugPrint('🔥 =====================================');
    } catch (e) {
      debugPrint('❌ Error getting existing FCM token: $e');
    }
  }

  /// Generate new FCM token and log to console
  /// Only works in debug mode
  static Future<void> generateAndLogToken() async {
    if (!kDebugMode) return;

    try {
      debugPrint('🔥 ===== GENERATING NEW FCM TOKEN =====');

      // Get a fresh token from Firebase Messaging
      final messaging = FirebaseMessaging.instance;
      await messaging.deleteToken(); // Delete current token
      final token = await messaging.getToken(); // Get new token

      if (token != null && token.isNotEmpty) {
        debugPrint('🔥 NEW FCM TOKEN GENERATED:');
        debugPrint('🔥 Length: ${token.length} characters');
        debugPrint('🔥 Token: $token');
        debugPrint('🔥 Preview: ${token.substring(0, 50)}...');
      } else {
        debugPrint('❌ Failed to generate FCM token');
      }

      debugPrint('🔥 ====================================');
    } catch (e) {
      debugPrint('❌ Error generating FCM token: $e');
    }
  }

  /// Log both existing and new FCM tokens to console
  /// Only works in debug mode
  static Future<void> logAllTokens() async {
    if (!kDebugMode) return;

    debugPrint('🔥 ===== FCM TOKEN COMPLETE DEBUG LOG =====');

    // Log existing token
    await logCurrentToken();

    // Generate and log new token
    await generateAndLogToken();

    // Log direct Firebase Messaging token
    await _logDirectFirebaseToken();

    debugPrint('🔥 ========================================');
  }

  /// Log token directly from Firebase Messaging
  static Future<void> _logDirectFirebaseToken() async {
    try {
      debugPrint('🔥 ===== DIRECT FIREBASE MESSAGING TOKEN =====');

      final messaging = FirebaseMessaging.instance;
      final token = await messaging.getToken();

      if (token != null && token.isNotEmpty) {
        debugPrint('🔥 DIRECT FIREBASE TOKEN:');
        debugPrint('🔥 Length: ${token.length} characters');
        debugPrint('🔥 Token: $token');
        debugPrint('🔥 Preview: ${token.substring(0, 50)}...');
      } else {
        debugPrint('🔥 No direct Firebase token available');
      }

      debugPrint('🔥 ============================================');
    } catch (e) {
      debugPrint('❌ Error getting direct Firebase token: $e');
    }
  }

  /// Log FCM token with timestamp
  static Future<void> logTokenWithTimestamp() async {
    if (!kDebugMode) return;

    final timestamp = DateTime.now().toIso8601String();
    debugPrint('🔥 ===== FCM TOKEN LOG - $timestamp =====');

    await logCurrentToken();

    debugPrint('🔥 =======================================');
  }

  /// Quick debug method - just log the current token
  static Future<void> quickLog() async {
    if (!kDebugMode) return;

    try {
      final messaging = FirebaseMessaging.instance;
      final token = await messaging.getToken();

      if (token != null) {
        debugPrint('🔥 FCM TOKEN: $token');
      } else {
        debugPrint('🔥 No FCM token available');
      }
    } catch (e) {
      debugPrint('❌ FCM token error: $e');
    }
  }

  /// Log token refresh event
  static void logTokenRefresh(String newToken) {
    if (!kDebugMode) return;

    debugPrint('🔥 ===== FCM TOKEN REFRESHED =====');
    debugPrint('🔥 New Token: $newToken');
    debugPrint('🔥 Length: ${newToken.length} characters');
    debugPrint('🔥 Timestamp: ${DateTime.now().toIso8601String()}');
    debugPrint('🔥 ===============================');
  }

  /// Log FCM service status
  static Future<void> logFCMStatus() async {
    if (!kDebugMode) return;

    try {
      debugPrint('🔥 ===== FCM SERVICE STATUS =====');

      // Check if FCM service is available
      debugPrint('🔥 FCM Service Available: true');

      // Check notification permissions
      final messaging = FirebaseMessaging.instance;
      final settings = await messaging.requestPermission();
      debugPrint('🔥 Notification Permission: ${settings.authorizationStatus}');

      // Check if we can get a token
      try {
        final token = await messaging.getToken();
        debugPrint('🔥 Token Available: ${token != null && token.isNotEmpty}');
      } catch (e) {
        debugPrint('🔥 Token Error: $e');
      }

      debugPrint('🔥 ==============================');
    } catch (e) {
      debugPrint('❌ Error checking FCM status: $e');
    }
  }
}

/// Extension to easily add FCM token logging to any class
extension FCMTokenLogging on Object {
  /// Log FCM token from any class
  Future<void> logFCMToken() async {
    await FCMTokenConsoleLogger.logCurrentToken();
  }

  /// Quick FCM token log
  Future<void> quickFCMLog() async {
    await FCMTokenConsoleLogger.quickLog();
  }
}
