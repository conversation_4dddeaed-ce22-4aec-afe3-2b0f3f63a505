import 'package:flutter/material.dart';
import '../config/notification_config.dart';

/// EcoPlug Notification Icon Verification Tool
/// Verifies notification icon configuration for production deployment
class NotificationIconVerification {
  /// Verify notification icon configuration without sending test notifications
  static Future<void> runCompleteVerification() async {
    debugPrint('🔍 ===== VERIFYING NOTIFICATION ICON CONFIGURATION =====');

    try {
      // Only verify configuration - no test notifications for production
      verifyIconConfiguration();

      debugPrint('✅ ===== NOTIFICATION ICON VERIFICATION COMPLETED =====');
      debugPrint('📱 All notification services configured to use EcoPlug launcher icon');
      debugPrint('🔔 Charging notifications handled by FCM from server');
    } catch (e) {
      debugPrint('❌ Error during notification verification: $e');
      rethrow;
    }
  }

  /// Verify notification icon configuration
  static void verifyIconConfiguration() {
    debugPrint('🔍 ===== VERIFYING NOTIFICATION ICON CONFIGURATION =====');

    // Check notification config constants
    debugPrint('📱 Default Icon: ${NotificationConfig.defaultIcon}');
    debugPrint('📱 Default Large Icon: ${NotificationConfig.defaultLargeIcon}');

    // Verify expected values
    const expectedIcon = '@drawable/ic_launcher';

    if (NotificationConfig.defaultIcon == expectedIcon) {
      debugPrint('✅ Default icon correctly configured');
    } else {
      debugPrint('❌ Default icon misconfigured: ${NotificationConfig.defaultIcon}');
    }

    if (NotificationConfig.defaultLargeIcon == expectedIcon) {
      debugPrint('✅ Default large icon correctly configured');
    } else {
      debugPrint('❌ Default large icon misconfigured: ${NotificationConfig.defaultLargeIcon}');
    }

    debugPrint('✅ ===== ICON CONFIGURATION VERIFICATION COMPLETED =====');
  }

  /// Show verification results dialog
  static void showVerificationDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.verified, color: Colors.green),
            SizedBox(width: 8),
            Text('Notification Icon Verification'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('✅ Configuration Verified'),
            Text('✅ EcoPlug Launcher Icon Set'),
            Text('✅ FCM Ready for Server Notifications'),
            SizedBox(height: 16),
            Text(
              'All notifications configured to use EcoPlug launcher icon.',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'Charging notifications will be handled by FCM from server.',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

/// Verification Widget for production deployment
class NotificationIconVerificationWidget extends StatelessWidget {
  const NotificationIconVerificationWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Icon Verification'),
        backgroundColor: const Color(0xFF4CAF50),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info, color: Colors.blue),
                        SizedBox(width: 8),
                        Text(
                          'EcoPlug Notification Icon Verification',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    Text(
                      'This tool verifies that all notification services are configured to use the EcoPlug launcher icon for consistent branding.',
                    ),
                    SizedBox(height: 8),
                    Text(
                      'No test notifications will be sent in production mode.',
                      style: TextStyle(
                        fontStyle: FontStyle.italic,
                        color: Colors.orange,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () async {
                await NotificationIconVerification.runCompleteVerification();
                if (context.mounted) {
                  NotificationIconVerification.showVerificationDialog(context);
                }
              },
              icon: const Icon(Icons.verified),
              label: const Text('Verify Configuration'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4CAF50),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
            ),
            const SizedBox(height: 8),
            ElevatedButton.icon(
              onPressed: () {
                NotificationIconVerification.verifyIconConfiguration();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Configuration verified - check debug console'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              icon: const Icon(Icons.settings),
              label: const Text('Check Icon Configuration'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
            ),
            const SizedBox(height: 16),
            const Card(
              color: Color(0xFFF5F5F5),
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Production Configuration:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text('• All notifications use EcoPlug launcher icon'),
                    Text('• Charging notifications via FCM only'),
                    Text('• No test notifications in production'),
                    Text('• Consistent branding across all notification types'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
