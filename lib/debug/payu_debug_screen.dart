import 'package:flutter/material.dart';
import '../services/payment/payu_service.dart';
import 'payu_debug_helper.dart';

/// Debug screen for testing PayU integration
class PayUDebugScreen extends StatefulWidget {
  const PayUDebugScreen({super.key});

  @override
  State<PayUDebugScreen> createState() => _PayUDebugScreenState();
}

class _PayUDebugScreenState extends State<PayUDebugScreen> {
  final _merchantKeyController = TextEditingController();
  final _environmentController = TextEditingController(text: '1'); // Test environment
  final _amountController = TextEditingController(text: '10.00');
  
  bool _isLoading = false;
  String _debugOutput = '';

  @override
  void initState() {
    super.initState();
    // Set default test values
    _merchantKeyController.text = 'YOUR_MERCHANT_KEY'; // Replace with actual key
  }

  void _addDebugOutput(String message) {
    setState(() {
      _debugOutput += '${DateTime.now().toString().substring(11, 19)}: $message\n';
    });
    debugPrint(message);
  }

  void _clearDebugOutput() {
    setState(() {
      _debugOutput = '';
    });
  }

  Future<void> _testSDKInitialization() async {
    setState(() {
      _isLoading = true;
    });

    _addDebugOutput('🔔 Testing PayU SDK initialization...');

    try {
      final result = await PayUDebugHelper.testSDKInitialization(
        merchantKey: _merchantKeyController.text,
        environment: _environmentController.text,
      );

      if (result) {
        _addDebugOutput('✅ SDK initialization successful');
      } else {
        _addDebugOutput('❌ SDK initialization failed');
      }
    } catch (e) {
      _addDebugOutput('❌ SDK initialization error: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  void _testCallbacks() {
    _addDebugOutput('🔔 Testing PayU callbacks...');
    PayUDebugHelper.testCallbackRegistration();
    PayUDebugHelper.simulateCallbacks();
    _addDebugOutput('✅ Callback test completed - check console logs');
  }

  void _testResponseTracking() {
    _addDebugOutput('🔔 Testing response tracking...');
    PayUDebugHelper.testResponseTracking();
    _addDebugOutput('✅ Response tracking test completed');
  }

  Future<void> _testPaymentFlow() async {
    setState(() {
      _isLoading = true;
    });

    _addDebugOutput('🔔 Testing complete payment flow...');

    try {
      // First initialize SDK
      final sdkInitialized = await PayUService.init(
        merchantKey: _merchantKeyController.text,
        environment: _environmentController.text,
        enableLogging: true,
      );

      if (!sdkInitialized) {
        _addDebugOutput('❌ SDK not initialized');
        return;
      }

      // Generate test parameters
      final testParams = PayUDebugHelper.generateTestPaymentParams(
        merchantKey: _merchantKeyController.text,
        environment: _environmentController.text,
        amount: double.tryParse(_amountController.text) ?? 10.0,
      );

      // Validate parameters
      final validation = PayUDebugHelper.validatePaymentParameters(testParams);
      final allValid = validation.values.every((valid) => valid);

      if (!allValid) {
        _addDebugOutput('❌ Payment parameters validation failed');
        return;
      }

      _addDebugOutput('✅ Payment parameters validated');
      _addDebugOutput('🔔 Starting PayU payment...');

      // Start payment
      final result = await PayUService.startPayment(
        paymentParams: testParams,
        timeout: const Duration(minutes: 2),
      );

      _addDebugOutput('🔔 Payment result: ${result.type}');
      _addDebugOutput('🔔 Payment message: ${result.message}');
      _addDebugOutput('🔔 Payment data: ${result.data}');

    } catch (e) {
      _addDebugOutput('❌ Payment flow error: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _runComprehensiveTest() async {
    setState(() {
      _isLoading = true;
    });

    _addDebugOutput('🔔 Running comprehensive PayU test...');

    try {
      await PayUDebugHelper.runComprehensiveTest(
        merchantKey: _merchantKeyController.text,
        environment: _environmentController.text,
      );
      _addDebugOutput('✅ Comprehensive test completed - check console logs');
    } catch (e) {
      _addDebugOutput('❌ Comprehensive test error: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('PayU Debug Console'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Configuration Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'PayU Configuration',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _merchantKeyController,
                      decoration: const InputDecoration(
                        labelText: 'Merchant Key',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _environmentController,
                      decoration: const InputDecoration(
                        labelText: 'Environment (0=Prod, 1=Test)',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _amountController,
                      decoration: const InputDecoration(
                        labelText: 'Test Amount',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Test Buttons
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Debug Tests',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        ElevatedButton(
                          onPressed: _isLoading ? null : _testSDKInitialization,
                          child: const Text('Test SDK Init'),
                        ),
                        ElevatedButton(
                          onPressed: _isLoading ? null : _testCallbacks,
                          child: const Text('Test Callbacks'),
                        ),
                        ElevatedButton(
                          onPressed: _isLoading ? null : _testResponseTracking,
                          child: const Text('Test Tracking'),
                        ),
                        ElevatedButton(
                          onPressed: _isLoading ? null : _testPaymentFlow,
                          child: const Text('Test Payment'),
                        ),
                        ElevatedButton(
                          onPressed: _isLoading ? null : _runComprehensiveTest,
                          child: const Text('Full Test'),
                        ),
                        ElevatedButton(
                          onPressed: _clearDebugOutput,
                          child: const Text('Clear Log'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Debug Output
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Text(
                            'Debug Output',
                            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                          ),
                          const Spacer(),
                          if (_isLoading)
                            const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Expanded(
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            border: Border.all(color: Colors.grey[300]!),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: SingleChildScrollView(
                            child: Text(
                              _debugOutput.isEmpty 
                                  ? 'Debug output will appear here...\nCheck console logs for detailed information.'
                                  : _debugOutput,
                              style: const TextStyle(
                                fontFamily: 'monospace',
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _merchantKeyController.dispose();
    _environmentController.dispose();
    _amountController.dispose();
    super.dispose();
  }
}
