import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../services/fcm_subscription_service.dart';

/// Test widget to verify FCM topic subscription with backend-specified format: Charging_id
class FCMTopicSubscriptionTest extends StatefulWidget {
  const FCMTopicSubscriptionTest({super.key});

  @override
  State<FCMTopicSubscriptionTest> createState() => _FCMTopicSubscriptionTestState();
}

class _FCMTopicSubscriptionTestState extends State<FCMTopicSubscriptionTest> {
  final FCMSubscriptionService _fcmService = FCMSubscriptionService();
  final TextEditingController _chargingIdController = TextEditingController();
  
  bool _isLoading = false;
  String? _lastResult;
  String? _currentSubscription;

  @override
  void initState() {
    super.initState();
    _initializeService();
  }

  Future<void> _initializeService() async {
    try {
      await _fcmService.initialize();
      setState(() {
        _currentSubscription = _fcmService.currentTransactionId;
      });
    } catch (e) {
      debugPrint('Error initializing FCM service: $e');
    }
  }

  Future<void> _testSubscription() async {
    if (_chargingIdController.text.trim().isEmpty) {
      setState(() {
        _lastResult = 'Please enter a charging ID';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _lastResult = null;
    });

    try {
      final chargingId = _chargingIdController.text.trim();
      debugPrint('🧪 Testing FCM subscription with charging ID: $chargingId');
      debugPrint('🧪 Expected topic format: Charging_$chargingId');

      final success = await _fcmService.subscribeToChargingNotifications(chargingId);

      setState(() {
        _lastResult = success 
            ? '✅ Successfully subscribed to topic: Charging_$chargingId'
            : '❌ Failed to subscribe to topic: Charging_$chargingId';
        _currentSubscription = success ? chargingId : null;
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Error: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testUnsubscription() async {
    setState(() {
      _isLoading = true;
      _lastResult = null;
    });

    try {
      final success = await _fcmService.unsubscribeFromChargingNotifications();

      setState(() {
        _lastResult = success 
            ? '✅ Successfully unsubscribed from charging notifications'
            : '❌ Failed to unsubscribe from charging notifications';
        _currentSubscription = success ? null : _currentSubscription;
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Error: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('FCM Topic Subscription Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Info Card
            Card(
              color: Colors.blue.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '🔔 FCM Topic Subscription Test',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Backend team confirmed:',
                      style: TextStyle(fontWeight: FontWeight.w600),
                    ),
                    const Text('• No server endpoints needed'),
                    const Text('• Use FCM topic self-subscription'),
                    const Text('• Topic format: Charging_id'),
                    const SizedBox(height: 8),
                    Text(
                      'FCM Token: ${_fcmService.fcmToken?.substring(0, 20) ?? 'Not available'}...',
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Current Subscription Status
            Card(
              color: _currentSubscription != null ? Colors.green.shade50 : Colors.grey.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current Subscription Status',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: _currentSubscription != null ? Colors.green : Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _currentSubscription != null 
                          ? '✅ Subscribed to: Charging_$_currentSubscription'
                          : '❌ No active subscription',
                      style: TextStyle(
                        color: _currentSubscription != null ? Colors.green : Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Charging ID Input
            TextField(
              controller: _chargingIdController,
              decoration: const InputDecoration(
                labelText: 'Charging ID',
                hintText: 'Enter charging session ID (e.g., 12345)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.electric_car),
              ),
              keyboardType: TextInputType.text,
            ),
            const SizedBox(height: 16),

            // Subscribe Button
            ElevatedButton.icon(
              onPressed: _isLoading ? null : _testSubscription,
              icon: _isLoading 
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.notifications_active),
              label: Text(_isLoading ? 'Subscribing...' : 'Subscribe to Topic'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
            const SizedBox(height: 8),

            // Unsubscribe Button
            ElevatedButton.icon(
              onPressed: _isLoading || _currentSubscription == null ? null : _testUnsubscription,
              icon: const Icon(Icons.notifications_off),
              label: const Text('Unsubscribe'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
            const SizedBox(height: 20),

            // Result Display
            if (_lastResult != null)
              Card(
                color: _lastResult!.startsWith('✅') ? Colors.green.shade50 : Colors.red.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Test Result:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _lastResult!,
                        style: TextStyle(
                          color: _lastResult!.startsWith('✅') ? Colors.green : Colors.red,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            const Spacer(),

            // Debug Info
            if (kDebugMode)
              Card(
                color: Colors.grey.shade100,
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Debug Info (Debug Mode Only):',
                        style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'This test verifies FCM topic subscription using the backend-specified format.',
                        style: TextStyle(fontSize: 11, color: Colors.grey.shade600),
                      ),
                      Text(
                        'Check debug console for detailed logs.',
                        style: TextStyle(fontSize: 11, color: Colors.grey.shade600),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _chargingIdController.dispose();
    super.dispose();
  }
}
