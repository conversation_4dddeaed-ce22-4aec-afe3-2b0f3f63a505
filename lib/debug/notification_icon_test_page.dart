import 'package:flutter/material.dart';
import 'notification_icon_verification.dart';

/// Test page for easy access to notification icon verification
class NotificationIconTestPage extends StatelessWidget {
  const NotificationIconTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const NotificationIconVerificationWidget();
  }
}

/// Helper function to navigate to the notification icon test page
void showNotificationIconTest(BuildContext context) {
  Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => const NotificationIconTestPage(),
    ),
  );
}
