import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

import '../services/payment/payu_service.dart';

/// A debug tool to test PayU integration fixes
class PayUIntegrationTester extends StatefulWidget {
  const PayUIntegrationTester({super.key});

  @override
  State<PayUIntegrationTester> createState() => _PayUIntegrationTesterState();
}

class _PayUIntegrationTesterState extends State<PayUIntegrationTester> {
  final TextEditingController _merchantKeyController = TextEditingController(text: 'YOUR_MERCHANT_KEY');
  final TextEditingController _amountController = TextEditingController(text: '10.0');
  final TextEditingController _environmentController = TextEditingController(text: '1'); // Test environment
  
  final List<String> _logs = [];
  bool _isLoading = false;

  @override
  void dispose() {
    _merchantKeyController.dispose();
    _amountController.dispose();
    _environmentController.dispose();
    super.dispose();
  }

  void _addLog(String log) {
    setState(() {
      _logs.add('${DateTime.now().toString().substring(11, 19)} - $log');
      if (_logs.length > 100) {
        _logs.removeAt(0);
      }
    });
  }

  Future<void> _testHashGeneration() async {
    setState(() {
      _isLoading = true;
    });

    _addLog('🔐 Testing backend hash generation...');

    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        _addLog('❌ No auth token available');
        setState(() {
          _isLoading = false;
        });
        return;
      }

      final payload = {
        'hashName': 'payment_hash',
        'hashString': 'test_string|${_merchantKeyController.text}|${_amountController.text}|product|user|<EMAIL>|udf1|udf2|udf3|udf4|udf5||||||',
        'hashType': 'SHA512',
        'postSalt': 'test_post_salt'
      };

      _addLog('📤 Sending hash request to backend...');
      _addLog('📦 Payload: ${jsonEncode(payload)}');
      
      final response = await http.post(
        Uri.parse('https://api2.eeil.online/api/v1/user/payu/get-hash'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(payload),
      );

      _addLog('📥 Response status: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final hash = responseData['hash']?.toString() ?? '';
        
        _addLog('✅ Hash received: ${hash.substring(0, 20)}...');
        _addLog('✅ Hash length: ${hash.length}');
      } else {
        _addLog('❌ Hash generation failed: ${response.body}');
      }
    } catch (e) {
      _addLog('❌ Error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testReverseHash() async {
    setState(() {
      _isLoading = true;
    });

    _addLog('🔄 Testing reverse hash generation...');

    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        _addLog('❌ No auth token available');
        setState(() {
          _isLoading = false;
        });
        return;
      }

      final txnId = 'test_txn_${DateTime.now().millisecondsSinceEpoch}';
      final payload = {
        'txnid': txnId,
        'amount': _amountController.text,
        'status': 'success',
      };

      _addLog('📤 Sending reverse hash request...');
      _addLog('📦 Payload: ${jsonEncode(payload)}');
      
      final response = await http.post(
        Uri.parse('https://api2.eeil.online/api/v1/user/payu/reverse-hash'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(payload),
      );

      _addLog('📥 Response status: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final hash = responseData['hash']?.toString() ?? '';
        
        _addLog('✅ Reverse hash received: ${hash.substring(0, 20)}...');
        _addLog('✅ Hash length: ${hash.length}');
      } else {
        _addLog('❌ Reverse hash generation failed: ${response.body}');
      }
    } catch (e) {
      _addLog('❌ Error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testVerifyPayment() async {
    setState(() {
      _isLoading = true;
    });

    _addLog('🔍 Testing PayU verify payment API...');

    try {
      final txnId = 'test_txn_${DateTime.now().millisecondsSinceEpoch}';
      final merchantKey = _merchantKeyController.text;
      
      _addLog('📤 Calling PayU verify payment API...');
      _addLog('📦 Transaction ID: $txnId');
      
      final response = await http.post(
        Uri.parse('https://info.payu.in/merchant/postservice.php?form=2'),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'key=$merchantKey&command=verify_payment&var1=$txnId',
      );

      _addLog('📥 Response status: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        _addLog('✅ Verify payment response: ${response.body}');
      } else {
        _addLog('❌ Verify payment failed: ${response.body}');
      }
    } catch (e) {
      _addLog('❌ Error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testFullPayment() async {
    setState(() {
      _isLoading = true;
    });

    _addLog('💳 Starting full PayU payment test...');

    try {
      // First initialize SDK
      _addLog('🔄 Initializing PayU SDK...');
      final sdkInitialized = await PayUService.init(
        merchantKey: _merchantKeyController.text,
        environment: _environmentController.text,
        enableLogging: true,
      );

      if (!sdkInitialized) {
        _addLog('❌ SDK not initialized');
        setState(() {
          _isLoading = false;
        });
        return;
      }

      _addLog('✅ SDK initialized successfully');

      // Generate test parameters
      final txnId = 'test_${DateTime.now().millisecondsSinceEpoch}';
      final testParams = {
        'key': _merchantKeyController.text,
        'txnid': txnId,
        'amount': _amountController.text,
        'productinfo': 'Test Payment',
        'firstname': 'Test User',
        'email': '<EMAIL>',
        'phone': '9999999999',
        'surl': 'com.eeil.ecoplug://payu/success',
        'furl': 'com.eeil.ecoplug://payu/failure',
        'environment': _environmentController.text,
        'userCredential': '${_merchantKeyController.text}:test_user',
        'udf1': 'test_data',
        'udf2': 'debug_mode',
      };

      _addLog('📦 Payment parameters: ${jsonEncode(testParams)}');
      _addLog('🚀 Starting PayU payment...');

      // Start payment
      final result = await PayUService.startPayment(
        paymentParams: testParams,
        timeout: const Duration(minutes: 5),
      );

      _addLog('📥 Payment result: ${result.type}');
      _addLog('📥 Payment message: ${result.message}');
      _addLog('📥 Payment data: ${result.data}');

    } catch (e) {
      _addLog('❌ Payment flow error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('PayU Integration Tester'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            TextField(
              controller: _merchantKeyController,
              decoration: const InputDecoration(
                labelText: 'Merchant Key',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _amountController,
              decoration: const InputDecoration(
                labelText: 'Amount',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _environmentController,
              decoration: const InputDecoration(
                labelText: 'Environment (0=prod, 1=test)',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testHashGeneration,
                    child: const Text('Test Hash Generation'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testReverseHash,
                    child: const Text('Test Reverse Hash'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testVerifyPayment,
                    child: const Text('Test Verify Payment'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testFullPayment,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                    ),
                    child: const Text('Test Full Payment'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text('Logs:', style: TextStyle(fontWeight: FontWeight.bold)),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: ListView.builder(
                  itemCount: _logs.length,
                  itemBuilder: (context, index) {
                    return Text(_logs[index], style: const TextStyle(fontFamily: 'monospace'));
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
