import 'package:flutter/foundation.dart';
import 'package:ecoplug/models/charging_session.dart';
import 'package:ecoplug/services/charging_session_notification_manager.dart';

/// Active Charging Notification Tester
/// Debug-only utility to test active charging notifications in Android notification tray
/// NO UI COMPONENTS - Only Android notification tray testing
class ActiveChargingNotificationTester {
  static final ChargingSessionNotificationManager _notificationManager = 
      ChargingSessionNotificationManager();

  /// Test active charging notification in Android tray
  /// This creates a test charging session and shows persistent notification
  static Future<Map<String, dynamic>> testActiveChargingNotification() async {
    if (!kDebugMode) {
      debugPrint('❌ Active charging notification testing only available in debug mode');
      return {'success': false, 'error': 'Debug mode only'};
    }

    try {
      debugPrint('🔋 ===== TESTING ACTIVE CHARGING NOTIFICATION =====');
      debugPrint('🔋 This will show a persistent notification in Android tray');
      debugPrint('🔋 NO UI changes will be made to the app');

      // Initialize notification manager
      await _notificationManager.initialize();

      // Create test charging session
      final testSession = ChargingSession(
        id: 'TEST_SESSION_${DateTime.now().millisecondsSinceEpoch}',
        stationUid: 'TEST_STATION_001',
        connectorId: 'CONNECTOR_A',
        startTime: DateTime.now(),
        currentCharge: 0.45, // 45%
        currentPower: 7.2, // 7.2 kW
        energyDelivered: 12.5, // 12.5 kWh
        cost: 187.50, // ₹187.50
        co2Saved: 6.8, // 6.8 kg CO2
      );

      // Start active charging notification
      await _notificationManager.startChargingSession(testSession);

      debugPrint('✅ Test charging notification started');
      debugPrint('🔋 Check Android notification tray for persistent notification');
      debugPrint('🔋 Notification should show: "Charging Active • 45%"');
      debugPrint('🔋 Tap notification to test navigation');

      return {
        'success': true,
        'session_id': testSession.id,
        'notification_started': true,
        'message': 'Check Android notification tray for persistent charging notification',
      };
    } catch (e) {
      debugPrint('❌ Error testing active charging notification: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Test notification updates with changing charging data
  static Future<Map<String, dynamic>> testNotificationUpdates() async {
    if (!kDebugMode) {
      debugPrint('❌ Notification update testing only available in debug mode');
      return {'success': false, 'error': 'Debug mode only'};
    }

    try {
      debugPrint('🔋 ===== TESTING NOTIFICATION UPDATES =====');
      debugPrint('🔋 This will update the persistent notification with new data');

      // Create updated charging session with different values
      final updatedSession = ChargingSession(
        id: 'TEST_SESSION_${DateTime.now().millisecondsSinceEpoch}',
        stationUid: 'TEST_STATION_001',
        connectorId: 'CONNECTOR_A',
        startTime: DateTime.now().subtract(const Duration(minutes: 30)),
        currentCharge: 0.75, // 75% (increased)
        currentPower: 6.8, // 6.8 kW (slightly decreased)
        energyDelivered: 18.3, // 18.3 kWh (increased)
        cost: 275.80, // ₹275.80 (increased)
        co2Saved: 9.4, // 9.4 kg CO2 (increased)
      );

      // Update the notification
      await _notificationManager.updateChargingSession(updatedSession);

      debugPrint('✅ Notification updated with new charging data');
      debugPrint('🔋 Check Android notification tray for updated values');
      debugPrint('🔋 Notification should now show: "Charging Active • 75%"');

      return {
        'success': true,
        'session_id': updatedSession.id,
        'notification_updated': true,
        'message': 'Notification updated - check Android tray for new values',
      };
    } catch (e) {
      debugPrint('❌ Error testing notification updates: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Test stopping the active charging notification
  static Future<Map<String, dynamic>> testStopNotification() async {
    if (!kDebugMode) {
      debugPrint('❌ Stop notification testing only available in debug mode');
      return {'success': false, 'error': 'Debug mode only'};
    }

    try {
      debugPrint('🔋 ===== TESTING STOP NOTIFICATION =====');
      debugPrint('🔋 This will remove the persistent notification from Android tray');

      // Stop the charging notification
      await _notificationManager.stopChargingSession();

      debugPrint('✅ Charging notification stopped');
      debugPrint('🔋 Persistent notification should be removed from Android tray');

      return {
        'success': true,
        'notification_stopped': true,
        'message': 'Notification stopped - should be removed from Android tray',
      };
    } catch (e) {
      debugPrint('❌ Error stopping notification: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Run complete notification test cycle
  static Future<Map<String, dynamic>> runCompleteNotificationTest() async {
    if (!kDebugMode) {
      debugPrint('❌ Complete notification testing only available in debug mode');
      return {'success': false, 'error': 'Debug mode only'};
    }

    final results = <String, dynamic>{};

    try {
      debugPrint('🔋 ===== RUNNING COMPLETE NOTIFICATION TEST CYCLE =====');

      // Test 1: Start notification
      debugPrint('🔋 Step 1: Testing notification start...');
      final startResult = await testActiveChargingNotification();
      results['start_test'] = startResult;
      
      if (!startResult['success']) {
        results['overall_success'] = false;
        return results;
      }

      // Wait 3 seconds
      debugPrint('🔋 Waiting 3 seconds...');
      await Future.delayed(const Duration(seconds: 3));

      // Test 2: Update notification
      debugPrint('🔋 Step 2: Testing notification update...');
      final updateResult = await testNotificationUpdates();
      results['update_test'] = updateResult;

      // Wait 3 seconds
      debugPrint('🔋 Waiting 3 seconds...');
      await Future.delayed(const Duration(seconds: 3));

      // Test 3: Stop notification
      debugPrint('🔋 Step 3: Testing notification stop...');
      final stopResult = await testStopNotification();
      results['stop_test'] = stopResult;

      results['overall_success'] = startResult['success'] && 
                                  updateResult['success'] && 
                                  stopResult['success'];

      debugPrint('✅ Complete notification test cycle finished');
      debugPrint('🔋 Overall success: ${results['overall_success']}');

      return results;
    } catch (e) {
      debugPrint('❌ Error in complete notification test: $e');
      results['overall_success'] = false;
      results['error'] = e.toString();
      return results;
    }
  }

  /// Test notification tap navigation
  static Future<Map<String, dynamic>> testNotificationNavigation() async {
    if (!kDebugMode) {
      debugPrint('❌ Navigation testing only available in debug mode');
      return {'success': false, 'error': 'Debug mode only'};
    }

    try {
      debugPrint('🔋 ===== TESTING NOTIFICATION TAP NAVIGATION =====');
      debugPrint('🔋 This will show a notification that you can tap to test navigation');

      // Create test session for navigation testing
      final testSession = ChargingSession(
        id: 'NAV_TEST_${DateTime.now().millisecondsSinceEpoch}',
        stationUid: 'NAV_TEST_STATION',
        connectorId: 'NAV_CONNECTOR',
        startTime: DateTime.now(),
        currentCharge: 0.60, // 60%
        currentPower: 5.5, // 5.5 kW
        energyDelivered: 15.0, // 15.0 kWh
        cost: 225.00, // ₹225.00
        co2Saved: 8.1, // 8.1 kg CO2
      );

      // Start notification for navigation testing
      await _notificationManager.startChargingSession(testSession);

      debugPrint('✅ Navigation test notification created');
      debugPrint('🔋 Tap the notification in Android tray to test navigation');
      debugPrint('🔋 It should open the charging session screen');

      return {
        'success': true,
        'session_id': testSession.id,
        'navigation_test_ready': true,
        'message': 'Tap notification in Android tray to test navigation to charging session screen',
      };
    } catch (e) {
      debugPrint('❌ Error testing notification navigation: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Quick test for immediate verification
  static Future<bool> quickNotificationTest() async {
    if (!kDebugMode) return false;

    try {
      debugPrint('🔋 Running quick notification test...');

      // Initialize
      await _notificationManager.initialize();

      // Create simple test session
      final session = ChargingSession(
        id: 'QUICK_TEST',
        stationUid: 'QUICK_STATION',
        connectorId: 'QUICK_CONNECTOR',
        startTime: DateTime.now(),
        currentCharge: 0.50,
        currentPower: 7.0,
        energyDelivered: 10.0,
        cost: 150.0,
        co2Saved: 5.0,
      );

      // Start notification
      await _notificationManager.startChargingSession(session);

      debugPrint('✅ Quick notification test passed');
      debugPrint('🔋 Check Android notification tray');

      return true;
    } catch (e) {
      debugPrint('❌ Quick notification test failed: $e');
      return false;
    }
  }

  /// Get notification manager status
  static Map<String, dynamic> getNotificationStatus() {
    return {
      'manager_initialized': _notificationManager.isInitialized,
      'has_active_session': _notificationManager.hasActiveSession,
      'is_notification_active': _notificationManager.isNotificationActive,
      'current_session_id': _notificationManager.currentSession?.id,
    };
  }

  /// Clean up test notifications
  static Future<void> cleanupTestNotifications() async {
    try {
      debugPrint('🧹 Cleaning up test notifications...');
      await _notificationManager.stopChargingSession();
      debugPrint('✅ Test notifications cleaned up');
    } catch (e) {
      debugPrint('❌ Error cleaning up notifications: $e');
    }
  }
}
