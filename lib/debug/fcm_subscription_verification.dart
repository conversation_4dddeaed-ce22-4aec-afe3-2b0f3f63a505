import 'dart:async';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../services/fcm_charging_session_manager.dart';
import '../services/fcm_subscription_service.dart';

/// FCM Subscription Verification Tool
/// Tests and verifies FCM subscription functionality for charging sessions
class FCMSubscriptionVerification {
  static final FCMChargingSessionManager _sessionManager = FCMChargingSessionManager();
  static final FCMSubscriptionService _subscriptionService = FCMSubscriptionService();

  /// Run complete FCM subscription verification
  static Future<Map<String, dynamic>> runCompleteVerification() async {
    debugPrint('🔔 ===== STARTING FCM SUBSCRIPTION VERIFICATION =====');
    
    final results = <String, dynamic>{};
    
    try {
      // Test 1: Firebase Messaging Instance
      results['firebase_instance'] = await _testFirebaseInstance();
      
      // Test 2: FCM Token Generation
      results['fcm_token'] = await _testFCMToken();
      
      // Test 3: Topic Subscription/Unsubscription
      results['topic_subscription'] = await _testTopicSubscription();
      
      // Test 4: Charging Session Manager
      results['session_manager'] = await _testSessionManager();
      
      // Test 5: Subscription Service
      results['subscription_service'] = await _testSubscriptionService();
      
      debugPrint('✅ ===== FCM SUBSCRIPTION VERIFICATION COMPLETED =====');
      
      return {
        'overall_status': _calculateOverallStatus(results),
        'timestamp': DateTime.now().toIso8601String(),
        'tests': results,
      };
    } catch (e) {
      debugPrint('❌ Error during FCM verification: $e');
      return {
        'overall_status': 'failed',
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Test Firebase Messaging instance creation
  static Future<Map<String, dynamic>> _testFirebaseInstance() async {
    try {
      debugPrint('🔔 Testing Firebase Messaging instance...');
      
      final messaging = FirebaseMessaging.instance;
      final isSupported = await messaging.isSupported();
      
      return {
        'status': isSupported ? 'passed' : 'failed',
        // ignore: unnecessary_null_comparison
        'instance_created': messaging != null,
        'is_supported': isSupported,
        'details': 'Firebase Messaging instance creation and support check',
      };
    } catch (e) {
      debugPrint('❌ Firebase instance test failed: $e');
      return {
        'status': 'failed',
        'error': e.toString(),
        'instance_created': false,
        'is_supported': false,
      };
    }
  }

  /// Test FCM token generation
  static Future<Map<String, dynamic>> _testFCMToken() async {
    try {
      debugPrint('🔔 Testing FCM token generation...');
      
      final messaging = FirebaseMessaging.instance;
      final token = await messaging.getToken();
      
      return {
        'status': token != null ? 'passed' : 'failed',
        'token_generated': token != null,
        'token_length': token?.length ?? 0,
        'token_preview': token != null ? '${token.substring(0, 20)}...' : null,
        'details': 'FCM token generation and validation',
      };
    } catch (e) {
      debugPrint('❌ FCM token test failed: $e');
      return {
        'status': 'failed',
        'error': e.toString(),
        'token_generated': false,
      };
    }
  }

  /// Test topic subscription and unsubscription
  static Future<Map<String, dynamic>> _testTopicSubscription() async {
    try {
      debugPrint('🔔 Testing FCM topic subscription...');
      
      final messaging = FirebaseMessaging.instance;
      final testTopic = 'test_charging_${DateTime.now().millisecondsSinceEpoch}';
      
      // Test subscription
      await messaging.subscribeToTopic(testTopic).timeout(
        const Duration(seconds: 10),
        onTimeout: () => throw TimeoutException('Subscription timeout', const Duration(seconds: 10)),
      );
      
      debugPrint('✅ Topic subscription successful: $testTopic');
      
      // Wait a moment
      await Future.delayed(const Duration(seconds: 1));
      
      // Test unsubscription
      await messaging.unsubscribeFromTopic(testTopic).timeout(
        const Duration(seconds: 10),
        onTimeout: () => throw TimeoutException('Unsubscription timeout', const Duration(seconds: 10)),
      );
      
      debugPrint('✅ Topic unsubscription successful: $testTopic');
      
      return {
        'status': 'passed',
        'subscription_successful': true,
        'unsubscription_successful': true,
        'test_topic': testTopic,
        'details': 'FCM topic subscription and unsubscription test',
      };
    } on TimeoutException catch (e) {
      debugPrint('❌ Topic subscription timeout: $e');
      return {
        'status': 'failed',
        'error': 'Timeout: ${e.message}',
        'subscription_successful': false,
        'timeout_occurred': true,
      };
    } catch (e) {
      debugPrint('❌ Topic subscription test failed: $e');
      return {
        'status': 'failed',
        'error': e.toString(),
        'subscription_successful': false,
      };
    }
  }

  /// Test charging session manager
  static Future<Map<String, dynamic>> _testSessionManager() async {
    try {
      debugPrint('🔔 Testing FCM Charging Session Manager...');
      
      // Initialize session manager
      await _sessionManager.initialize();
      
      // Test session start
      final testSessionId = 'TEST_SESSION_${DateTime.now().millisecondsSinceEpoch}';
      final testTransactionId = 'TEST_TRANSACTION_${DateTime.now().millisecondsSinceEpoch}';
      
      final startResult = await _sessionManager.startChargingSession(
        sessionId: testSessionId,
        transactionId: testTransactionId,
      );
      
      if (!startResult) {
        return {
          'status': 'failed',
          'error': 'Failed to start charging session',
          'session_start': false,
        };
      }
      
      // Check session info
      final sessionInfo = _sessionManager.sessionInfo;
      
      // Wait a moment
      await Future.delayed(const Duration(seconds: 2));
      
      // Test session stop
      final stopResult = await _sessionManager.stopChargingSession();
      
      return {
        'status': startResult && stopResult ? 'passed' : 'failed',
        'session_start': startResult,
        'session_stop': stopResult,
        'session_info': sessionInfo,
        'test_session_id': testSessionId,
        'test_transaction_id': testTransactionId,
        'details': 'FCM charging session lifecycle test',
      };
    } catch (e) {
      debugPrint('❌ Session manager test failed: $e');
      return {
        'status': 'failed',
        'error': e.toString(),
        'session_start': false,
        'session_stop': false,
      };
    }
  }

  /// Test subscription service
  static Future<Map<String, dynamic>> _testSubscriptionService() async {
    try {
      debugPrint('🔔 Testing FCM Subscription Service...');
      
      // Initialize subscription service
      await _subscriptionService.initialize();
      
      // Run test subscription
      final testResult = await _subscriptionService.testSubscription();
      
      return {
        'status': testResult['overall_success'] == true ? 'passed' : 'failed',
        'test_result': testResult,
        'details': 'FCM subscription service functionality test',
      };
    } catch (e) {
      debugPrint('❌ Subscription service test failed: $e');
      return {
        'status': 'failed',
        'error': e.toString(),
      };
    }
  }

  /// Calculate overall verification status
  static String _calculateOverallStatus(Map<String, dynamic> results) {
    final testStatuses = results.values
        .whereType<Map<String, dynamic>>()
        .map((result) => result['status'] as String?)
        .where((status) => status != null)
        .toList();
    
    if (testStatuses.isEmpty) return 'unknown';
    if (testStatuses.every((status) => status == 'passed')) return 'passed';
    if (testStatuses.any((status) => status == 'passed')) return 'partial';
    return 'failed';
  }

  /// Show verification results dialog
  static void showVerificationDialog(BuildContext context, Map<String, dynamic> results) {
    final overallStatus = results['overall_status'] as String;
    final tests = results['tests'] as Map<String, dynamic>? ?? {};
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              overallStatus == 'passed' ? Icons.check_circle : 
              overallStatus == 'partial' ? Icons.warning : Icons.error,
              color: overallStatus == 'passed' ? Colors.green : 
                     overallStatus == 'partial' ? Colors.orange : Colors.red,
            ),
            const SizedBox(width: 8),
            const Text('FCM Subscription Verification'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Overall Status: ${overallStatus.toUpperCase()}',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: overallStatus == 'passed' ? Colors.green : 
                         overallStatus == 'partial' ? Colors.orange : Colors.red,
                ),
              ),
              const SizedBox(height: 16),
              ...tests.entries.map((entry) {
                final testName = entry.key;
                final testResult = entry.value as Map<String, dynamic>;
                final status = testResult['status'] as String;
                
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Row(
                    children: [
                      Icon(
                        status == 'passed' ? Icons.check : Icons.close,
                        color: status == 'passed' ? Colors.green : Colors.red,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          testName.replaceAll('_', ' ').toUpperCase(),
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                    ],
                  ),
                );
              }),
              const SizedBox(height: 16),
              const Text(
                'FCM subscription system is ready for charging notifications.',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

/// FCM Subscription Verification Widget
class FCMSubscriptionVerificationWidget extends StatefulWidget {
  const FCMSubscriptionVerificationWidget({super.key});

  @override
  State<FCMSubscriptionVerificationWidget> createState() => _FCMSubscriptionVerificationWidgetState();
}

class _FCMSubscriptionVerificationWidgetState extends State<FCMSubscriptionVerificationWidget> {
  bool _isRunning = false;
  Map<String, dynamic>? _lastResults;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('FCM Subscription Verification'),
        backgroundColor: const Color(0xFF4CAF50),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.cloud_sync, color: Colors.blue),
                        SizedBox(width: 8),
                        Text(
                          'FCM Subscription Verification',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    Text(
                      'This tool verifies that FCM subscription functionality is working correctly for charging notifications.',
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _isRunning ? null : _runVerification,
              icon: _isRunning 
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.play_arrow),
              label: Text(_isRunning ? 'Running Verification...' : 'Run FCM Verification'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4CAF50),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
            ),
            if (_lastResults != null) ...[
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Last Verification Results',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text('Status: ${_lastResults!['overall_status']}'),
                      Text('Time: ${_lastResults!['timestamp']}'),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: () {
                          FCMSubscriptionVerification.showVerificationDialog(context, _lastResults!);
                        },
                        child: const Text('View Details'),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _runVerification() async {
    setState(() {
      _isRunning = true;
    });

    try {
      final results = await FCMSubscriptionVerification.runCompleteVerification();
      setState(() {
        _lastResults = results;
      });

      if (mounted) {
        FCMSubscriptionVerification.showVerificationDialog(context, results);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Verification failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }
}
