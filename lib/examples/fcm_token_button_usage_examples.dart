import 'package:flutter/material.dart';
import 'package:ecoplug/widgets/fcm_token_button_widget.dart';

/// FCM Token Button Usage Examples
/// Shows different ways to integrate the FCM token button in your app
class FCMTokenButtonUsageExamples extends StatelessWidget {
  const FCMTokenButtonUsageExamples({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('FCM Token Button Examples'),
        backgroundColor: theme.colorScheme.surface,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Icon(
                      Icons.token,
                      size: 48,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'FCM Token Button Examples',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Different ways to integrate FCM token generation in your app',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Example 1: Basic Button
            _buildExampleSection(
              'Basic Button',
              'Simple button for quick token generation',
              const FCMTokenButtonWidget(),
              '''
FCMTokenButtonWidget()
''',
              theme,
            ),

            // Example 2: Custom Styled Button
            _buildExampleSection(
              'Custom Styled Button',
              'Button with custom label, icon, and styling',
              FCMTokenButtonWidget(
                label: 'Get FCM Token for Testing',
                icon: Icons.key,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
              ),
              '''
FCMTokenButtonWidget(
  label: 'Get FCM Token for Testing',
  icon: Icons.key,
  style: ElevatedButton.styleFrom(
    backgroundColor: Colors.orange,
    foregroundColor: Colors.white,
  ),
)
''',
              theme,
            ),

            // Example 3: Card Version
            _buildExampleSection(
              'Card Version',
              'Full card with description and usage info',
              const FCMTokenButtonWidget(
                showAsCard: true,
                showUsageInfo: true,
              ),
              '''
FCMTokenButtonWidget(
  showAsCard: true,
  showUsageInfo: true,
)
''',
              theme,
            ),

            // Example 4: Compact Card
            _buildExampleSection(
              'Compact Card',
              'Card version without usage info button',
              const FCMTokenButtonWidget(
                showAsCard: true,
                showUsageInfo: false,
              ),
              '''
FCMTokenButtonWidget(
  showAsCard: true,
  showUsageInfo: false,
)
''',
              theme,
            ),

            // Example 5: In Settings Section
            _buildExampleSection(
              'In Settings Section',
              'How it looks in a settings or debug section',
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Developer Tools',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Tools for testing and debugging notifications',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(height: 16),
                      const FCMTokenButtonWidget(
                        label: 'Generate FCM Token',
                        icon: Icons.developer_mode,
                      ),
                    ],
                  ),
                ),
              ),
              '''
Card(
  child: Padding(
    padding: const EdgeInsets.all(16),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Developer Tools'),
        Text('Tools for testing and debugging notifications'),
        SizedBox(height: 16),
        FCMTokenButtonWidget(
          label: 'Generate FCM Token',
          icon: Icons.developer_mode,
        ),
      ],
    ),
  ),
)
''',
              theme,
            ),

            // Example 6: In App Bar Actions
            _buildExampleSection(
              'App Bar Integration',
              'How to add as an action in app bar',
              AppBar(
                title: const Text('Notification Settings'),
                backgroundColor: theme.colorScheme.surfaceContainerHighest,
                actions: [
                  Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FCMTokenButtonWidget(
                      label: 'FCM',
                      icon: Icons.token,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.colorScheme.primary,
                        foregroundColor: theme.colorScheme.onPrimary,
                        minimumSize: const Size(80, 36),
                      ),
                    ),
                  ),
                ],
              ),
              '''
AppBar(
  title: Text('Notification Settings'),
  actions: [
    Padding(
      padding: EdgeInsets.only(right: 8),
      child: FCMTokenButtonWidget(
        label: 'FCM',
        icon: Icons.token,
        style: ElevatedButton.styleFrom(
          minimumSize: Size(80, 36),
        ),
      ),
    ),
  ],
)
''',
              theme,
            ),

            // Integration Instructions
            const SizedBox(height: 20),
            Card(
              color: theme.colorScheme.primaryContainer,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.integration_instructions,
                          color: theme.colorScheme.onPrimaryContainer,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Integration Instructions',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onPrimaryContainer,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      '1. Import the widget:\n'
                      '   import \'package:ecoplug/widgets/fcm_token_button_widget.dart\';\n\n'
                      '2. Add to your screen:\n'
                      '   FCMTokenButtonWidget()\n\n'
                      '3. Customize as needed:\n'
                      '   • Change label and icon\n'
                      '   • Apply custom styling\n'
                      '   • Use card version for more context\n\n'
                      '4. The button will:\n'
                      '   • Generate FCM tokens on tap\n'
                      '   • Show tokens in a bottom sheet\n'
                      '   • Provide copy-to-clipboard functionality\n'
                      '   • Display usage instructions',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onPrimaryContainer,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExampleSection(
    String title,
    String description,
    Widget example,
    String code,
    ThemeData theme,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          description,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 12),
        
        // Example widget
        example,
        const SizedBox(height: 12),
        
        // Code snippet
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.code,
                    size: 16,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Code:',
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              SelectableText(
                code.trim(),
                style: theme.textTheme.bodySmall?.copyWith(
                  fontFamily: 'monospace',
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }
}
