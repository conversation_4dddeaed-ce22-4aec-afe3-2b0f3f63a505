import 'package:flutter/material.dart';
import '../utils/gst_formatter.dart';

/// Example showing how to integrate GST formatter with profile page and API data
class ProfileWithGSTExample extends StatefulWidget {
  const ProfileWithGSTExample({super.key});

  @override
  State<ProfileWithGSTExample> createState() => _ProfileWithGSTExampleState();
}

class _ProfileWithGSTExampleState extends State<ProfileWithGSTExample> {
  Map<String, dynamic>? _profileData;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadProfileData();
  }

  // Simulate API call to get profile data
  Future<void> _loadProfileData() async {
    setState(() {
      _isLoading = true;
    });

    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));

    // Sample API response with GST data
    final apiResponse = {
      "success": true,
      "message": "Profile retrieved successfully",
      "data": {
        "id": 123,
        "uid": "USER_123",
        "name": "<PERSON><PERSON>",
        "email": "<EMAIL>",
        "mobile_number": "+91 9876543210",
        "address": "123 Business Park, Sector 18",
        "city": "Gurgaon",
        "state": "Haryana",
        "pincode": "122001",
        "gst_no": "06AAAAA0000A1Z5", // Haryana GST number
        "business_name": "Kumar Enterprises Pvt Ltd",
        "company_uid": "COMP_123",
        "status": 1,
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-12-15T14:20:00Z"
      }
    };

    setState(() {
      _profileData = apiResponse['data'] as Map<String, dynamic>?;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile with GST'),
        backgroundColor: const Color(0xFF22C55E),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadProfileData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _profileData == null
              ? const Center(child: Text('Failed to load profile'))
              : _buildProfileContent(),
    );
  }

  Widget _buildProfileContent() {
    // Parse GST data from API response
    final gstData = GSTFormatter.parseGSTFromAPIData(_profileData!);
    final gstDisplayData = GSTFormatter.createGSTDisplayData(
      gstData['gst_number'],
      gstData['business_name'],
    );

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile Header
          _buildProfileHeader(),
          
          const SizedBox(height: 24),
          
          // Personal Information
          _buildPersonalInfoCard(),
          
          const SizedBox(height: 16),
          
          // Business Information (GST)
          _buildBusinessInfoCard(gstDisplayData),
          
          const SizedBox(height: 16),
          
          // Address Information
          _buildAddressCard(),
          
          const SizedBox(height: 24),
          
          // Action Buttons
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF22C55E), Color(0xFF16A34A)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          CircleAvatar(
            radius: 40,
            backgroundColor: Colors.white.withValues(alpha: 0.2),
            child: Text(
              _getInitials(_profileData!['name']),
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(height: 12),
          Text(
            _profileData!['name'] ?? 'Unknown User',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          Text(
            _profileData!['email'] ?? '',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.person, color: Color(0xFF22C55E)),
                SizedBox(width: 8),
                Text(
                  'Personal Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Full Name', _profileData!['name']),
            _buildInfoRow('Email', _profileData!['email']),
            _buildInfoRow('Mobile', _profileData!['mobile_number']),
            _buildInfoRow('User ID', _profileData!['uid']),
          ],
        ),
      ),
    );
  }

  Widget _buildBusinessInfoCard(Map<String, dynamic> gstDisplayData) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.business, color: Color(0xFF22C55E)),
                SizedBox(width: 8),
                Text(
                  'Business Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            if (gstDisplayData['has_gst']) ...[
              // Valid GST Information
              _buildInfoRow('Business Name', gstDisplayData['business_name']),
              _buildInfoRow('GST Number', gstDisplayData['formatted_gst']),
              
              // GST Status
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: gstDisplayData['is_valid'] 
                      ? Colors.green.shade100 
                      : Colors.red.shade100,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: gstDisplayData['is_valid'] 
                        ? Colors.green.shade300 
                        : Colors.red.shade300,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      gstDisplayData['is_valid'] 
                          ? Icons.verified 
                          : Icons.error,
                      size: 16,
                      color: gstDisplayData['is_valid'] 
                          ? Colors.green.shade700 
                          : Colors.red.shade700,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      gstDisplayData['is_valid'] 
                          ? 'GST Verified' 
                          : 'GST Invalid',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: gstDisplayData['is_valid'] 
                            ? Colors.green.shade700 
                            : Colors.red.shade700,
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 12),
              
              // Additional GST Details
              _buildInfoRow('State Code', gstDisplayData['state_code']),
              _buildInfoRow('PAN', gstDisplayData['pan']),
              
              if (gstDisplayData['validation_error'] != null)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(top: 8),
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red.shade200),
                  ),
                  child: Text(
                    '⚠️ ${gstDisplayData['validation_error']}',
                    style: TextStyle(
                      color: Colors.red.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
            ] else ...[
              // No GST Registration
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.business_center_outlined,
                      size: 48,
                      color: Colors.grey.shade500,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'No GST Registration',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey.shade700,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Add your GST details for business transactions',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    ElevatedButton.icon(
                      onPressed: () {
                        // Navigate to GST registration page
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('GST registration page would open here'),
                          ),
                        );
                      },
                      icon: const Icon(Icons.add),
                      label: const Text('Add GST Details'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF22C55E),
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAddressCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.location_on, color: Color(0xFF22C55E)),
                SizedBox(width: 8),
                Text(
                  'Address Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Address', _profileData!['address']),
            _buildInfoRow('City', _profileData!['city']),
            _buildInfoRow('State', _profileData!['state']),
            _buildInfoRow('PIN Code', _profileData!['pincode']),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Edit profile functionality')),
              );
            },
            icon: const Icon(Icons.edit),
            label: const Text('Edit Profile'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF22C55E),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Download GST certificate')),
              );
            },
            icon: const Icon(Icons.download),
            label: const Text('Download GST'),
            style: OutlinedButton.styleFrom(
              foregroundColor: const Color(0xFF22C55E),
              side: const BorderSide(color: Color(0xFF22C55E)),
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String? value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey.shade600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value ?? 'Not provided',
              style: const TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getInitials(String name) {
    final parts = name.split(' ');
    if (parts.length >= 2) {
      return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
    } else if (parts.isNotEmpty) {
      return parts[0][0].toUpperCase();
    }
    return 'U';
  }
}
