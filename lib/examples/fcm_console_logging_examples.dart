import 'package:flutter/material.dart';
import 'package:ecoplug/debug/fcm_token_console_logger.dart';

/// FCM Console Logging Examples
/// Shows how to use FCM token console logging in your app
/// All output goes to debug console only
class FCMConsoleLoggingExamples {
  
  /// Example 1: Log current FCM token during app initialization
  static Future<void> logTokenOnAppStart() async {
    // Call this in main.dart or app initialization
    await FCMTokenConsoleLogger.logCurrentToken();
  }

  /// Example 2: Generate and log new FCM token
  static Future<void> generateNewToken() async {
    // Generate a fresh FCM token and log it
    await FCMTokenConsoleLogger.generateAndLogToken();
  }

  /// Example 3: Log all available FCM tokens
  static Future<void> logAllAvailableTokens() async {
    // Comprehensive logging of all FCM tokens
    await FCMTokenConsoleLogger.logAllTokens();
  }

  /// Example 4: Quick token log (minimal output)
  static Future<void> quickTokenCheck() async {
    // Quick one-line token log
    await FCMTokenConsoleLogger.quickLog();
  }

  /// Example 5: Log FCM service status
  static Future<void> checkFCMStatus() async {
    // Check and log FCM service configuration
    await FCMTokenConsoleLogger.logFCMStatus();
  }

  /// Example 6: Log token with timestamp
  static Future<void> logTokenWithTime() async {
    // Log token with current timestamp
    await FCMTokenConsoleLogger.logTokenWithTimestamp();
  }

  /// Example 7: Use extension method
  static Future<void> useExtensionMethod() async {
    // Use extension method from any class
    final example = FCMConsoleLoggingExamples();
    await example.logFCMToken();
    await example.quickFCMLog();
  }
}

/// Example Screen showing how to integrate FCM console logging
class FCMConsoleLoggingScreen extends StatefulWidget {
  const FCMConsoleLoggingScreen({super.key});

  @override
  State<FCMConsoleLoggingScreen> createState() => _FCMConsoleLoggingScreenState();
}

class _FCMConsoleLoggingScreenState extends State<FCMConsoleLoggingScreen> {

  @override
  void initState() {
    super.initState();
    // Example: Log FCM token when screen loads
    _logTokenOnScreenLoad();
  }

  Future<void> _logTokenOnScreenLoad() async {
    // This will only log in debug mode
    await FCMTokenConsoleLogger.logCurrentToken();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('FCM Console Logging'),
        backgroundColor: theme.colorScheme.surface,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Icon(
                      Icons.terminal,
                      size: 48,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'FCM Console Logging',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'FCM tokens are logged to debug console only',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Logging buttons (these trigger console output)
            ElevatedButton.icon(
              onPressed: () async {
                await FCMTokenConsoleLogger.logCurrentToken();
                _showSnackBar('Current FCM token logged to console');
              },
              icon: const Icon(Icons.token),
              label: const Text('Log Current Token'),
            ),
            const SizedBox(height: 8),

            ElevatedButton.icon(
              onPressed: () async {
                await FCMTokenConsoleLogger.generateAndLogToken();
                _showSnackBar('New FCM token generated and logged to console');
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Generate & Log New Token'),
            ),
            const SizedBox(height: 8),

            ElevatedButton.icon(
              onPressed: () async {
                await FCMTokenConsoleLogger.logAllTokens();
                _showSnackBar('All FCM tokens logged to console');
              },
              icon: const Icon(Icons.list),
              label: const Text('Log All Tokens'),
            ),
            const SizedBox(height: 8),

            ElevatedButton.icon(
              onPressed: () async {
                await FCMTokenConsoleLogger.quickLog();
                _showSnackBar('Quick FCM token logged to console');
              },
              icon: const Icon(Icons.speed),
              label: const Text('Quick Log'),
            ),
            const SizedBox(height: 8),

            ElevatedButton.icon(
              onPressed: () async {
                await FCMTokenConsoleLogger.logFCMStatus();
                _showSnackBar('FCM status logged to console');
              },
              icon: const Icon(Icons.info),
              label: const Text('Log FCM Status'),
            ),
            const SizedBox(height: 20),

            // Info card
            Card(
              color: theme.colorScheme.primaryContainer,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info,
                          color: theme.colorScheme.onPrimaryContainer,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Console Output Only',
                          style: theme.textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onPrimaryContainer,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '• All FCM tokens are logged to debug console only\n'
                      '• No tokens are displayed in the UI\n'
                      '• Only works in debug mode (kDebugMode)\n'
                      '• Check your IDE debug console for output\n'
                      '• Look for lines starting with "🔥"',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}

/// Example of integrating FCM console logging in your authentication flow
class AuthenticationWithFCMLogging {
  
  /// Example: Log FCM token after successful login
  static Future<void> onLoginSuccess(String userId) async {
    // Your existing login logic here...
    
    // Log FCM token to console for debugging
    await FCMTokenConsoleLogger.logTokenWithTimestamp();
    
    // Continue with your login flow...
  }

  /// Example: Log FCM token during app initialization
  static Future<void> onAppInitialization() async {
    // Your existing app initialization...
    
    // Log FCM status and token
    await FCMTokenConsoleLogger.logFCMStatus();
    await FCMTokenConsoleLogger.logCurrentToken();
    
    // Continue with app initialization...
  }

  /// Example: Log token refresh events
  static void onTokenRefresh(String newToken) {
    // Log the token refresh event
    FCMTokenConsoleLogger.logTokenRefresh(newToken);
    
    // Your existing token refresh handling...
  }
}
