import 'package:flutter/material.dart';
import 'package:ecoplug/widgets/firebase_config_test_widget.dart';
import 'package:ecoplug/widgets/welcome_notification_test_widget.dart';
import 'package:ecoplug/widgets/notification_test_widget.dart';
import 'package:ecoplug/widgets/firebase_icon_widget.dart';
import 'package:ecoplug/widgets/fcm_token_test_widget.dart';

/// Firebase Integration Test Screen
/// Comprehensive testing interface for Firebase and notification features
class FirebaseIntegrationTestScreen extends StatelessWidget {
  const FirebaseIntegrationTestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Firebase Integration Test'),
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    FirebaseIconWidget(
                      size: 48,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Firebase Integration Test',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Test Firebase Cloud Messaging and notification features',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Configuration Status
            Text(
              'Configuration Status',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            _buildConfigurationStatus(theme),
            const SizedBox(height: 20),

            // Firebase Configuration Test
            Text(
              'Firebase Configuration Test',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const FirebaseConfigTestWidget(),
            const SizedBox(height: 20),

            // FCM Token Generation Test
            Text(
              'FCM Token Generation Test',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const FCMTokenTestWidget(),
            const SizedBox(height: 20),

            // Welcome Notification Test
            Text(
              'Welcome Notification Test',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const WelcomeNotificationTestWidget(),
            const SizedBox(height: 20),

            // General Notification Test
            Text(
              'General Notification Test',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const NotificationTestWidget(),
            const SizedBox(height: 20),

            // Instructions Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Testing Instructions',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildInstructionItem(
                      '1. Firebase Configuration',
                      'Run the Firebase configuration test to verify your setup',
                      Icons.verified,
                      theme,
                    ),
                    _buildInstructionItem(
                      '2. FCM Token Generation',
                      'Test FCM token generation and copy token for backend testing',
                      Icons.token,
                      theme,
                    ),
                    _buildInstructionItem(
                      '3. Welcome Notifications',
                      'Test welcome notifications for login scenarios',
                      Icons.waving_hand,
                      theme,
                    ),
                    _buildInstructionItem(
                      '4. General Notifications',
                      'Test charging and other notification types',
                      Icons.notifications,
                      theme,
                    ),
                    _buildInstructionItem(
                      '5. Backend Testing',
                      'Share FCM token with backend team for end-to-end testing',
                      Icons.cloud,
                      theme,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigurationStatus(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Current Configuration',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildStatusItem('Project ID', 'ecoplug-9ab21', true, theme),
            _buildStatusItem('Project Number', '386706794878', true, theme),
            _buildStatusItem('Package Name', 'com.eeil.ecoplug', true, theme),
            _buildStatusItem('Android App ID', '1:386706794878:android:7e19a2e8f607c0584eab2f', true, theme),
            _buildStatusItem('API Key', 'AIzaSyAdQ49tgN61V6taM4PJHYOD2pdmKkwMjW4', true, theme),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green, size: 16),
                  const SizedBox(width: 8),
                  Text(
                    'Android configuration complete ✅',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.green,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.warning, color: Colors.orange, size: 16),
                  const SizedBox(width: 8),
                  Text(
                    'iOS configuration needed from backend team ⚠️',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.orange,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(String label, String value, bool isConfigured, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            isConfigured ? Icons.check_circle : Icons.error,
            size: 16,
            color: isConfigured ? Colors.green : Colors.red,
          ),
          const SizedBox(width: 8),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            flex: 3,
            child: Text(
              value.length > 30 ? '${value.substring(0, 30)}...' : value,
              style: theme.textTheme.bodySmall?.copyWith(
                fontFamily: 'monospace',
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInstructionItem(String title, String description, IconData icon, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 20,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
