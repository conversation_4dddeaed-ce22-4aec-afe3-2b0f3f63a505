import 'package:flutter/material.dart';
import '../utils/connectivity_error_utils.dart';

/// Example widget demonstrating how to use the connectivity error system
class ConnectivityErrorExample extends StatefulWidget {
  const ConnectivityErrorExample({super.key});

  @override
  State<ConnectivityErrorExample> createState() => _ConnectivityErrorExampleState();
}

class _ConnectivityErrorExampleState extends State<ConnectivityErrorExample>
    with ConnectivityErrorMixin {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Connectivity Error Examples'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Connectivity Error System Examples',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),

            // Example 1: Show error page directly
            ElevatedButton(
              onPressed: () {
                context.showConnectivityError(
                  message: 'This is a test connectivity error message.',
                  onRetry: () {
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Retry button pressed!')),
                    );
                  },
                );
              },
              child: const Text('Show Connectivity Error Page'),
            ),

            const SizedBox(height: 12),

            // Example 2: Show error as bottom sheet
            ElevatedButton(
              onPressed: () {
                context.showConnectivityErrorSheet(
                  message: 'This is a test connectivity error in a bottom sheet.',
                  onRetry: () {
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Retry from sheet pressed!')),
                    );
                  },
                );
              },
              child: const Text('Show Connectivity Error Sheet'),
            ),

            const SizedBox(height: 12),

            // Example 3: Navigate using named route
            ElevatedButton(
              onPressed: () {
                context.pushConnectivityError(
                  errorMessage: 'This error page was opened using named route navigation.',
                );
              },
              child: const Text('Navigate to Error Page (Named Route)'),
            ),

            const SizedBox(height: 12),

            // Example 4: Simulate API call with error handling
            ElevatedButton(
              onPressed: _isLoading ? null : _simulateApiCall,
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('Simulate API Call with Error Handling'),
            ),

            const SizedBox(height: 12),

            // Example 5: Check connectivity before operation
            ElevatedButton(
              onPressed: () async {
                final result = await context.withConnectivityCheck(() async {
                  // Simulate some operation
                  await Future.delayed(const Duration(seconds: 1));
                  return 'Operation completed successfully!';
                });

                if (result != null && mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text(result)),
                  );
                }
              },
              child: const Text('Check Connectivity Before Operation'),
            ),

            const SizedBox(height: 24),

            const Text(
              'Error Scenarios:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),

            // Example 6: Different error scenarios
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildErrorScenarioChip('Timeout', () {
                  _showErrorScenario('Connection timeout occurred');
                }),
                _buildErrorScenarioChip('DNS Error', () {
                  _showErrorScenario('DNS resolution failed');
                }),
                _buildErrorScenarioChip('Server Unreachable', () {
                  _showErrorScenario('Server is unreachable');
                }),
                _buildErrorScenarioChip('Offline', () {
                  _showErrorScenario('Device is offline');
                }),
              ],
            ),

            const SizedBox(height: 24),

            const Text(
              'Integration Examples:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),

            // Example 7: Using mixin methods
            ElevatedButton(
              onPressed: () {
                executeWithConnectivityHandling(() async {
                  // Simulate network operation that might fail
                  await Future.delayed(const Duration(milliseconds: 500));
                  throw Exception('SocketException: Network unreachable');
                }, retryCallback: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Retry from mixin!')),
                  );
                });
              },
              child: const Text('Test Mixin Error Handling'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorScenarioChip(String label, VoidCallback onTap) {
    return ActionChip(
      label: Text(label),
      onPressed: onTap,
      backgroundColor: Colors.red.shade50,
      labelStyle: TextStyle(color: Colors.red.shade700),
    );
  }

  void _showErrorScenario(String errorMessage) {
    context.showConnectivityErrorSheet(
      message: errorMessage,
      onRetry: () {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Retry for: $errorMessage')),
        );
      },
    );
  }

  Future<void> _simulateApiCall() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await context.handleApiCall(() async {
        // Simulate API delay
        await Future.delayed(const Duration(seconds: 1));
        
        // Simulate network error
        throw Exception('SocketException: Failed to connect to server');
      }, onRetry: () {
        Navigator.of(context).pop();
        _simulateApiCall(); // Retry the API call
      });
    } catch (e) {
      // Error was handled by the connectivity error system
      debugPrint('API call failed: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
