import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ecoplug/services/auth_notification_service.dart';
import 'package:ecoplug/providers/notification_provider.dart';
import 'package:ecoplug/widgets/welcome_notification_test_widget.dart';

/// Example integration of welcome notifications with login flow
/// This demonstrates how to integrate the welcome notification system
/// with your existing authentication flow
class LoginWithWelcomeNotificationExample extends ConsumerStatefulWidget {
  const LoginWithWelcomeNotificationExample({super.key});

  @override
  ConsumerState<LoginWithWelcomeNotificationExample> createState() => 
      _LoginWithWelcomeNotificationExampleState();
}

class _LoginWithWelcomeNotificationExampleState 
    extends ConsumerState<LoginWithWelcomeNotificationExample> {
  
  final AuthNotificationService _authService = AuthNotificationService();
  final TextEditingController _userNameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  
  bool _isLoading = false;
  bool _isFirstLogin = false;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    try {
      await _authService.initialize();
      debugPrint('✅ Auth notification service initialized');
    } catch (e) {
      debugPrint('❌ Error initializing auth service: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Welcome Notification Example'),
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Icon(
                      Icons.login,
                      size: 48,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Login Example',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Demonstrates welcome notification integration',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Login Form
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Simulate Login',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // User Name Field
                    TextField(
                      controller: _userNameController,
                      decoration: const InputDecoration(
                        labelText: 'User Name',
                        hintText: 'Enter your name',
                        prefixIcon: Icon(Icons.person),
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // Email Field
                    TextField(
                      controller: _emailController,
                      decoration: const InputDecoration(
                        labelText: 'Email',
                        hintText: 'Enter your email',
                        prefixIcon: Icon(Icons.email),
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // First Login Checkbox
                    CheckboxListTile(
                      title: const Text('Simulate First Login'),
                      subtitle: const Text('Check this to simulate a new user'),
                      value: _isFirstLogin,
                      onChanged: (value) {
                        setState(() {
                          _isFirstLogin = value ?? false;
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    
                    // Login Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _isLoading ? null : _simulateLogin,
                        icon: _isLoading 
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              )
                            : const Icon(Icons.login),
                        label: Text(_isLoading ? 'Logging in...' : 'Login'),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Quick Actions
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Quick Actions',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        OutlinedButton.icon(
                          onPressed: _testWelcomeNotification,
                          icon: const Icon(Icons.notifications),
                          label: const Text('Test Welcome'),
                        ),
                        OutlinedButton.icon(
                          onPressed: _clearNotifications,
                          icon: const Icon(Icons.clear),
                          label: const Text('Clear'),
                        ),
                        OutlinedButton.icon(
                          onPressed: _resetTracking,
                          icon: const Icon(Icons.restore),
                          label: const Text('Reset'),
                        ),
                        OutlinedButton.icon(
                          onPressed: _showStats,
                          icon: const Icon(Icons.analytics),
                          label: const Text('Stats'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Welcome Notification Test Widget
            const WelcomeNotificationTestWidget(),
          ],
        ),
      ),
    );
  }

  /// Simulate login process with welcome notification
  Future<void> _simulateLogin() async {
    if (_userNameController.text.isEmpty) {
      _showSnackBar('Please enter a user name', Colors.orange);
      return;
    }

    setState(() => _isLoading = true);

    try {
      // Simulate login delay
      await Future.delayed(const Duration(seconds: 1));

      // Generate user ID
      final userId = 'user_${DateTime.now().millisecondsSinceEpoch}';
      final userName = _userNameController.text.trim();
      final userEmail = _emailController.text.trim().isNotEmpty 
          ? _emailController.text.trim() 
          : null;

      debugPrint('🔐 ===== SIMULATING LOGIN =====');
      debugPrint('🔐 User ID: $userId');
      debugPrint('🔐 User Name: $userName');
      debugPrint('🔐 User Email: $userEmail');
      debugPrint('🔐 Is First Login: $_isFirstLogin');

      // This is where you would normally call your authentication API
      // For this example, we'll just simulate success

      // Trigger welcome notification through auth service
      await _authService.onLoginSuccess(
        userId: userId,
        userName: userName,
        userEmail: userEmail,
      );

      _showSnackBar(
        'Login successful! Welcome notification sent 😊⚡',
        Colors.green,
      );

      // Clear form
      _userNameController.clear();
      _emailController.clear();
      setState(() => _isFirstLogin = false);

    } catch (e) {
      debugPrint('❌ Login simulation error: $e');
      _showSnackBar('Login failed: $e', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// Test welcome notification directly
  Future<void> _testWelcomeNotification() async {
    try {
      final notificationNotifier = ref.read(notificationProvider.notifier);
      
      await notificationNotifier.showWelcomeNotification(
        userName: _userNameController.text.isNotEmpty 
            ? _userNameController.text 
            : 'Test User',
        isFirstLogin: _isFirstLogin,
      );
      
      _showSnackBar('Test welcome notification sent!', Colors.blue);
    } catch (e) {
      _showSnackBar('Error: $e', Colors.red);
    }
  }

  /// Clear all notifications
  Future<void> _clearNotifications() async {
    try {
      final notificationNotifier = ref.read(notificationProvider.notifier);
      await notificationNotifier.clearWelcomeNotification();
      
      _showSnackBar('Notifications cleared!', Colors.orange);
    } catch (e) {
      _showSnackBar('Error: $e', Colors.red);
    }
  }

  /// Reset tracking data
  Future<void> _resetTracking() async {
    try {
      await _authService.resetLoginTracking('test_user');
      
      _showSnackBar('Tracking data reset!', Colors.purple);
    } catch (e) {
      _showSnackBar('Error: $e', Colors.red);
    }
  }

  /// Show statistics dialog
  Future<void> _showStats() async {
    try {
      final stats = await _authService.getAuthNotificationStatus();
      
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Notification Statistics'),
            content: SingleChildScrollView(
              child: Text(
                stats.entries
                    .map((e) => '${e.key}: ${e.value}')
                    .join('\n'),
                style: const TextStyle(fontFamily: 'monospace'),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      _showSnackBar('Error: $e', Colors.red);
    }
  }

  /// Show snackbar message
  void _showSnackBar(String message, Color backgroundColor) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  @override
  void dispose() {
    _userNameController.dispose();
    _emailController.dispose();
    super.dispose();
  }
}
