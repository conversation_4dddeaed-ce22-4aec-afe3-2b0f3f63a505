import '../models/station/station_marker_response.dart';
/// Utility functions for working with map markers
class MapMarkerUtils {
  /// Convert StationMarkerData to a format compatible with the map
  static Map<String, dynamic> convertMarkerToMapFormat(
      StationMarkerData marker) {
    // Use the status from the marker or determine based on the marker URL
    String status = marker.status;
    if (status.isEmpty && marker.mapPinUrl != null) {
      if (marker.mapPinUrl!.contains('unavailable')) {
        status = 'Unavailable';
      } else if (marker.mapPinUrl!.contains('charging')) {
        status = 'In Use';
      } else {
        status = 'Available';
      }
    }

    // Ensure the map pin URLs are properly set
    String? mapPinUrl = marker.mapPinUrl;
    String? focusedMapPinUrl = marker.focusedMapPinUrl;

    // If the URLs are null or empty, use default values based on status
    if (mapPinUrl == null || mapPinUrl.isEmpty) {
      final urls = getMarkerIconUrls(status, focused: false);
      mapPinUrl = urls['remote'];
    }

    if (focusedMapPinUrl == null || focusedMapPinUrl.isEmpty) {
      final urls = getMarkerIconUrls(status, focused: true);
      focusedMapPinUrl = urls['remote'];
    }

    return {
      'id': marker.id,
      'name': marker.name,
      'latitude': marker.latitude,
      'longitude': marker.longitude,
      'status': status,
      'mapPinUrl': mapPinUrl,
      'focusedMapPinUrl': focusedMapPinUrl,
      'address': marker.address, // Real address from API - null if not provided
      'connectorType': marker
          .connectorType, // Real connector type from API - null if not provided
    };
  }

  /// Convert a list of StationMarkerData to a format compatible with the map
  static List<Map<String, dynamic>> convertMarkersToMapFormat(
      List<StationMarkerData> markers) {
    return markers.map((marker) => convertMarkerToMapFormat(marker)).toList();
  }

  /// Determine the appropriate marker icon URL based on status
  /// Returns both remote and local fallback URLs
  static Map<String, String> getMarkerIconUrls(String status,
      {bool focused = false}) {
    String remoteUrl;

    if (status == 'Unavailable') {
      remoteUrl = 'https://api2.eeil.online/icons/unavailable.png';
    } else if (status == 'In Use') {
      remoteUrl = focused
          ? 'https://api2.eeil.online/mapicons/ecoplug_charging_focus_new.png'
          : 'https://api2.eeil.online/mapicons/ecoplug_charging_default_new.png';
    } else {
      remoteUrl = focused
          ? 'https://api2.eeil.online/mapicons/ecoplug_focus.png'
          : 'https://api2.eeil.online/mapicons/ecoplug_default.png';
    }

    return {
      'remote': remoteUrl,
      'local': remoteUrl,
    };
  }

  /// Get marker icon URL with fallback
  static String getMarkerIconUrl(String status, {bool focused = false}) {
    final urls = getMarkerIconUrls(status, focused: focused);
    return urls['remote']!;
  }

  /// Check if a URL is valid and accessible
  static bool isValidUrl(String? url) {
    if (url == null || url.isEmpty) {
      return false;
    }

    // Basic URL validation
    return url.startsWith('http://') || url.startsWith('https://');
  }

  /// Future clustering functionality will be implemented here
  /// This space is reserved for clustering utilities when needed
}
