import 'package:flutter/material.dart';
import '../screens/firebase_messaging_test_page.dart';

/// Helper utility to easily navigate to FCM test page
/// Provides quick access to Firebase messaging testing functionality
class FCMTestHelper {
  /// Navigate to Firebase Messaging Test Page
  static void navigateToTestPage(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const FirebaseMessagingTestPage(),
      ),
    );
  }

  /// Show FCM test dialog with quick access button
  static void showTestDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('🔔 FCM Testing'),
          content: const Text(
            'Access Firebase Cloud Messaging test tools to verify FCM functionality, '
            'test charging notifications, and debug subscription status.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                navigateToTestPage(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF00BCD4),
                foregroundColor: Colors.white,
              ),
              child: const Text('Open Test Page'),
            ),
          ],
        );
      },
    );
  }

  /// Create a floating action button for FCM testing (debug mode only)
  static Widget? createTestFAB(BuildContext context) {
    // Only show in debug mode
    bool isDebugMode = false;
    assert(isDebugMode = true); // This will only execute in debug mode
    
    if (!isDebugMode) return null;

    return FloatingActionButton(
      onPressed: () => showTestDialog(context),
      backgroundColor: const Color(0xFF00BCD4),
      tooltip: 'FCM Test Tools',
      child: const Icon(Icons.message, color: Colors.white),
    );
  }

  /// Create a debug menu item for FCM testing
  static PopupMenuItem<String> createDebugMenuItem() {
    return const PopupMenuItem<String>(
      value: 'fcm_test',
      child: Row(
        children: [
          Icon(Icons.message, size: 20),
          SizedBox(width: 8),
          Text('FCM Test Tools'),
        ],
      ),
    );
  }

  /// Handle debug menu selection
  static void handleDebugMenuSelection(String value, BuildContext context) {
    if (value == 'fcm_test') {
      navigateToTestPage(context);
    }
  }
}
