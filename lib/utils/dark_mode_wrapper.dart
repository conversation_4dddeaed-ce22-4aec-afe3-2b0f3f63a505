import 'package:flutter/material.dart';

/// A wrapper widget that applies dark mode for specific pages
/// regardless of the app's theme setting
class DarkModeWrapper extends StatelessWidget {
  final Widget child;

  const DarkModeWrapper({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    // Force dark theme for this widget and its children
    return Theme(
      data: ThemeData.dark().copyWith(
        // Copy important theme attributes from the app's dark theme
        primaryColor: Theme.of(context).primaryColor,
        colorScheme: ColorScheme.dark(
          primary: const Color(0xFF8cc051), // Green primary color
          secondary: const Color(0xFF3D7AF5), // Blue secondary color
          surface: const Color(0xFF1E1E1E), // Dark surface color
          surfaceTint: const Color(0xFF121212), // Dark surface tint color
          onSurface: Colors.white,
        ),
        scaffoldBackgroundColor: const Color(0xFF121212),
        cardColor: const Color(0xFF1E1E1E),
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFF1E1E1E),
          foregroundColor: Colors.white,
          elevation: 0,
          iconTheme: IconThemeData(color: Colors.white),
        ),
        textTheme: const TextTheme(
          titleLarge: TextStyle(color: Colors.white),
          titleMedium: TextStyle(color: Colors.white),
          titleSmall: TextStyle(color: Colors.white),
          bodyLarge: TextStyle(color: Colors.white),
          bodyMedium: TextStyle(color: Colors.white),
          bodySmall: TextStyle(color: Colors.grey),
        ),
        inputDecorationTheme: InputDecorationTheme(
          fillColor: const Color(0xFF2C2C2C),
          filled: true,
          hintStyle: TextStyle(color: Colors.grey.shade400),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(25),
            borderSide: BorderSide.none,
          ),
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            foregroundColor: Colors.white,
          ),
        ),
      ),
      child: child,
    );
  }
}
