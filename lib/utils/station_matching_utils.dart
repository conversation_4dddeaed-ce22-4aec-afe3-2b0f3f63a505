import 'package:flutter/foundation.dart';

/// DEPRECATED: Station matching utilities for UID extraction from markers
/// Markers do not contain UIDs - only use nearest station, search, or paginate APIs for UIDs
class StationMatchingUtils {
  /// DEPRECATED: Do not use - markers don't contain UIDs
  @Deprecated(
      'Markers do not contain UIDs. Use nearest station, search, or paginate APIs instead.')
  static Map<String, dynamic> enrichStationWithUid(Map<String, dynamic> station,
      List<Map<String, dynamic>> nearestStations) {
    try {
      final double latitude = station['latitude'] as double? ?? 0.0;
      final double longitude = station['longitude'] as double? ?? 0.0;
      final String stationName =
          (station['name'] as String? ?? '').toLowerCase();
      final String stationAddress =
          (station['address'] as String? ?? '').toLowerCase();

      if (latitude == 0.0 || longitude == 0.0) {
        debugPrint('Cannot enrich station - invalid coordinates');
        return station;
      }

      // Check if the station already has a UID
      if (station['uid'] != null && station['uid'].toString().isNotEmpty) {
        debugPrint('Station already has UID: ${station["uid"]}');
        return station;
      }

      // Create a new map that we can modify
      final Map<String, dynamic> enrichedStation =
          Map<String, dynamic>.from(station);

      if (nearestStations.isNotEmpty) {
        debugPrint(
            'Searching for UID match among ${nearestStations.length} nearest stations');

        // Try multiple matching strategies in priority order

        // 1. First try exact coordinate match with wider threshold
        final double coordinateThreshold = 0.001; // Increased from 0.0001
        var matchingStation = nearestStations.firstWhere(
          (s) {
            final stationLat = s['latitude'] as double? ?? 0.0;
            final stationLng = s['longitude'] as double? ?? 0.0;

            return (stationLat - latitude).abs() < coordinateThreshold &&
                (stationLng - longitude).abs() < coordinateThreshold;
          },
          orElse: () => <String, dynamic>{},
        );

        // 2. If no coordinate match, try matching by name
        if (matchingStation.isEmpty && stationName.isNotEmpty) {
          matchingStation = nearestStations.firstWhere(
            (s) {
              final name = (s['name'] as String? ?? '').toLowerCase();
              return name.isNotEmpty &&
                  (name == stationName ||
                      name.contains(stationName) ||
                      stationName.contains(name));
            },
            orElse: () => <String, dynamic>{},
          );
        }

        // 3. If still no match, try matching by address
        if (matchingStation.isEmpty && stationAddress.isNotEmpty) {
          matchingStation = nearestStations.firstWhere(
            (s) {
              final address = (s['address'] as String? ?? '').toLowerCase();
              return address.isNotEmpty &&
                  (address == stationAddress ||
                      address.contains(stationAddress) ||
                      stationAddress.contains(address));
            },
            orElse: () => <String, dynamic>{},
          );
        }

        // 4. If still no match, try matching by partial address (first 10 chars)
        if (matchingStation.isEmpty && stationAddress.length > 10) {
          matchingStation = nearestStations.firstWhere(
            (s) {
              final address = (s['address'] as String? ?? '').toLowerCase();
              return address.length > 10 &&
                  (address.substring(0, 10) == stationAddress.substring(0, 10));
            },
            orElse: () => <String, dynamic>{},
          );
        }

        // If we found a matching station with UID, use it
        if (matchingStation.isNotEmpty &&
            matchingStation['uid'] != null &&
            matchingStation['uid'].toString().isNotEmpty) {
          final String uid = matchingStation['uid'].toString();
          debugPrint('Enriched station with UID: $uid using matching strategy');
          enrichedStation['uid'] = uid;

          // Also copy other useful fields that might be missing
          if (enrichedStation['stationId'] == null &&
              matchingStation['stationId'] != null) {
            enrichedStation['stationId'] = matchingStation['stationId'];
          }

          // Ensure chargePointId is present
          if (enrichedStation['chargePointId'] == null &&
              matchingStation['chargePointId'] != null) {
            enrichedStation['chargePointId'] = matchingStation['chargePointId'];
          }
        } else {
          debugPrint('No matching station found with UID');
        }
      }

      return enrichedStation;
    } catch (e) {
      debugPrint('Error enriching station with UID: $e');
      return station; // Return original station if enrichment fails
    }
  }

  /// DEPRECATED: Batch process a list of stations to enrich them with UIDs
  @Deprecated(
      'Markers do not contain UIDs. Use nearest station, search, or paginate APIs instead.')
  static List<Map<String, dynamic>> enrichStationsWithUid(
      List<Map<String, dynamic>> stations,
      List<Map<String, dynamic>> nearestStations) {
    if (stations.isEmpty || nearestStations.isEmpty) {
      return stations;
    }

    debugPrint(
        'Enriching ${stations.length} stations with UIDs from ${nearestStations.length} nearest stations');

    return stations
        .map((station) => enrichStationWithUid(station, nearestStations))
        .toList();
  }
}
