import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'dart:io';

/// Utility class for shared notification channel logic
/// Ensures both local and FCM builders use identical channel settings
class NotificationUtils {
  NotificationUtils._(); // Private constructor to prevent instantiation

  /// Shared notification channel constants
  static const String chargingChannelId = 'charging_session_custom';
  static const String fcmChannelId = 'ecoplug_fcm';
  static const String chargingChannelName = 'Charging Session (Custom)';
  static const String fcmChannelName = 'EcoPlug FCM Notifications';
  static const String chargingChannelDescription =
      'Custom pin bar style charging notifications';
  static const String fcmChannelDescription =
      'Firebase Cloud Messaging notifications from EcoPlug';

  /// Creates a unified notification channel with identical settings for both local and FCM notifications
  /// Guards against IMPORTANCE_NONE and prompts user to re-enable if disabled
  static Future<bool> createNotificationChannel(
    FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin, {
    String? customChannelId,
    String? customChannelName,
    String? customChannelDescription,
    bool isChargingChannel = false,
  }) async {
    if (!Platform.isAndroid) {
      debugPrint('📱 Skipping channel creation - not Android platform');
      return true;
    }

    // Use defaults or custom values
    final channelId = customChannelId ??
        (isChargingChannel ? chargingChannelId : fcmChannelId);
    final channelName = customChannelName ??
        (isChargingChannel ? chargingChannelName : fcmChannelName);
    final channelDescription = customChannelDescription ??
        (isChargingChannel
            ? chargingChannelDescription
            : fcmChannelDescription);

    try {
      debugPrint('🔔 Creating notification channel: $channelId');

      final androidPlugin =
          flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();

      if (androidPlugin == null) {
        debugPrint('❌ Android plugin not available for channel creation');
        return false;
      }

      // Create notification channel with harmonized settings
      final channel = AndroidNotificationChannel(
        channelId,
        channelName,
        description: channelDescription,
        importance: Importance.high, // High importance for visibility
        playSound: false, // Silent notifications as specified
        enableVibration: false, // No vibration for charging updates
        showBadge: true,
        enableLights: true,
        ledColor: const Color(0xFF4CAF50), // EcoPlug green
      );

      await androidPlugin.createNotificationChannel(channel);
      debugPrint('✅ Notification channel created successfully: $channelId');

      // Check if channel was created with IMPORTANCE_NONE (disabled by user)
      await _checkChannelImportance(androidPlugin, channelId, channelName);

      return true;
    } catch (e) {
      debugPrint('❌ Error creating notification channel $channelId: $e');
      return false;
    }
  }

  /// Checks if the notification channel has IMPORTANCE_NONE and warns user
  static Future<void> _checkChannelImportance(
    AndroidFlutterLocalNotificationsPlugin androidPlugin,
    String channelId,
    String channelName,
  ) async {
    try {
      // Note: flutter_local_notifications doesn't expose getNotificationChannel
      // This would need to be implemented at the native Android level
      // For now, we'll log a warning message
      debugPrint(
          '⚠️ Please ensure notifications are enabled for $channelName in device settings');
      debugPrint('⚠️ If notifications appear disabled, please go to:');
      debugPrint('   Settings > Apps > EcoPlug > Notifications > $channelName');
      debugPrint(
          '   and ensure the channel is enabled with appropriate importance level');
    } catch (e) {
      debugPrint('❌ Error checking channel importance: $e');
    }
  }

  /// Creates the standard charging session notification channel
  static Future<bool> createChargingNotificationChannel(
    FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin,
  ) async {
    return await createNotificationChannel(
      flutterLocalNotificationsPlugin,
      isChargingChannel: true,
    );
  }

  /// Creates the standard FCM notification channel
  static Future<bool> createFCMNotificationChannel(
    FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin,
  ) async {
    return await createNotificationChannel(
      flutterLocalNotificationsPlugin,
      isChargingChannel: false,
    );
  }

  /// Checks if notifications are enabled and provides user guidance
  static Future<bool> areNotificationsEnabled(
    FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin,
  ) async {
    if (!Platform.isAndroid) return true;

    try {
      final androidPlugin =
          flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();

      if (androidPlugin == null) return false;

      final areEnabled = await androidPlugin.areNotificationsEnabled();

      if (areEnabled != null && !areEnabled) {
        debugPrint('⚠️ Notifications are disabled for EcoPlug');
        debugPrint('💡 To enable notifications:');
        debugPrint('   1. Go to Settings > Apps > EcoPlug');
        debugPrint('   2. Tap on "Notifications"');
        debugPrint('   3. Enable "Show notifications"');
        debugPrint('   4. Ensure individual channels are enabled');
      }

      return areEnabled ?? false;
    } catch (e) {
      debugPrint('❌ Error checking notification permissions: $e');
      return false;
    }
  }

  /// Provides user guidance for re-enabling disabled notifications
  static void promptUserToEnableNotifications() {
    debugPrint('🔔 === NOTIFICATION PERMISSION GUIDANCE ===');
    debugPrint('📱 It looks like notifications might be disabled.');
    debugPrint('💡 To receive important charging updates:');
    debugPrint('');
    debugPrint('   1. Open device Settings');
    debugPrint('   2. Go to Apps → EcoPlug → Notifications');
    debugPrint('   3. Enable "Show notifications"');
    debugPrint('   4. Enable these channels:');
    debugPrint('      • $chargingChannelName');
    debugPrint('      • $fcmChannelName');
    debugPrint('   5. Set importance to "High" for best experience');
    debugPrint('');
    debugPrint('🔋 This ensures you get real-time charging session updates!');
    debugPrint('========================================');
  }
}
