import 'package:flutter/material.dart';
import '../utils/api_exception.dart';
import '../utils/error_handler.dart';

/// Utility class for handling API responses consistently
class ApiResponseHandler {
  /// Handle API response with a callback for success and error
  static Future<void> handle<T>({
    required Future<T> Function() apiCall,
    required Function(T data) onSuccess,
    required Function(String message, String? errorCode) onError,
    Function()? onLoading,
    Function()? onComplete,
    bool showLoadingIndicator = true,
  }) async {
    try {
      // Show loading indicator if requested
      if (showLoadingIndicator && onLoading != null) {
        onLoading();
      }

      // Make the API call
      final response = await apiCall();

      // Call the success callback
      onSuccess(response);
    } on ApiException catch (e) {
      // Get a user-friendly error message
      final errorMessage = e.code != null
          ? ErrorHandler.getErrorMessageByCode(e.code)
          : ErrorHandler.getUserFriendlyMessage(e.message);

      // Call the error callback
      onError(errorMessage, e.code);

      // Log the error for debugging
      debugPrint('API Exception: ${e.message} (Code: ${e.code})');
    } catch (e) {
      // Handle other exceptions
      final errorMessage = ErrorHandler.getUserFriendlyMessage(e.toString());
      onError(errorMessage, null);

      // Log the error for debugging
      debugPrint('Unexpected error: $e');
    } finally {
      // Call the complete callback if provided
      if (onComplete != null) {
        onComplete();
      }
    }
  }

  /// Handle API response with a silent retry mechanism
  /// This will retry the API call silently without showing loading indicators
  static Future<T?> handleWithSilentRetry<T>({
    required Future<T> Function() apiCall,
    int maxRetries = 2,
    Duration retryDelay = const Duration(milliseconds: 500),
  }) async {
    int retryCount = 0;
    Exception? lastException;

    while (retryCount <= maxRetries) {
      try {
        // Make the API call
        final response = await apiCall();
        return response;
      } on ApiException catch (e) {
        lastException = e;
        debugPrint('API Exception (attempt ${retryCount + 1}/${maxRetries + 1}): ${e.message}');
      } catch (e) {
        lastException = Exception(e.toString());
        debugPrint('Unexpected error (attempt ${retryCount + 1}/${maxRetries + 1}): $e');
      }

      // Increment retry count
      retryCount++;

      // If we've reached the maximum retries, break out of the loop
      if (retryCount > maxRetries) break;

      // Wait before retrying
      await Future.delayed(Duration(
        milliseconds: retryDelay.inMilliseconds * retryCount,
      ));
    }

    // If we get here, all retries failed
    if (lastException != null) {
      debugPrint('All retry attempts failed: $lastException');
    }

    return null;
  }
}
