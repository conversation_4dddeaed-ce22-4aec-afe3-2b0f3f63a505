import 'package:flutter/material.dart';
import '../services/connectivity_error_service.dart';

/// Extension methods for easy connectivity error handling
extension ConnectivityErrorUtils on BuildContext {
  /// Show connectivity error page
  Future<void> showConnectivityError({
    String? message,
    VoidCallback? onRetry,
    bool replaceCurrentRoute = false,
  }) async {
    await ConnectivityErrorService.showConnectivityError(
      this,
      customMessage: message,
      onRetry: onRetry,
      replaceCurrentRoute: replaceCurrentRoute,
    );
  }

  /// Show connectivity error as bottom sheet
  Future<void> showConnectivityErrorSheet({
    String? message,
    VoidCallback? onRetry,
  }) async {
    await ConnectivityErrorService.showConnectivityErrorSheet(
      this,
      customMessage: message,
      onRetry: onRetry,
    );
  }

  /// Navigate to connectivity error page using named route
  Future<void> pushConnectivityError({
    String? errorMessage,
    bool replaceCurrentRoute = false,
  }) async {
    final args = <String, dynamic>{
      if (errorMessage != null) 'error_message': errorMessage,
    };

    if (replaceCurrentRoute) {
      await Navigator.of(this).pushReplacementNamed(
        '/connectivity-error',
        arguments: args,
      );
    } else {
      await Navigator.of(this).pushNamed(
        '/connectivity-error',
        arguments: args,
      );
    }
  }

  /// Handle API call with automatic connectivity error handling
  Future<T> handleApiCall<T>(
    Future<T> Function() apiCall, {
    String? customErrorMessage,
    VoidCallback? onRetry,
    bool showErrorPage = true,
    bool showAsSheet = false,
  }) async {
    return await ConnectivityErrorService.handleApiCall<T>(
      apiCall,
      context: this,
      customErrorMessage: customErrorMessage,
      onRetry: onRetry,
      showErrorPage: showErrorPage,
      showAsSheet: showAsSheet,
    );
  }

  /// Execute operation with connectivity check
  Future<T?> withConnectivityCheck<T>(
    Future<T> Function() operation, {
    String? errorMessage,
    bool showAsSheet = false,
  }) async {
    return await ConnectivityErrorService.withConnectivityCheck<T>(
      this,
      operation,
      errorMessage: errorMessage,
      showAsSheet: showAsSheet,
    );
  }
}

/// Utility class for common connectivity error scenarios
class ConnectivityErrorScenarios {
  /// Error message for API timeout
  static const String apiTimeout =
      'The request is taking longer than expected. Please check your internet connection and try again.';

  /// Error message for server unreachable
  static const String serverUnreachable =
      'Unable to reach the server. Please check your internet connection.';

  /// Error message for general network error
  static const String networkError =
      'A network error occurred. Please check your internet connection and try again.';

  /// Error message for offline state
  static const String offline =
      'You appear to be offline. Please check your internet connection.';

  /// Error message for slow connection
  static const String slowConnection =
      'Your connection seems slow. Please wait or try again with a better connection.';

  /// Error message for DNS resolution failure
  static const String dnsError =
      'Unable to resolve server address. Please check your internet connection.';

  /// Error message for connection refused
  static const String connectionRefused =
      'Connection was refused by the server. Please try again later.';

  /// Error message for SSL/TLS errors
  static const String sslError =
      'Secure connection failed. Please check your internet connection and try again.';
}

/// Mixin for widgets that need connectivity error handling
mixin ConnectivityErrorMixin<T extends StatefulWidget> on State<T> {
  /// Show connectivity error with retry callback
  Future<void> showConnectivityErrorWithRetry(
    VoidCallback retryCallback, {
    String? customMessage,
    bool showAsSheet = false,
  }) async {
    if (!mounted) return;

    if (showAsSheet) {
      await context.showConnectivityErrorSheet(
        message: customMessage,
        onRetry: retryCallback,
      );
    } else {
      await context.showConnectivityError(
        message: customMessage,
        onRetry: retryCallback,
      );
    }
  }

  /// Handle error and show connectivity error if applicable
  Future<void> handleErrorWithConnectivityCheck(
    dynamic error, {
    VoidCallback? retryCallback,
    String? customMessage,
    bool showAsSheet = false,
  }) async {
    if (!mounted) return;

    if (ConnectivityErrorService.isConnectivityError(error)) {
      final errorMessage = customMessage ??
          ConnectivityErrorService.getConnectivityErrorMessage(error);

      await showConnectivityErrorWithRetry(
        retryCallback ?? () {},
        customMessage: errorMessage,
        showAsSheet: showAsSheet,
      );
    } else {
      // Handle non-connectivity errors as needed
      debugPrint('Non-connectivity error: $error');
    }
  }

  /// Execute async operation with connectivity error handling
  Future<R?> executeWithConnectivityHandling<R>(
    Future<R> Function() operation, {
    VoidCallback? retryCallback,
    String? errorMessage,
    bool showAsSheet = false,
  }) async {
    try {
      return await operation();
    } catch (error) {
      await handleErrorWithConnectivityCheck(
        error,
        retryCallback: retryCallback,
        customMessage: errorMessage,
        showAsSheet: showAsSheet,
      );
      return null;
    }
  }
}
