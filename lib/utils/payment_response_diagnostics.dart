import 'dart:convert';
import 'package:flutter/foundation.dart';

/// Simple transaction interface for diagnostics
abstract class TransactionLike {
  String get id;
  String get title;
  String? get remark;
  String? get status;
  double get amount;
  DateTime get dateTime;
  String? get type;
}

/// Comprehensive payment response diagnostics tool
/// Helps identify and debug payment response handling issues
class PaymentResponseDiagnostics {
  static const String _logPrefix = '🔍 PAYMENT_DIAGNOSTICS';

  /// Diagnose PayU payment response issues
  static void diagnosePayUResponse({
    required String transactionId,
    required Map<String, dynamic> response,
    required String expectedStatus,
    List<TransactionLike>? currentTransactions,
  }) {
    debugPrint('$_logPrefix: ========== PAYU RESPONSE DIAGNOSIS START ==========');
    debugPrint('$_logPrefix: Transaction ID: $transactionId');
    debugPrint('$_logPrefix: Expected Status: $expectedStatus');
    debugPrint('$_logPrefix: Response Keys: ${response.keys.toList()}');

    // 1. Check response structure
    _checkResponseStructure('PayU', response);

    // 2. Check status field variations
    _checkStatusFields('PayU', response);

    // 3. Check transaction ID variations
    _checkTransactionIdFields('PayU', response, transactionId);

    // 4. Check if transaction exists in current data
    if (currentTransactions != null) {
      _checkTransactionInList(transactionId, currentTransactions);
    }

    // 5. Generate recommendations
    _generatePayURecommendations(response, transactionId);

    debugPrint('$_logPrefix: ========== PAYU RESPONSE DIAGNOSIS END ==========');
  }

  /// Diagnose PhonePe payment response issues
  static void diagnosePhonePeResponse({
    required String transactionId,
    required Map<String, dynamic> response,
    required String expectedStatus,
    List<TransactionLike>? currentTransactions,
  }) {
    debugPrint('$_logPrefix: ========== PHONEPE RESPONSE DIAGNOSIS START ==========');
    debugPrint('$_logPrefix: Transaction ID: $transactionId');
    debugPrint('$_logPrefix: Expected Status: $expectedStatus');
    debugPrint('$_logPrefix: Response Keys: ${response.keys.toList()}');

    // 1. Check response structure
    _checkResponseStructure('PhonePe', response);

    // 2. Check status field variations
    _checkStatusFields('PhonePe', response);

    // 3. Check transaction ID variations
    _checkTransactionIdFields('PhonePe', response, transactionId);

    // 4. Check if transaction exists in current data
    if (currentTransactions != null) {
      _checkTransactionInList(transactionId, currentTransactions);
    }

    // 5. Generate recommendations
    _generatePhonePeRecommendations(response, transactionId);

    debugPrint('$_logPrefix: ========== PHONEPE RESPONSE DIAGNOSIS END ==========');
  }

  /// Check response structure for common issues
  static void _checkResponseStructure(String gateway, Map<String, dynamic> response) {
    debugPrint('$_logPrefix: === RESPONSE STRUCTURE CHECK ===');

    if (response.isEmpty) {
      debugPrint('$_logPrefix: ❌ ISSUE: Response is empty');
      return;
    }

    debugPrint('$_logPrefix: ✅ Response has ${response.length} fields');

    // Check for nested structures
    for (final entry in response.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value is Map) {
        debugPrint('$_logPrefix: 📁 Nested object found: $key (${value.length} fields)');
      } else if (value is List) {
        debugPrint('$_logPrefix: 📋 Array found: $key (${value.length} items)');
      } else {
        debugPrint('$_logPrefix: 📄 Field: $key = $value (${value.runtimeType})');
      }
    }
  }

  /// Check various status field possibilities
  static void _checkStatusFields(String gateway, Map<String, dynamic> response) {
    debugPrint('$_logPrefix: === STATUS FIELD CHECK ===');

    final statusFields = [
      'status', 'Status', 'STATUS',
      'statusCode', 'status_code', 'code',
      'state', 'result', 'outcome',
      'paymentStatus', 'payment_status',
      'transactionStatus', 'transaction_status'
    ];

    bool foundStatus = false;
    for (final field in statusFields) {
      if (response.containsKey(field)) {
        debugPrint('$_logPrefix: ✅ Status field found: $field = ${response[field]}');
        foundStatus = true;
      }
    }

    if (!foundStatus) {
      debugPrint('$_logPrefix: ❌ ISSUE: No status field found in response');
      debugPrint('$_logPrefix: Available fields: ${response.keys.toList()}');
    }
  }

  /// Check various transaction ID field possibilities
  static void _checkTransactionIdFields(String gateway, Map<String, dynamic> response, String expectedTxnId) {
    debugPrint('$_logPrefix: === TRANSACTION ID CHECK ===');

    final txnIdFields = [
      'txnid', 'txnId', 'TxnId', 'TXNID',
      'transactionId', 'transaction_id', 'TransactionId',
      'id', 'Id', 'ID',
      'orderId', 'order_id', 'OrderId',
      'merchantTransactionId', 'merchant_transaction_id',
      'mihpayid', 'paymentId', 'payment_id'
    ];

    bool foundTxnId = false;
    for (final field in txnIdFields) {
      if (response.containsKey(field)) {
        final value = response[field];
        debugPrint('$_logPrefix: 📋 Transaction ID field: $field = $value');

        if (value.toString() == expectedTxnId) {
          debugPrint('$_logPrefix: ✅ MATCH: $field matches expected transaction ID');
          foundTxnId = true;
        } else {
          debugPrint('$_logPrefix: ⚠️ MISMATCH: $field does not match expected ID ($expectedTxnId)');
        }
      }
    }

    if (!foundTxnId) {
      debugPrint('$_logPrefix: ❌ ISSUE: No matching transaction ID found in response');
      debugPrint('$_logPrefix: Expected: $expectedTxnId');
    }
  }

  /// Check if transaction exists in current transaction list
  static void _checkTransactionInList(String transactionId, List<TransactionLike> transactions) {
    debugPrint('$_logPrefix: === TRANSACTION EXISTENCE CHECK ===');
    debugPrint('$_logPrefix: Checking ${transactions.length} transactions for ID: $transactionId');

    TransactionLike? foundTransaction;
    for (final tx in transactions) {
      if (tx.id == transactionId ||
          tx.title.contains(transactionId) ||
          tx.remark?.contains(transactionId) == true) {
        foundTransaction = tx;
        break;
      }
    }

    if (foundTransaction != null) {
      debugPrint('$_logPrefix: ✅ Transaction found in wallet data');
      debugPrint('$_logPrefix: Status: ${foundTransaction.status}');
      debugPrint('$_logPrefix: Amount: ${foundTransaction.amount}');
      debugPrint('$_logPrefix: Date: ${foundTransaction.dateTime}');
      debugPrint('$_logPrefix: Type: ${foundTransaction.type}');
    } else {
      debugPrint('$_logPrefix: ❌ ISSUE: Transaction not found in current wallet data');
      debugPrint('$_logPrefix: This indicates the server may not have created/updated the transaction');
    }
  }

  /// Generate PayU-specific recommendations
  static void _generatePayURecommendations(Map<String, dynamic> response, String transactionId) {
    debugPrint('$_logPrefix: === PAYU RECOMMENDATIONS ===');

    final status = response['status']?.toString().toLowerCase();

    if (status == null) {
      debugPrint('$_logPrefix: 🔧 RECOMMENDATION: Add status field to PayU response');
      debugPrint('$_logPrefix: 🔧 RECOMMENDATION: Check PayU SDK callback implementation');
    }

    if (!response.containsKey('txnid') && !response.containsKey('transactionId')) {
      debugPrint('$_logPrefix: 🔧 RECOMMENDATION: Ensure transaction ID is included in PayU response');
    }

    if (status == 'pending' || status == 'processing') {
      debugPrint('$_logPrefix: 🔧 RECOMMENDATION: Implement server-side status polling for pending transactions');
    }

    debugPrint('$_logPrefix: 🔧 RECOMMENDATION: Verify server endpoint /user/payment/response-payu is working');
    debugPrint('$_logPrefix: 🔧 RECOMMENDATION: Check database transaction status update logic');
  }

  /// Generate PhonePe-specific recommendations
  static void _generatePhonePeRecommendations(Map<String, dynamic> response, String transactionId) {
    debugPrint('$_logPrefix: === PHONEPE RECOMMENDATIONS ===');

    final statusCode = response['statusCode']?.toString();
    final code = response['code']?.toString();

    if (statusCode == null && code == null) {
      debugPrint('$_logPrefix: 🔧 RECOMMENDATION: Check PhonePe response format - missing status code');
    }

    debugPrint('$_logPrefix: 🔧 RECOMMENDATION: Verify server endpoint /user/payment/response-phonepev2 is working');
    debugPrint('$_logPrefix: 🔧 RECOMMENDATION: Implement PhonePe webhook for server-side notifications');
  }

  /// Log comprehensive payment flow state
  static void logPaymentFlowState({
    required String gateway,
    required String transactionId,
    required String currentStep,
    Map<String, dynamic>? additionalData,
  }) {
    debugPrint('$_logPrefix: ========== PAYMENT FLOW STATE ==========');
    debugPrint('$_logPrefix: Gateway: $gateway');
    debugPrint('$_logPrefix: Transaction ID: $transactionId');
    debugPrint('$_logPrefix: Current Step: $currentStep');
    debugPrint('$_logPrefix: Timestamp: ${DateTime.now().toIso8601String()}');

    if (additionalData != null) {
      debugPrint('$_logPrefix: Additional Data: ${jsonEncode(additionalData)}');
    }

    debugPrint('$_logPrefix: ========================================');
  }
}
