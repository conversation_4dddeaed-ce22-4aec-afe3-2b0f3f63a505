import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ecoplug/services/api_bridge.dart'; // Keep ApiBridge if still used elsewhere
import 'package:ecoplug/core/api/api_service.dart'; // Import ApiService
import 'package:ecoplug/features/station/services/station_service.dart'; // Import StationService
import 'package:ecoplug/services/auth/auth_service.dart'; // Import the fixed AuthService
import 'package:ecoplug/services/connectivity_service.dart';
import 'package:ecoplug/repositories/station_repository.dart';

// Provider for ApiService
final apiServiceProvider = Provider<ApiService>((ref) {
  return ApiService();
});

// Provider for ApiBridge (keep if needed)
final apiBridgeProvider = Provider<ApiBridge>((ref) {
  return ApiBridge();
});

// Provider for the fixed AuthService (singleton)
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService();
});

// Provider for ConnectivityService
final connectivityServiceProvider = Provider<ConnectivityService>((ref) {
  return ConnectivityService();
});

// Provider for StationRepository
final stationRepositoryProvider = Provider<StationRepository>((ref) {
  return StationRepository();
});

// Provider for StationService
final stationServiceProvider = Provider<StationService>((ref) {
  final apiService = ref.read(apiServiceProvider);
  final connectivityService = ref.read(connectivityServiceProvider);
  final stationRepository = ref.read(stationRepositoryProvider);
  return StationService(connectivityService, stationRepository, apiService);
});

// List of all core providers for easy access/override
final coreProviders = <Provider>[
  apiServiceProvider,
  apiBridgeProvider,
  authServiceProvider, // Add the fixed auth service provider
  stationServiceProvider,
];
