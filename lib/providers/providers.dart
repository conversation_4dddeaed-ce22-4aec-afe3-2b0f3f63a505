import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ecoplug/providers/core_providers.dart';

// Re-export all providers for easy access
export 'package:ecoplug/providers/core_providers.dart';
export 'package:ecoplug/providers/theme_provider.dart';
export 'package:ecoplug/features/profile/application/profile_notifier.dart';

// Export the stations provider from the correct location
export 'package:ecoplug/features/stations/application/stations_provider.dart'
    show stationsProvider, StationsNotifier, StationsState;

export 'package:ecoplug/providers/map_marker_provider.dart';

// Combined provider container for the app
final appProviders = ProviderContainer(
  overrides: [
    ...coreProviders,
    // Add other provider overrides here as needed
  ],
);
