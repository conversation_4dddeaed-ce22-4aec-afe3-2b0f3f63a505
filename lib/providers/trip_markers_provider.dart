import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../models/place_suggestion.dart';
import '../services/trip_marker_service.dart';

/// Provider for TripMarkerService
final tripMarkerServiceProvider = Provider<TripMarkerService>((ref) {
  return TripMarkerService();
});

/// State class for trip location markers
class TripMarkersState {
  final PlaceSuggestion? startLocation;
  final PlaceSuggestion? destinationLocation;
  final Set<Marker> markers;
  final bool isLoading;
  final String? error;

  const TripMarkersState({
    this.startLocation,
    this.destinationLocation,
    this.markers = const {},
    this.isLoading = false,
    this.error,
  });

  TripMarkersState copyWith({
    PlaceSuggestion? startLocation,
    PlaceSuggestion? destinationLocation,
    Set<Marker>? markers,
    bool? isLoading,
    String? error,
  }) {
    return TripMarkersState(
      startLocation: startLocation,
      destinationLocation: destinationLocation,
      markers: markers ?? this.markers,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  /// Check if we have both start and destination locations
  bool get hasValidLocations =>
      startLocation?.coordinates != null &&
      destinationLocation?.coordinates != null;

  /// Get start location coordinates
  LatLng? get startCoordinates => startLocation?.coordinates;

  /// Get destination coordinates
  LatLng? get destinationCoordinates => destinationLocation?.coordinates;

  @override
  String toString() {
    return 'TripMarkersState(start: ${startLocation?.mainText}, dest: ${destinationLocation?.mainText}, markers: ${markers.length})';
  }
}

/// Notifier for managing trip location markers
class TripMarkersNotifier extends StateNotifier<TripMarkersState> {
  final TripMarkerService _markerService;

  TripMarkersNotifier(this._markerService) : super(const TripMarkersState());

  /// Update start location and create marker
  Future<void> updateStartLocation(PlaceSuggestion location) async {
    if (location.coordinates == null) {
      debugPrint('❌ TRIP MARKERS: Start location has no coordinates');
      return;
    }

    debugPrint('📍 TRIP MARKERS: Updating start location to ${location.mainText}');

    state = state.copyWith(
      startLocation: location,
      isLoading: true,
      error: null,
    );

    await _updateMarkers();
  }

  /// Update destination location and create marker
  Future<void> updateDestinationLocation(PlaceSuggestion location) async {
    if (location.coordinates == null) {
      debugPrint('❌ TRIP MARKERS: Destination location has no coordinates');
      return;
    }

    debugPrint('📍 TRIP MARKERS: Updating destination location to ${location.mainText}');

    state = state.copyWith(
      destinationLocation: location,
      isLoading: true,
      error: null,
    );

    await _updateMarkers();
  }

  /// Update both locations at once
  Future<void> updateLocations({
    PlaceSuggestion? startLocation,
    PlaceSuggestion? destinationLocation,
  }) async {
    debugPrint('📍 TRIP MARKERS: Updating both locations');

    state = state.copyWith(
      startLocation: startLocation ?? state.startLocation,
      destinationLocation: destinationLocation ?? state.destinationLocation,
      isLoading: true,
      error: null,
    );

    await _updateMarkers();
  }

  /// Swap start and destination locations
  Future<void> swapLocations() async {
    if (state.startLocation == null || state.destinationLocation == null) {
      debugPrint('❌ TRIP MARKERS: Cannot swap - missing locations');
      return;
    }

    debugPrint('🔄 TRIP MARKERS: Swapping start and destination locations');

    state = state.copyWith(
      startLocation: state.destinationLocation,
      destinationLocation: state.startLocation,
      isLoading: true,
      error: null,
    );

    await _updateMarkers();
  }

  /// Clear all location markers
  void clearMarkers() {
    debugPrint('🗑️ TRIP MARKERS: Clearing all markers');
    state = const TripMarkersState();
  }

  /// Clear only start location marker
  Future<void> clearStartLocation() async {
    debugPrint('🗑️ TRIP MARKERS: Clearing start location');
    state = state.copyWith(
      startLocation: null,
      isLoading: true,
      error: null,
    );
    await _updateMarkers();
  }

  /// Clear only destination location marker
  Future<void> clearDestinationLocation() async {
    debugPrint('🗑️ TRIP MARKERS: Clearing destination location');
    state = state.copyWith(
      destinationLocation: null,
      isLoading: true,
      error: null,
    );
    await _updateMarkers();
  }

  /// Update markers on the map
  Future<void> _updateMarkers() async {
    try {
      final Set<Marker> newMarkers = {};

      // Create start location marker
      if (state.startLocation?.coordinates != null) {
        final startMarker = await _createStartMarker(state.startLocation!);
        if (startMarker != null) {
          newMarkers.add(startMarker);
        }
      }

      // Create destination location marker
      if (state.destinationLocation?.coordinates != null) {
        final destMarker = await _createDestinationMarker(state.destinationLocation!);
        if (destMarker != null) {
          newMarkers.add(destMarker);
        }
      }

      state = state.copyWith(
        markers: newMarkers,
        isLoading: false,
        error: null,
      );

      debugPrint('✅ TRIP MARKERS: Updated ${newMarkers.length} markers');
    } catch (e) {
      debugPrint('❌ TRIP MARKERS: Error updating markers: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to update markers: $e',
      );
    }
  }

  /// Create start location marker
  Future<Marker?> _createStartMarker(PlaceSuggestion location) async {
    try {
      final icon = await _markerService.createLocationMarker(
        isStartLocation: true,
        locationName: location.mainText,
      );

      return Marker(
        markerId: const MarkerId('trip_start'),
        position: location.coordinates!,
        icon: icon,
        anchor: const Offset(0.5, 0.5), // Center anchor for simple circle
        zIndex: 1000, // High z-index to appear above other markers
      );
    } catch (e) {
      debugPrint('❌ TRIP MARKERS: Error creating start marker: $e');
      return null;
    }
  }

  /// Create destination location marker
  Future<Marker?> _createDestinationMarker(PlaceSuggestion location) async {
    try {
      final icon = await _markerService.createLocationMarker(
        isStartLocation: false,
        locationName: location.mainText,
      );

      return Marker(
        markerId: const MarkerId('trip_destination'),
        position: location.coordinates!,
        icon: icon,
        anchor: const Offset(0.5, 0.5), // Center anchor for simple circle
        zIndex: 1001, // Slightly higher z-index than start marker
      );
    } catch (e) {
      debugPrint('❌ TRIP MARKERS: Error creating destination marker: $e');
      return null;
    }
  }
}

/// Provider for trip markers state
final tripMarkersProvider = StateNotifierProvider<TripMarkersNotifier, TripMarkersState>((ref) {
  final markerService = ref.watch(tripMarkerServiceProvider);
  return TripMarkersNotifier(markerService);
});

/// Provider for trip markers set (computed from trip markers state)
final tripMarkersSetProvider = Provider<Set<Marker>>((ref) {
  final tripMarkersState = ref.watch(tripMarkersProvider);
  return tripMarkersState.markers;
});

/// Provider for checking if trip has valid locations
final tripHasValidLocationsProvider = Provider<bool>((ref) {
  final tripMarkersState = ref.watch(tripMarkersProvider);
  return tripMarkersState.hasValidLocations;
});
