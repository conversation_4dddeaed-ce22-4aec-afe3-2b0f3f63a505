import 'package:ecoplug/providers/core_providers.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart'; // Import Flutter Riverpod
import '../models/station/station_marker_response.dart';
import '../services/map_marker_service.dart';
import '../utils/map_marker_utils.dart';
import '../services/api_bridge.dart'; // Import ApiBridge

// Define the AsyncNotifierProvider for Map Markers
final mapMarkerNotifierProvider =
    AsyncNotifierProvider<MapMarkerNotifier, List<StationMarkerData>>(
        MapMarkerNotifier.new);

// AsyncNotifier class to manage the map marker state
class MapMarkerNotifier extends AsyncNotifier<List<StationMarkerData>> {
  // Dependencies
  late final MapMarkerService _markerService;
  late final ApiBridge _apiBridge; // Add ApiBridge dependency

  @override
  Future<List<StationMarkerData>> build() async {
    // Initialize dependencies using ref.read
    _markerService =
        MapMarkerService(); // Replace with ref.read(mapMarkerServiceProvider) later
    _apiBridge = ref.read(apiBridgeProvider); // Initialize ApiBridge here

    // Load markers when the provider is created
    return _loadMarkers();
  }

  /// Load markers from the API or cache
  Future<List<StationMarkerData>> _loadMarkers() async {
    try {
      // Use the class-level _apiBridge
      final markers = await _markerService
          .getMarkers(() => _apiBridge.getApiStationMarkers());

      return markers;
    } catch (e) {
      // Re-throw the error to be caught by AsyncValue
      rethrow;
    }
  }

  /// Refresh markers from the API
  Future<void> refreshMarkers() async {
    // Clear the cache and load fresh markers
    await _markerService.clearCache();
    // Invalidate the current state to trigger a reload
    ref.invalidateSelf();
  }

  /// Get a marker image
  Future<Uint8List?> getMarkerImage(String url) {
    return _markerService.getMarkerImage(url);
  }

  // Helper to get formatted markers (can be a separate provider or method)
  List<Map<String, dynamic>> get formattedMarkers {
    // Access the current state (list of StationMarkerData)
    final markers =
        state.value ?? []; // Use value to get the data from AsyncValue
    return MapMarkerUtils.convertMarkersToMapFormat(markers);
  }
}
