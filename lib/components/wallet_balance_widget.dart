import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:line_icons/line_icons.dart';
import 'package:ecoplug/core/api/api_service.dart';
import '../models/wallet/wallet_response.dart';
import '../services/service_locator.dart'; // Add missing import for ServiceLocator

class WalletBalanceWidget extends StatefulWidget {
  const WalletBalanceWidget({super.key});

  @override
  State<WalletBalanceWidget> createState() => _WalletBalanceWidgetState();
}

class _WalletBalanceWidgetState extends State<WalletBalanceWidget> {
  final ApiService _apiService = ServiceLocator().apiService;
  bool _isLoading = true;
  double _balance = 0.0;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _fetchWalletBalance();
  }

  Future<void> _fetchWalletBalance() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final response = await _apiService.fetchWalletBalance();

      if (response['success'] == true && response['wallet'] != null) {
        final wallet = Wallet.fromJson(response['wallet']);

        if (mounted) {
          setState(() {
            _balance = wallet.balance ?? 0.0;
            _isLoading = false;
          });
        }

        if (kDebugMode) {
          debugPrint('Wallet balance: $_balance');
        }
      } else {
        if (mounted) {
          setState(() {
            _errorMessage = 'Failed to load balance';
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error fetching wallet balance: $e');
      }

      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load balance';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            LineIcons.wallet, // Using consistent navigation bar icon
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Wallet Balance',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color:
                      isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
                ),
              ),
              const SizedBox(height: 4),
              if (_isLoading)
                const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              else if (_errorMessage != null)
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 16,
                      color: Theme.of(context).colorScheme.error,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _errorMessage!,
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.error,
                        fontSize: 14,
                      ),
                    ),
                  ],
                )
              else
                Text(
                  '₹${_balance.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }
}
