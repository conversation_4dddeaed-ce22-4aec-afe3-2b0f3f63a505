import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
 
// ==================== APP COLORS ====================

/// App color constants for notifications
class AppColors {
  static const Color primaryGreen = Color(0xFF8cc051);
  static const Color primaryBlue = Color(0xFF2196F3);
  static const Color primaryOrange = Color(0xFFFF9800);
  static const Color primaryYellow = Color(0xFFFFC107);
  static const Color primaryRed = Color(0xFFF44336);
  static const Color primaryPurple = Color(0xFF9C27B0);
  static const Color primaryTeal = Color(0xFF009688);
}

// ==================== TIMING CONSTANTS ====================

/// Notification timing configurations
class NotificationTiming {
  static const int ledOnMs = 800;
  static const int ledOffMs = 1200;
  static const int vibrationPattern = 500;
  static const int autoCancel = 5000; // 5 seconds
  static const int chargingUpdateInterval = 30000; // 30 seconds
  static const int tokenRefreshInterval = 3600000; // 1 hour
}

// ==================== NOTIFICATION IDS ====================

/// Notification ID constants
class NotificationIds {
  static const int chargingSession = 1001;
  static const int stationAlert = 1002;
  static const int tripReminder = 1003;
  static const int walletUpdate = 1004;
  static const int promotion = 1005;
  static const int systemUpdate = 1006;
  static const int welcomeLogin = 1007;
  static const int fcmMessage = 2000; // Base ID for FCM messages
  static const int testNotification = 9999;
}

/// Centralized Notification Configuration for EcoPlug
/// Contains all notification channels, topics, preferences, and constants
class NotificationConfig {
  // Private constructor to prevent instantiation
  NotificationConfig._();

  // ==================== NOTIFICATION CHANNELS ====================

  /// Notification channel configurations - Only charging-related channels
  static const Map<String, NotificationChannelConfig> channels = {
    'charging_session': NotificationChannelConfig(
      id: 'charging_session',
      name: 'Charging Session',
      description:
          'Real-time EV charging session notifications with live metrics, progress tracking, and session controls',
      importance: Importance.high,
      playSound: false,
      enableVibration: false,
      showBadge: true,
      enableLights: true,
      ledColor: AppColors.primaryGreen,
      groupId: 'ecoplug_charging',
    ),
    'active_charging_session': NotificationChannelConfig(
      id: 'active_charging_session',
      name: 'Active Charging Session',
      description: 'Persistent notifications for ongoing charging sessions',
      importance: Importance.high,
      playSound: false,
      enableVibration: false,
      showBadge: true,
      enableLights: true,
      ledColor: AppColors.primaryGreen,
      groupId: 'ecoplug_charging',
    ),
  };

  // ==================== FCM TOPICS ====================

  /// FCM topic configurations - Only charging-related topics
  static const Map<String, TopicConfig> topics = {
    // Charging-specific topics only
    'charging_updates': TopicConfig(
      name: 'charging_updates',
      displayName: 'Charging Updates',
      description: 'Real-time charging session updates',
      defaultSubscribed: true,
      userConfigurable: true,
    ),
    // Note: Dynamic charging topics follow format 'Charging_[id]' and are managed by FCMSubscriptionService
  };

  // ==================== NOTIFICATION PREFERENCES ====================

  /// Default notification preferences - Only charging-related preferences
  static const Map<String, NotificationPreference> defaultPreferences = {
    'charging_updates': NotificationPreference(
      key: 'charging_updates',
      displayName: 'Charging Updates',
      description:
          'Get notified about charging session progress and completion',
      defaultEnabled: true,
      category: PreferenceCategory.charging,
      relatedTopics: ['charging_updates'],
      relatedChannels: ['charging_session', 'active_charging_session'],
    ),
  };

  // ==================== NOTIFICATION TYPES ====================

  /// Notification type configurations - Charging and essential types only
  static const Map<String, NotificationTypeConfig> notificationTypes = {
    'charging_started': NotificationTypeConfig(
      type: 'charging_started',
      title: 'Charging Started',
      defaultBody: 'Your EV charging session has begun',
      channel: 'charging_session',
      priority: NotificationPriority.high,
      persistent: true,
      showProgress: false,
    ),
    'charging_progress': NotificationTypeConfig(
      type: 'charging_progress',
      title: 'Charging in Progress',
      defaultBody: 'Your EV is charging - {percentage}% complete',
      channel: 'charging_session',
      priority: NotificationPriority.high,
      persistent: true,
      showProgress: true,
    ),
    'charging_complete': NotificationTypeConfig(
      type: 'charging_complete',
      title: 'Charging Complete',
      defaultBody: 'Your EV is fully charged and ready to go!',
      channel: 'charging_session',
      priority: NotificationPriority.high,
      persistent: false,
      showProgress: false,
    ),
    'welcome_login': NotificationTypeConfig(
      type: 'welcome_login',
      title: 'Welcome to EcoPlug! 😊⚡',
      defaultBody: 'Start your eco-friendly charging journey today',
      channel: 'charging_session',
      priority: NotificationPriority.high,
      persistent: false,
      showProgress: false,
    ),
  };

  // ==================== HELPER METHODS ====================

  /// Get notification channel configuration by ID
  static NotificationChannelConfig? getChannelConfig(String channelId) {
    return channels[channelId];
  }

  /// Get topic configuration by name
  static TopicConfig? getTopicConfig(String topicName) {
    return topics[topicName];
  }

  /// Get notification preference by key
  static NotificationPreference? getPreference(String key) {
    return defaultPreferences[key];
  }

  /// Get notification type configuration
  static NotificationTypeConfig? getNotificationType(String type) {
    return notificationTypes[type];
  }

  /// Get all user-configurable preferences
  static List<NotificationPreference> getUserConfigurablePreferences() {
    return defaultPreferences.values
        .where((pref) => pref.category != PreferenceCategory.system)
        .toList();
  }

  /// Get all default subscribed topics
  static List<String> getDefaultTopics() {
    return topics.values
        .where((topic) => topic.defaultSubscribed)
        .map((topic) => topic.name)
        .toList();
  }

  /// Get topics for a specific preference
  static List<String> getTopicsForPreference(String preferenceKey) {
    final preference = defaultPreferences[preferenceKey];
    return preference?.relatedTopics ?? [];
  }

  /// Get channels for a specific preference
  static List<String> getChannelsForPreference(String preferenceKey) {
    final preference = defaultPreferences[preferenceKey];
    return preference?.relatedChannels ?? [];
  }

  // ==================== ANDROID NOTIFICATION SETTINGS ====================

  /// Android notification icon and visual settings - BRANDING: EcoPlug launcher icon
  static const String defaultIcon = '@drawable/ic_launcher';
  static const String defaultLargeIcon = '@drawable/ic_launcher';
  static const Color defaultColor = Color(0xFF8cc051);
  static const bool defaultColorized = true;
}

// ==================== CONFIGURATION MODELS ====================

/// Notification channel configuration model
class NotificationChannelConfig {
  final String id;
  final String name;
  final String description;
  final Importance importance;
  final bool playSound;
  final bool enableVibration;
  final bool showBadge;
  final bool enableLights;
  final Color ledColor;
  final String groupId;

  const NotificationChannelConfig({
    required this.id,
    required this.name,
    required this.description,
    required this.importance,
    required this.playSound,
    required this.enableVibration,
    required this.showBadge,
    required this.enableLights,
    required this.ledColor,
    required this.groupId,
  });

  /// Convert to AndroidNotificationChannel
  AndroidNotificationChannel toAndroidChannel() {
    return AndroidNotificationChannel(
      id,
      name,
      description: description,
      importance: importance,
      playSound: playSound,
      enableVibration: enableVibration,
      showBadge: showBadge,
      enableLights: enableLights,
      ledColor: ledColor,
      // Remove groupId to avoid "NotificationChannelGroup doesn't exist" error
      // groupId: groupId,
    );
  }
}

/// FCM topic configuration model
class TopicConfig {
  final String name;
  final String displayName;
  final String description;
  final bool defaultSubscribed;
  final bool userConfigurable;

  const TopicConfig({
    required this.name,
    required this.displayName,
    required this.description,
    required this.defaultSubscribed,
    required this.userConfigurable,
  });
}

/// Notification preference configuration model
class NotificationPreference {
  final String key;
  final String displayName;
  final String description;
  final bool defaultEnabled;
  final PreferenceCategory category;
  final List<String> relatedTopics;
  final List<String> relatedChannels;

  const NotificationPreference({
    required this.key,
    required this.displayName,
    required this.description,
    required this.defaultEnabled,
    required this.category,
    required this.relatedTopics,
    required this.relatedChannels,
  });
}

/// Notification type configuration model
class NotificationTypeConfig {
  final String type;
  final String title;
  final String defaultBody;
  final String channel;
  final NotificationPriority priority;
  final bool persistent;
  final bool showProgress;

  const NotificationTypeConfig({
    required this.type,
    required this.title,
    required this.defaultBody,
    required this.channel,
    required this.priority,
    required this.persistent,
    required this.showProgress,
  });
}

/// Preference category enum
enum PreferenceCategory {
  charging,
  stations,
  trips,
  wallet,
  marketing,
  system,
}

/// Notification priority enum
enum NotificationPriority {
  low,
  normal,
  high,
  urgent,
}
