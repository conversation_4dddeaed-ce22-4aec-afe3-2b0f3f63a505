import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ecoplug/screens/charging_session_screen.dart';
import '../services/charging_session_service.dart';
import '../services/charging_parameters_service.dart';
import '../utils/app_themes.dart';

/// Modern animated background with gradient waves
class ModernBackgroundPainter extends CustomPainter {
  final double animationValue;
  final List<Color> gradientColors;

  ModernBackgroundPainter({
    required this.animationValue,
    required this.gradientColors,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Create gradient background
    final gradient = LinearGradient(
      colors: gradientColors,
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );

    final rect = Offset.zero & size;
    final paint = Paint()..shader = gradient.createShader(rect);
    canvas.drawRect(rect, paint);

    // Draw animated wave patterns
    final wavePaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.1)
      ..style = PaintingStyle.fill;

    final path = Path();
    final waveHeight = 50.0;
    final waveLength = size.width;
    final offset = animationValue * waveLength;

    path.moveTo(0, size.height * 0.7);

    for (double x = 0; x <= size.width; x++) {
      final y = size.height * 0.7 +
          math.sin((x + offset) * 2 * math.pi / waveLength) * waveHeight;
      path.lineTo(x, y);
    }

    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    canvas.drawPath(path, wavePaint);

    // Second wave layer
    final path2 = Path();
    path2.moveTo(0, size.height * 0.75);

    for (double x = 0; x <= size.width; x++) {
      final y = size.height * 0.75 +
          math.sin((x - offset * 0.5) * 2 * math.pi / waveLength) *
              waveHeight *
              0.7;
      path2.lineTo(x, y);
    }

    path2.lineTo(size.width, size.height);
    path2.lineTo(0, size.height);
    path2.close();

    canvas.drawPath(
        path2, wavePaint..color = Colors.white.withValues(alpha: 0.05));
  }

  @override
  bool shouldRepaint(covariant ModernBackgroundPainter oldDelegate) {
    return animationValue != oldDelegate.animationValue;
  }
}

/// Progress card widget with animations
class ProgressCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;
  final bool isActive;
  final bool isCompleted;
  final bool isError;
  final double progress;
  final Animation<double> animation;

  const ProgressCard({
    super.key,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.isActive,
    required this.isCompleted,
    required this.isError,
    required this.progress,
    required this.animation,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        final scale = isActive
            ? 1.0 + (animation.value * 0.02)
            : 1.0; // Further reduced scale factor
        final elevation =
            isActive ? 3.0 : 1.0; // Reduced elevation for more compact look

        return Transform.scale(
          scale: scale,
          child: Container(
            margin: const EdgeInsets.symmetric(
                vertical: 4), // Further reduced vertical margin
            child: Material(
              elevation: elevation,
              borderRadius: BorderRadius.circular(14), // Smaller radius
              color: Colors.white,
              shadowColor: isActive ? color.withAlpha(50) : Colors.black12,
              child: InkWell(
                borderRadius: BorderRadius.circular(14),
                onTap: isActive || isCompleted ? null : null,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 12, vertical: 8), // Further reduced padding
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(14),
                    border: Border.all(
                      color:
                          isActive ? color.withAlpha(50) : Colors.transparent,
                      width: 1.0, // Thinner border
                    ),
                  ),
                  child: Row(
                    children: [
                      // Icon container with progress
                      Stack(
                        alignment: Alignment.center,
                        children: [
                          // Progress circle
                          if (isActive)
                            SizedBox(
                              width: 40, // Smaller size
                              height: 40,
                              child: CircularProgressIndicator(
                                value: progress,
                                strokeWidth: 2.0, // Thinner stroke
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(color),
                                backgroundColor: color.withAlpha(20),
                              ),
                            ),
                          // Icon background
                          Container(
                            width: 40, // Smaller size
                            height: 40,
                            decoration: BoxDecoration(
                              color: isError
                                  ? Colors.red.withAlpha(20)
                                  : isActive || isCompleted
                                      ? color.withAlpha(20)
                                      : Colors.grey[100],
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                              child: AnimatedSwitcher(
                                duration: const Duration(milliseconds: 300),
                                child: Icon(
                                  isError
                                      ? Icons.error_outline_rounded
                                      : isCompleted
                                          ? Icons.check_circle_rounded
                                          : icon,
                                  key: ValueKey(
                                      '${title}_${isCompleted}_$isError'),
                                  size: 20, // Smaller icon
                                  color: isError
                                      ? Colors.red
                                      : isActive || isCompleted
                                          ? color
                                          : Colors.grey[400],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(width: 10), // Further reduced spacing
                      // Text content
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize:
                              MainAxisSize.min, // Ensures minimum height
                          children: [
                            Text(
                              title,
                              style: TextStyle(
                                fontSize: 14, // Smaller font
                                fontWeight: isActive
                                    ? FontWeight.w700
                                    : FontWeight.w600,
                                color: isActive || isCompleted
                                    ? Colors.grey[900]
                                    : Colors.grey[500],
                              ),
                              maxLines: 1, // Ensure single line for title
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(
                                height: 1), // Further reduced spacing
                            AnimatedSwitcher(
                              duration: const Duration(milliseconds: 300),
                              child: Text(
                                subtitle,
                                key: ValueKey(subtitle),
                                style: TextStyle(
                                  fontSize:
                                      11, // Even smaller font for subtitle
                                  color: isActive
                                      ? Colors.grey[700]
                                      : Colors.grey[500],
                                  height: 1.1, // Tighter line spacing
                                ),
                                maxLines: 1, // Limit to 1 line
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Status indicator - even more compact
                      if (isActive)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6, // Further reduced padding
                            vertical: 3,
                          ),
                          decoration: BoxDecoration(
                            color: color.withAlpha(20),
                            borderRadius:
                                BorderRadius.circular(8), // Smaller radius
                          ),
                          child: Text(
                            'Active',
                            style: TextStyle(
                              fontSize: 10, // Smaller font
                              fontWeight: FontWeight.w500,
                              color: color,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Professional charging initialization screen
class ChargingInitializationScreen extends StatefulWidget {
  final String? stationUid;
  final String? connectorId;
  // Direct mode removed - only normal mode supported
  final bool
      sessionAlreadyStarted; // NEW: Flag to indicate session was already started

  const ChargingInitializationScreen({
    super.key,
    this.stationUid,
    this.connectorId,
    this.sessionAlreadyStarted =
        false, // NEW: Default to false for backward compatibility
  });

  @override
  State<ChargingInitializationScreen> createState() =>
      _ChargingInitializationScreenState();
}

class _ChargingInitializationScreenState
    extends State<ChargingInitializationScreen> with TickerProviderStateMixin {
  // Animation controllers
  late final AnimationController _bgAnimationController;
  late final AnimationController _mainProgressController;
  late final AnimationController _pulseAnimationController;
  late final List<AnimationController> _cardAnimationControllers;

  // Animations
  late Animation<double> _mainProgressAnimation;
  late Animation<double> _pulseAnimation;
  late List<Animation<double>> _cardAnimations;

  // State
  String _headerText = "Initializing Charging";
  String _subHeaderText = "Setting up your charging session";
  double _overallProgress = 0.0;
  int _currentStep = -1;
  bool _isError = false;
  String? _errorMessage;

  // Charging flow
  final ChargingFlowManager _chargingFlowManager = ChargingFlowManager();
  bool _realChargingStarted = false;

  // CRITICAL: Sequential API flow control variables
  Map<String, dynamic>? _verifiedSessionData;
  bool _hasNavigatedToChargingSession = false;

  // BULLETPROOF: Start Transaction protection
  bool _startTransactionCalled = false;
  String? _extractedTransactionId;

  // Image preloading state for smooth navigation transitions
  bool _isBackgroundImagePreloaded = false;

  // Professional step configuration - 5 stages × 4 seconds each = 20 seconds total
  final List<Map<String, dynamic>> _steps = [
    {
      'title': 'Station Connection',
      'subtitle': 'Establishing secure connection',
      'icon': Icons.wifi_tethering_rounded,
      'color': const Color(0xFF2196F3),
      'duration': 4000, // ✅ 4 seconds
    },
    {
      'title': 'User Authentication',
      'subtitle': 'Verifying your credentials',
      'icon': Icons.verified_user_rounded,
      'color': const Color(0xFF8cc051),
      'duration': 4000, // ✅ 4 seconds
    },
    {
      'title': 'Vehicle Communication',
      'subtitle': 'Syncing with your vehicle',
      'icon': Icons.directions_car_rounded,
      'color': const Color(0xFF9C27B0),
      'duration': 4000, // ✅ 4 seconds
    },
    {
      'title': 'Power Configuration',
      'subtitle': 'Optimizing charging parameters',
      'icon': Icons.power_settings_new_rounded,
      'color': const Color(0xFFFF9800),
      'duration': 4000, // ✅ 4 seconds
    },
    {
      'title': 'Session Activation',
      'subtitle': 'Starting your charging session',
      'icon': Icons.battery_charging_full_rounded,
      'color': const Color(0xFF00BCD4),
      'duration': 4000, // ✅ 4 seconds
    },
  ];

  // Progress tracking for each step
  final List<double> _stepProgress = List.filled(5, 0.0);
  Timer? _progressTimer;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _bgAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 20),
    )..repeat();

    _mainProgressController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _pulseAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat(reverse: true);

    _cardAnimationControllers = List.generate(
      _steps.length,
      (index) => AnimationController(
        vsync: this,
        duration: const Duration(milliseconds: 600),
      ),
    );

    // Initialize animations
    _mainProgressAnimation = CurvedAnimation(
      parent: _mainProgressController,
      curve: Curves.easeInOutCubic,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.95,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseAnimationController,
      curve: Curves.easeInOut,
    ));

    _cardAnimations = _cardAnimationControllers.map((controller) {
      return CurvedAnimation(
        parent: controller,
        curve: Curves.easeOutBack,
      );
    }).toList();

    // Add haptic feedback
    HapticFeedback.lightImpact();

    // Start initialization process
    _startInitializationProcess();
  }

  void _startInitializationProcess() {
    _mainProgressController.forward();
    _advanceToNextStep();
  }

  /// Preload charging session background image for smooth navigation transitions
  Future<void> _preloadChargingSessionBackgroundImage() async {
    if (_isBackgroundImagePreloaded) {
      debugPrint('🖼️ Background image already preloaded - skipping');
      return;
    }

    try {
      debugPrint(
          '🖼️ ===== PRELOADING CHARGING SESSION BACKGROUND IMAGE =====');
      debugPrint('🖼️ Starting image preload for smooth navigation transition');
      debugPrint(
          '🖼️ Image: assets/images/charging_session _screen_background.png');

      // Use precacheImage to preload the background image
      await precacheImage(
        const AssetImage(
            'assets/images/charging_session _screen_background.png'),
        context,
      );

      _isBackgroundImagePreloaded = true;
      debugPrint('✅ Charging session background image preloaded successfully');
      debugPrint(
          '✅ Navigation transition will be smooth without image loading delay');
    } catch (e) {
      debugPrint('⚠️ Error preloading background image: $e');
      debugPrint(
          '⚠️ Navigation will still work but may have slight loading delay');
      // Don't throw error - this is an optimization, not critical functionality
    }
  }

  void _advanceToNextStep() async {
    if (_currentStep >= _steps.length - 1) {
      // All steps completed
      await Future.delayed(const Duration(milliseconds: 500));
      _completeChargingProcess();
      return;
    }

    // Advance to next step
    setState(() {
      _currentStep++;
      _updateHeaderText();
    });

    // Animate current step card
    if (_currentStep < _cardAnimationControllers.length) {
      _cardAnimationControllers[_currentStep].forward();
    }

    // Start progress animation for current step
    _animateStepProgress(_currentStep);

    // Auto-advance after step duration
    final stepDuration = _steps[_currentStep]['duration'] as int;
    await Future.delayed(Duration(milliseconds: stepDuration));

    if (mounted && !_isError) {
      _advanceToNextStep();
    }
  }

  void _animateStepProgress(int stepIndex) {
    _progressTimer?.cancel();

    final stepDuration = _steps[stepIndex]['duration'] as int;
    final updateInterval = 50; // Update every 50ms
    final totalUpdates = stepDuration ~/ updateInterval;
    int currentUpdate = 0;

    _progressTimer = Timer.periodic(
      Duration(milliseconds: updateInterval),
      (timer) {
        if (!mounted) {
          timer.cancel();
          return;
        }

        currentUpdate++;
        final progress = currentUpdate / totalUpdates;

        setState(() {
          _stepProgress[stepIndex] = progress.clamp(0.0, 1.0);
          _updateOverallProgress();
        });

        if (currentUpdate >= totalUpdates) {
          timer.cancel();
        }
      },
    );
  }

  void _updateOverallProgress() {
    double totalProgress = 0;
    for (int i = 0; i <= _currentStep && i < _steps.length; i++) {
      if (i < _currentStep) {
        totalProgress += 1.0;
      } else {
        totalProgress += _stepProgress[i];
      }
    }

    _overallProgress = totalProgress / _steps.length;
    _mainProgressController.animateTo(_overallProgress);
  }

  void _updateHeaderText() {
    if (_currentStep >= 0 && _currentStep < _steps.length) {
      setState(() {
        final step = _steps[_currentStep];
        _headerText = step['title'];
        _subHeaderText = step['subtitle'];
      });
    }
  }

  void _completeChargingProcess() {
    if (!mounted || widget.stationUid == null || widget.connectorId == null) {
      _handleInvalidConfiguration();
      return;
    }

    // Add success haptic
    HapticFeedback.mediumImpact();

    debugPrint('🔌 NORMAL MODE: Starting real charging');
    _startRealChargingFlow();
  }

  // Direct mode navigation method removed

  void _startRealChargingFlow() async {
    // 🚨 CRITICAL FIX: Check if session was already started in ChargingOptionsPage
    if (widget.sessionAlreadyStarted) {
      debugPrint('🛑 ===== SESSION ALREADY STARTED IN CHARGING OPTIONS =====');
      debugPrint('🛑 Session was started in ChargingOptionsPage');
      debugPrint('🛑 Skipping duplicate startChargingSession call');
      debugPrint('🛑 Going directly to Step 2: Session Verification');
      _startSessionVerificationOnly();
      return;
    }

    // BULLETPROOF: Prevent ANY duplicate Start Transaction API calls
    if (_startTransactionCalled) {
      debugPrint('🛑 ===== START TRANSACTION ALREADY CALLED =====');
      debugPrint('🛑 Start Transaction API was already executed');
      debugPrint('🛑 Transaction ID: $_extractedTransactionId');
      debugPrint('🛑 Preventing duplicate API call - BLOCKED');
      return;
    }

    if (_realChargingStarted) return;

    // BULLETPROOF: Mark Start Transaction as called BEFORE making the call
    _startTransactionCalled = true;
    debugPrint('🔒 ===== START TRANSACTION PROTECTION ACTIVATED =====');
    debugPrint('🔒 Start Transaction flag set to TRUE');
    debugPrint('🔒 This ensures Start Transaction API is called ONLY ONCE');

    setState(() {
      _realChargingStarted = true;
      _headerText = 'Finalizing Setup';
      _subHeaderText = 'Almost ready to charge';
    });

    // OPTIMIZATION: Preload charging session background image for smooth navigation
    _preloadChargingSessionBackgroundImage();

    try {
      // ENHANCED: Comprehensive null safety and error handling
      debugPrint('🔍 ===== STARTING REAL CHARGING FLOW =====');
      debugPrint('🔍 Station UID: ${widget.stationUid}');
      debugPrint('🔍 Connector ID: ${widget.connectorId}');
      debugPrint('🔍 Mode: Normal (Direct mode removed)');

      // Validate required parameters
      if (widget.stationUid == null || widget.stationUid!.isEmpty) {
        throw Exception('Invalid station UID: ${widget.stationUid}');
      }
      if (widget.connectorId == null || widget.connectorId!.isEmpty) {
        throw Exception('Invalid connector ID: ${widget.connectorId}');
      }

      final chargingParamsService = ChargingParametersService();
      final authenticChargingValue =
          chargingParamsService.hasAuthenticParameters()
              ? chargingParamsService.getChargingValue()
              : 20.0;
      final authenticChargeType = chargingParamsService.hasAuthenticParameters()
          ? chargingParamsService.getChargeType()
          : 'units';
      final authenticPricePerUnit =
          chargingParamsService.hasAuthenticParameters()
              ? chargingParamsService.getPricePerUnit()
              : 0.25;

      // CRITICAL FIX: Get instant charging flag from stored parameters
      final authenticInstantCharging =
          chargingParamsService.hasAuthenticParameters()
              ? chargingParamsService.getInstantCharging()
              : false;

      // CRITICAL FIX: Get wallet balance from stored parameters for instant charging
      final authenticWalletBalance =
          chargingParamsService.hasAuthenticParameters()
              ? chargingParamsService.getWalletBalance()
              : 100.0;

      debugPrint('🔍 Charging Parameters:');
      debugPrint('🔍 - Value: $authenticChargingValue');
      debugPrint('🔍 - Type: $authenticChargeType');
      debugPrint('🔍 - Price per unit: $authenticPricePerUnit');
      debugPrint('🔍 - Instant Charging: $authenticInstantCharging');
      debugPrint('🔍 - Wallet Balance: $authenticWalletBalance');

      await _chargingFlowManager.startCompleteChargingFlow(
        evseUid: widget.stationUid!,
        connectorId: widget.connectorId!,
        chargingValue: authenticChargingValue,
        instantCharging:
            authenticInstantCharging, // FIXED: Use authentic instant charging flag
        chargeType: authenticChargeType,
        walletBalance:
            authenticWalletBalance, // FIXED: Use authentic wallet balance
        pricePerUnit: authenticPricePerUnit,
        onDataReceived: (Map<String, dynamic> data) {
          debugPrint('📊 ===== STEP 3: REAL-TIME DATA RECEIVED =====');
          debugPrint('📊 Real-time data: $data');

          // CRITICAL: Navigation already happened after Step 2 completion
          // This callback is now only for logging/debugging purposes
          if (_hasNavigatedToChargingSession) {
            debugPrint(
                '✅ Navigation already completed after Step 2 - data received in charging session screen');
          } else {
            debugPrint(
                '⚠️ Unexpected: Real-time data received but navigation not completed');
          }
        },
        onError: (String error) {
          debugPrint('❌ Charging flow error: $error');
          if (mounted) {
            _handleChargingError(error);
          }
        },
        onSessionComplete: () {
          debugPrint('🏁 Session completed');
          if (mounted) {
            setState(() {
              _headerText = 'Session Complete';
              _subHeaderText = 'Charging finished successfully';
            });
          }
        },
        onSessionVerified: (Map<String, dynamic> sessionData) {
          debugPrint('✅ ===== STEP 2 COMPLETED: SESSION VERIFIED =====');
          debugPrint('✅ Session verification data: $sessionData');
          debugPrint('✅ Authorization reference extracted successfully');

          // CRITICAL: Store session data for immediate navigation
          _verifiedSessionData = sessionData;
          debugPrint('✅ Session data stored: $_verifiedSessionData');

          // BULLETPROOF: Extract and store transaction ID for verification
          _extractedTransactionId = sessionData['id']?.toString();
          debugPrint(
              '🔒 Transaction ID extracted and stored: $_extractedTransactionId');

          // CRITICAL: Validate authorization reference extraction
          final authorizationReference =
              sessionData['authorization_reference']?.toString();
          debugPrint(
              '🔍 Extracted authorization reference: "$authorizationReference"');

          if (authorizationReference == null ||
              authorizationReference.isEmpty) {
            debugPrint(
                '❌ CRITICAL ERROR: Authorization reference not found in session data');
            debugPrint('❌ Available fields: ${sessionData.keys.toList()}');
            if (mounted) {
              _handleChargingError('Authorization reference extraction failed');
            }
            return;
          }

          if (mounted && !_hasNavigatedToChargingSession) {
            _hasNavigatedToChargingSession = true;

            setState(() {
              _headerText = 'Session Verified';
              _subHeaderText = 'Navigating to charging screen';
              _overallProgress = 1.0; // 100% complete after Step 2
            });

            debugPrint('🚀 ===== IMMEDIATE NAVIGATION AFTER STEP 2 =====');
            debugPrint(
                '🚀 Authorization reference validated: "$authorizationReference"');
            debugPrint('🚀 Navigating to charging session screen with zero delay');
            debugPrint('🧹 Closing Step 2 verification processes completely');

            // BULLETPROOF: Stop ChargingFlowManager IMMEDIATELY to prevent Step 3
            debugPrint(
                '🛑 ===== STOPPING CHARGINGFLOWMANAGER BEFORE NAVIGATION =====');
            _chargingFlowManager.stopChargingDataPolling();
            _chargingFlowManager.dispose();
            debugPrint('🛑 ChargingFlowManager stopped and disposed');
            debugPrint('🛑 NO Step 3 will execute from initialization screen');

            // IMMEDIATE navigation after Step 2 completion (no delay)
            if (mounted) {
              _navigateToChargingSessionWithStep2Cleanup();
            }
          } else if (_hasNavigatedToChargingSession) {
            debugPrint(
                '⚠️ Navigation already completed - ignoring duplicate Step 2 callback');
          }
        },
      );
    } catch (e, stackTrace) {
      debugPrint('❌ CRITICAL ERROR in _startRealChargingFlow: $e');
      debugPrint('❌ Stack trace: $stackTrace');
      if (mounted) {
        _handleChargingError('Failed to start charging: ${e.toString()}');
      }
    }
  }

  /// 🚨 NEW: Start session verification only (skip Step 1 - startChargingSession)
  /// This method is called when the session was already started in ChargingOptionsPage
  void _startSessionVerificationOnly() async {
    debugPrint('🔍 ===== STARTING SESSION VERIFICATION ONLY =====');
    debugPrint('🔍 Skipping Step 1 (startChargingSession) - already done');
    debugPrint('🔍 Going directly to Step 2 (session verification)');

    setState(() {
      _realChargingStarted = true;
      _headerText = 'Verifying Session';
      _subHeaderText = 'Checking your active charging session';
    });

    // OPTIMIZATION: Preload charging session background image for smooth navigation
    _preloadChargingSessionBackgroundImage();

    try {
      // Get charging parameters from the global service
      final chargingParamsService = ChargingParametersService();
      final authenticChargingValue =
          chargingParamsService.hasAuthenticParameters()
              ? chargingParamsService.getChargingValue()
              : 20.0;
      final authenticChargeType = chargingParamsService.hasAuthenticParameters()
          ? chargingParamsService.getChargeType()
          : 'units';
      final authenticPricePerUnit =
          chargingParamsService.hasAuthenticParameters()
              ? chargingParamsService.getPricePerUnit()
              : 0.25;

      // CRITICAL FIX: Get instant charging flag from stored parameters
      final authenticInstantCharging =
          chargingParamsService.hasAuthenticParameters()
              ? chargingParamsService.getInstantCharging()
              : false;

      // CRITICAL FIX: Get wallet balance from stored parameters for instant charging
      final authenticWalletBalance =
          chargingParamsService.hasAuthenticParameters()
              ? chargingParamsService.getWalletBalance()
              : 100.0;

      debugPrint('🔍 Using stored charging parameters:');
      debugPrint('🔍 - Value: $authenticChargingValue');
      debugPrint('🔍 - Type: $authenticChargeType');
      debugPrint('🔍 - Price per unit: $authenticPricePerUnit');
      debugPrint('🔍 - Instant Charging: $authenticInstantCharging');
      debugPrint('🔍 - Wallet Balance: $authenticWalletBalance');

      // Start ONLY Step 2 and Step 3 (skip Step 1)
      await _chargingFlowManager.startSessionVerificationAndPolling(
        evseUid: widget.stationUid!,
        connectorId: widget.connectorId!,
        chargingValue: authenticChargingValue,
        instantCharging:
            authenticInstantCharging, // FIXED: Use authentic instant charging flag
        chargeType: authenticChargeType,
        walletBalance:
            authenticWalletBalance, // FIXED: Use authentic wallet balance
        pricePerUnit: authenticPricePerUnit,
        onDataReceived: (Map<String, dynamic> data) {
          debugPrint('📊 ===== STEP 3: REAL-TIME DATA RECEIVED =====');
          debugPrint('📊 Real-time data: $data');

          if (_hasNavigatedToChargingSession) {
            debugPrint(
                '✅ Navigation already completed after Step 2 - data received in charging session screen');
          } else {
            debugPrint(
                '⚠️ Unexpected: Real-time data received but navigation not completed');
          }
        },
        onError: (String error) {
          debugPrint('❌ Session verification error: $error');
          if (mounted) {
            _handleChargingError(error);
          }
        },
        onSessionComplete: () {
          debugPrint('🏁 Session completed');
          if (mounted) {
            setState(() {
              _headerText = 'Session Complete';
              _subHeaderText = 'Charging finished successfully';
            });
          }
        },
        onSessionVerified: (Map<String, dynamic> sessionData) {
          debugPrint('✅ ===== STEP 2 COMPLETED: SESSION VERIFIED =====');
          debugPrint('✅ Session verification data: $sessionData');
          debugPrint('✅ Authorization reference extracted successfully');

          // Store session data for immediate navigation
          _verifiedSessionData = sessionData;
          debugPrint('✅ Session data stored: $_verifiedSessionData');

          // Extract and store transaction ID for verification
          _extractedTransactionId = sessionData['id']?.toString();
          debugPrint(
              '🔒 Transaction ID extracted and stored: $_extractedTransactionId');

          // Validate authorization reference extraction
          final authorizationReference =
              sessionData['authorization_reference']?.toString();
          debugPrint(
              '🔍 Extracted authorization reference: "$authorizationReference"');

          if (authorizationReference == null ||
              authorizationReference.isEmpty) {
            debugPrint(
                '❌ CRITICAL ERROR: Authorization reference not found in session data');
            debugPrint('❌ Available fields: ${sessionData.keys.toList()}');
            if (mounted) {
              _handleChargingError('Authorization reference extraction failed');
            }
            return;
          }

          if (mounted && !_hasNavigatedToChargingSession) {
            _hasNavigatedToChargingSession = true;

            setState(() {
              _headerText = 'Session Verified';
              _subHeaderText = 'Navigating to charging screen';
              _overallProgress = 1.0; // 100% complete after Step 2
            });

            debugPrint('🚀 ===== IMMEDIATE NAVIGATION AFTER STEP 2 =====');
            debugPrint(
                '🚀 Authorization reference validated: "$authorizationReference"');
            debugPrint('🚀 Navigating to charging session screen with zero delay');

            // IMMEDIATE navigation after Step 2 completion (no delay)
            if (mounted) {
              _navigateToChargingSessionWithStep2Cleanup();
            }
          } else if (_hasNavigatedToChargingSession) {
            debugPrint(
                '⚠️ Navigation already completed - ignoring duplicate Step 2 callback');
          }
        },
      );
    } catch (e, stackTrace) {
      debugPrint('❌ CRITICAL ERROR in _startSessionVerificationOnly: $e');
      debugPrint('❌ Stack trace: $stackTrace');
      if (mounted) {
        _handleChargingError('Failed to verify session: ${e.toString()}');
      }
    }
  }

  /// CRITICAL: Navigation method with Step 2 cleanup for immediate navigation after verification
  void _navigateToChargingSessionWithStep2Cleanup() {
    if (!mounted) return;

    debugPrint('🧹 ===== STEP 2 CLEANUP AND IMMEDIATE NAVIGATION =====');

    // STEP 1: Cancel any remaining timers/intervals from Step 2 verification
    _progressTimer?.cancel();
    debugPrint('✅ Progress timer cancelled');

    // STEP 2: Stop any background processes from Step 2
    for (final controller in _cardAnimationControllers) {
      controller.stop();
    }
    debugPrint('✅ Animation controllers stopped');

    // STEP 3: COMPLETELY STOP ChargingFlowManager to prevent ANY further API calls
    _chargingFlowManager.stopChargingDataPolling();
    _chargingFlowManager.dispose();
    debugPrint('🛑 ===== CHARGINGFLOWMANAGER COMPLETELY STOPPED =====');
    debugPrint('🛑 All polling stopped');
    debugPrint('🛑 ChargingFlowManager disposed');
    debugPrint('🛑 NO MORE API CALLS from initialization screen');
    debugPrint('🛑 Step 3 will be handled by charging session screen');

    // STEP 4: Ensure session data is available with authorization reference
    debugPrint('🔍 Session data check: $_verifiedSessionData');

    if (_verifiedSessionData == null) {
      debugPrint(
          '❌ CRITICAL ERROR: No verified session data available for navigation');
      if (mounted) {
        _handleChargingError('Session data not available');
      }
      return;
    }

    final authorizationReference =
        _verifiedSessionData!['authorization_reference']?.toString();
    if (authorizationReference == null || authorizationReference.isEmpty) {
      debugPrint(
          '❌ CRITICAL ERROR: Authorization reference not available in session data');
      if (mounted) {
        _handleChargingError('Authorization reference not found');
      }
      return;
    }

    debugPrint('🚀 ===== NAVIGATING TO CHARGING SESSION SCREEN =====');
    debugPrint('🚀 Station UID: ${widget.stationUid}');
    debugPrint('🚀 Connector ID: ${widget.connectorId}');
    debugPrint('🚀 Authorization Reference: $authorizationReference');
    debugPrint('🚀 Session Data: $_verifiedSessionData');

    Navigator.pushReplacement(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            ChargingSessionScreen(
          stationUid: widget.stationUid ?? 'unknown',
          connectorId: widget.connectorId ?? 'unknown',
          initialCharge: 0.3,
          // Direct mode removed - only normal mode supported
          verifiedSessionData:
              _verifiedSessionData, // CRITICAL: Pass complete session data
        ),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 500),
      ),
    );
  }

  void _handleChargingError(String error) {
    if (!mounted) return;

    // ENHANCED: Comprehensive error handling and logging
    debugPrint('🔥 ===== CHARGING ERROR HANDLER =====');
    debugPrint('🔥 Error message: $error');
    debugPrint('🔥 Current step: $_currentStep');
    debugPrint('🔥 Real charging started: $_realChargingStarted');
    debugPrint('🔥 Widget mounted: $mounted');
    debugPrint('🔥 Station UID: ${widget.stationUid}');
    debugPrint('🔥 Connector ID: ${widget.connectorId}');
    debugPrint('🔥 Mode: Normal (Direct mode removed)');

    // Error haptic
    HapticFeedback.heavyImpact();

    setState(() {
      _isError = true;
      _headerText = 'Connection Failed';
      _subHeaderText = error.length > 50 ? 'Please try again' : error;
      _errorMessage = error;

      // Mark current step as error
      if (_currentStep >= 0 && _currentStep < _steps.length) {
        _stepProgress[_currentStep] = 0.0;
      }
    });

    // Show error details in debug mode
    debugPrint('🔥 Error state updated - showing error for 3 seconds');

    Future.delayed(const Duration(seconds: 3), () {
      debugPrint('🔥 Error timeout - attempting to pop navigation');
      if (mounted) {
        Navigator.of(context).pop();
      }
    });
  }

  void _handleInvalidConfiguration() {
    if (!mounted) return;

    setState(() {
      _isError = true;
      _headerText = 'Configuration Error';
      _subHeaderText = 'Invalid station or connector';
    });

    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) Navigator.of(context).pop();
    });
  }

  @override
  void dispose() {
    debugPrint('🧹 ===== DISPOSING CHARGING INITIALIZATION SCREEN =====');

    // CRITICAL: Stop all charging flow processes (if not already disposed)
    try {
      _chargingFlowManager.dispose();
      debugPrint('✅ Charging flow manager disposed');
    } catch (e) {
      debugPrint('ℹ️ ChargingFlowManager already disposed: $e');
    }

    // Cancel all timers and animations
    _progressTimer?.cancel();
    debugPrint('✅ Progress timer cancelled');

    _bgAnimationController.dispose();
    _mainProgressController.dispose();
    _pulseAnimationController.dispose();

    for (final controller in _cardAnimationControllers) {
      controller.dispose();
    }
    debugPrint('✅ All animation controllers disposed');

    super.dispose();
    debugPrint('✅ Charging initialization screen disposed completely');
  }

  @override
  Widget build(BuildContext context) {
    // Modern gradient colors
    final gradientColors = _isError
        ? [const Color(0xFFFF5252), const Color(0xFFFF1744)]
        : [const Color(0xFF00BCD4), const Color(0xFF00ACC1)];

    return Scaffold(
      backgroundColor: Colors.white,
      body: AnimatedBuilder(
        animation: _bgAnimationController,
        builder: (context, child) {
          return CustomPaint(
            painter: ModernBackgroundPainter(
              animationValue: _bgAnimationController.value,
              gradientColors: gradientColors,
            ),
            child: child,
          );
        },
        child: SafeArea(
          child: Column(
            children: [
              // Header section
              Container(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    // Back button
                    Material(
                      color: Colors.white.withOpacity(0.9),
                      borderRadius: BorderRadius.circular(12),
                      child: InkWell(
                        onTap: () => Navigator.of(context).pop(),
                        borderRadius: BorderRadius.circular(12),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          child: const Icon(
                            Icons.arrow_back_rounded,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    const Spacer(),
                    // Status indicator
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.9),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: _isError ? Colors.red : AppThemes.primaryColor,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _isError ? 'Error' : 'Processing',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Main content
              Expanded(
                child: Container(
                  margin: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.95),
                    borderRadius: BorderRadius.circular(30),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(30),
                    child: Column(
                      children: [
                        // Header section with icon
                        Container(
                          padding: const EdgeInsets.symmetric(
                              vertical: 16, horizontal: 20), // Reduced padding
                          child: Column(
                            children: [
                              // Animated icon
                              AnimatedBuilder(
                                animation: _pulseAnimation,
                                builder: (context, child) {
                                  return Transform.scale(
                                    scale: _pulseAnimation.value,
                                    child: Container(
                                      width: 70, // Smaller icon size
                                      height: 70,
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          colors: _isError
                                              ? [
                                                  Colors.red[400]!,
                                                  Colors.red[600]!
                                                ]
                                              : [
                                                  const Color(0xFF00BCD4),
                                                  const Color(0xFF00ACC1)
                                                ],
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                        ),
                                        shape: BoxShape.circle,
                                        boxShadow: [
                                          BoxShadow(
                                            color: (_isError
                                                    ? Colors.red
                                                    : const Color(0xFF00BCD4))
                                                .withOpacity(
                                                    0.25), // Reduced opacity
                                            blurRadius: 15, // Reduced blur
                                            spreadRadius: 3, // Reduced spread
                                          ),
                                        ],
                                      ),
                                      child: Center(
                                        child: Icon(
                                          _isError
                                              ? Icons.error_outline_rounded
                                              : Icons.ev_station_rounded,
                                          size: 35, // Smaller icon
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                              const SizedBox(
                                  height:
                                      10), // Further reduced spacing for more card space
                              // Title
                              AnimatedSwitcher(
                                duration: const Duration(milliseconds: 400),
                                child: Text(
                                  _headerText,
                                  key: ValueKey(_headerText),
                                  style: const TextStyle(
                                    fontSize: 22, // Slightly smaller text
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              const SizedBox(
                                  height:
                                      4), // Further reduced spacing for more card space
                              // Subtitle
                              AnimatedSwitcher(
                                duration: const Duration(milliseconds: 400),
                                child: Text(
                                  _subHeaderText,
                                  key: ValueKey(_subHeaderText),
                                  style: TextStyle(
                                    fontSize: 14, // Smaller text
                                    color: Colors.grey[600],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Progress bar
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 24),
                          height: 4, // Thinner progress bar
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            borderRadius:
                                BorderRadius.circular(2), // Smaller radius
                          ),
                          child: AnimatedBuilder(
                            animation: _mainProgressAnimation,
                            builder: (context, child) {
                              return FractionallySizedBox(
                                alignment: Alignment.centerLeft,
                                widthFactor: _mainProgressAnimation.value,
                                child: Container(
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: _isError
                                          ? [Colors.red[400]!, Colors.red[600]!]
                                          : [
                                              const Color(0xFF00BCD4),
                                              const Color(0xFF00ACC1)
                                            ],
                                    ),
                                    borderRadius: BorderRadius.circular(3),
                                    boxShadow: [
                                      BoxShadow(
                                        color: (_isError
                                                ? Colors.red
                                                : const Color(0xFF00BCD4))
                                            .withOpacity(
                                                0.25), // Reduced opacity
                                        blurRadius: 6, // Reduced blur
                                        offset: const Offset(
                                            0, 1), // Reduced offset
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                        ),

                        const SizedBox(
                            height:
                                12), // Further reduced spacing for more card space

                        // Progress cards
                        Expanded(
                          child: ListView.builder(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16), // Reduced padding
                            itemCount: _steps.length,
                            itemBuilder: (context, index) {
                              final step = _steps[index];
                              final isActive = index == _currentStep;
                              final isCompleted = index < _currentStep;
                              final isError = _isError && index == _currentStep;

                              return ProgressCard(
                                title: step['title'],
                                subtitle: step['subtitle'],
                                icon: step['icon'],
                                color: step['color'],
                                isActive: isActive,
                                isCompleted: isCompleted,
                                isError: isError,
                                progress: _stepProgress[index],
                                animation: _cardAnimations[index],
                              );
                            },
                          ),
                        ),

                        // Bottom section
                        Container(
                          padding: const EdgeInsets.symmetric(
                              vertical: 8,
                              horizontal:
                                  16), // Further reduced padding for more card space
                          child: Text(
                            'Please wait while we prepare your charging session',
                            style: TextStyle(
                              fontSize: 12, // Smaller text
                              color: Colors.grey[600],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
