import 'package:flutter/material.dart';
import '../services/fcm_subscription_service.dart';
import '../widgets/fcm_subscription_debug_widget.dart';

/// Test page for FCM subscription functionality
/// Provides a UI to test FCM subscription and unsubscription
class FCMSubscriptionTestPage extends StatefulWidget {
  const FCMSubscriptionTestPage({super.key});

  @override
  State<FCMSubscriptionTestPage> createState() => _FCMSubscriptionTestPageState();
}

class _FCMSubscriptionTestPageState extends State<FCMSubscriptionTestPage> {
  final FCMSubscriptionService _subscriptionService = FCMSubscriptionService();
  final TextEditingController _transactionIdController = TextEditingController();
  
  bool _isLoading = false;
  String? _lastResult;

  @override
  void initState() {
    super.initState();
    _initializeService();
  }

  Future<void> _initializeService() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _subscriptionService.initialize();
      setState(() {
        _lastResult = 'FCM Subscription Service initialized successfully';
      });
    } catch (e) {
      setState(() {
        _lastResult = 'Error initializing service: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testSubscription() async {
    final transactionId = _transactionIdController.text.trim();
    if (transactionId.isEmpty) {
      setState(() {
        _lastResult = 'Please enter a transaction ID';
      });
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await _subscriptionService.subscribeToChargingNotifications(transactionId);
      setState(() {
        _lastResult = success 
            ? 'Successfully subscribed to charging notifications for transaction: $transactionId'
            : 'Failed to subscribe to charging notifications';
      });
    } catch (e) {
      setState(() {
        _lastResult = 'Error during subscription: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testUnsubscription() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final success = await _subscriptionService.unsubscribeFromChargingNotifications();
      setState(() {
        _lastResult = success 
            ? 'Successfully unsubscribed from charging notifications'
            : 'Failed to unsubscribe from charging notifications';
      });
    } catch (e) {
      setState(() {
        _lastResult = 'Error during unsubscription: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _runFullTest() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final testResults = await _subscriptionService.testSubscription();
      setState(() {
        _lastResult = 'Test completed. Results: $testResults';
      });
    } catch (e) {
      setState(() {
        _lastResult = 'Error during test: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('FCM Subscription Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Service Status
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Service Status',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('FCM Token: ${_subscriptionService.fcmToken?.substring(0, 20) ?? 'Not available'}...'),
                    Text('Is Subscribed: ${_subscriptionService.isSubscribed}'),
                    Text('Current Transaction: ${_subscriptionService.currentTransactionId ?? 'None'}'),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Transaction ID Input
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Manual Subscription Test',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _transactionIdController,
                      decoration: const InputDecoration(
                        labelText: 'Transaction ID',
                        hintText: 'Enter transaction ID for testing',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _testSubscription,
                            child: const Text('Subscribe'),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _testUnsubscription,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange,
                            ),
                            child: const Text('Unsubscribe'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Automated Test
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Automated Test',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'This will run a complete subscription/unsubscription test cycle',
                      style: TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _runFullTest,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                        ),
                        child: const Text('Run Full Test'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Loading Indicator
            if (_isLoading)
              const Card(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Row(
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(width: 16),
                      Text('Processing...'),
                    ],
                  ),
                ),
              ),
            
            // Last Result
            if (_lastResult != null)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Last Result',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _lastResult!,
                        style: TextStyle(
                          color: _lastResult!.contains('Error') || _lastResult!.contains('Failed')
                              ? Colors.red
                              : Colors.green,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            
            const SizedBox(height: 16),
            
            // Debug Widget
            const FCMSubscriptionDebugWidget(),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _transactionIdController.dispose();
    super.dispose();
  }
}
