import 'package:flutter/material.dart';
import '../services/api_service.dart';
import '../utils/app_theme.dart';

class ApiTestScreen extends StatefulWidget {
  const ApiTestScreen({super.key});

  @override
  ApiTestScreenState createState() => ApiTestScreenState();
}

class ApiTestScreenState extends State<ApiTestScreen> {
  final ApiService _apiService = ApiService();

  // State variables
  bool _isLoading = false;
  String _responseText = '';
  String _errorText = '';

  // Test location ID for reviews and bookmarks
  final String _testLocationId = 'a81f3b85-a4bf-4dc6-98d0-b92fa7de0a13';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('API Test'),
        backgroundColor: AppTheme.primaryColor,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // API Test Buttons
            const Text(
              'API Tests',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Profile API
            ElevatedButton(
              onPressed: _isLoading ? null : _testProfileApi,
              style: AppTheme.primaryButtonStyle,
              child: const Text('Test Profile API'),
            ),
            const SizedBox(height: 8),

            // Vehicles API
            ElevatedButton(
              onPressed: _isLoading ? null : _testVehiclesApi,
              style: AppTheme.primaryButtonStyle,
              child: const Text('Test Vehicles API'),
            ),
            const SizedBox(height: 8),

            // Reviews API
            ElevatedButton(
              onPressed: _isLoading ? null : _testReviewsApi,
              style: AppTheme.primaryButtonStyle,
              child: const Text('Test Reviews API'),
            ),
            const SizedBox(height: 8),

            // Bookmarks API
            ElevatedButton(
              onPressed: _isLoading ? null : _testBookmarksApi,
              style: AppTheme.primaryButtonStyle,
              child: const Text('Test Bookmarks API'),
            ),
            const SizedBox(height: 16),

            // Loading indicator
            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(),
              ),

            // Error text
            if (_errorText.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withAlpha(30),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red),
                ),
                child: Text(
                  _errorText,
                  style: const TextStyle(color: Colors.red),
                ),
              ),

            // Response text
            if (_responseText.isNotEmpty)
              Container(
                margin: const EdgeInsets.only(top: 16),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey.withAlpha(30),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Response:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    SelectableText(_responseText),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  // Test Profile API
  Future<void> _testProfileApi() async {
    setState(() {
      _isLoading = true;
      _responseText = '';
      _errorText = '';
    });

    try {
      final response = await _apiService.getUserProfile();

      setState(() {
        _responseText = 'Profile API Response:\n'
            'Success: ${response.success}\n'
            'Message: ${response.message}\n'
            'User ID: ${response.data.id}\n'
            'Name: ${response.data.name}\n'
            'Email: ${response.data.email}\n'
            'Mobile: ${response.data.mobileNumber}\n'
            'Balance: ${response.data.balance.balance}\n'
            'RFID Codes: ${response.data.rfidCode.join(', ')}';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorText = 'Error: $e';
        _isLoading = false;
      });
    }
  }

  // Test Vehicles API
  Future<void> _testVehiclesApi() async {
    setState(() {
      _isLoading = true;
      _responseText = '';
      _errorText = '';
    });

    try {
      final response = await _apiService.getVehicles();

      setState(() {
        _responseText = 'Vehicles API Response:\n'
            'Success: ${response.success}\n'
            'Total Vehicles: ${response.allVehicles.length}\n'
            'Brands: ${response.brands.join(', ')}\n\n'
            'First Vehicle:\n'
            'ID: ${response.allVehicles.isNotEmpty ? response.allVehicles[0].id : 'N/A'}\n'
            'Name: ${response.allVehicles.isNotEmpty ? response.allVehicles[0].name : 'N/A'}\n'
            'Battery: ${response.allVehicles.isNotEmpty ? response.allVehicles[0].batteryCapacity : 'N/A'}';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorText = 'Error: $e';
        _isLoading = false;
      });
    }
  }

  // Test Reviews API
  Future<void> _testReviewsApi() async {
    setState(() {
      _isLoading = true;
      _responseText = '';
      _errorText = '';
    });

    try {
      final response = await _apiService.getReviews(_testLocationId);

      setState(() {
        _responseText = 'Reviews API Response:\n'
            'Success: ${response.success}\n'
            'Total Reviews: ${response.reviews.length}\n\n'
            'Reviews: ${response.reviews.map((r) => '${r.comment} (${r.rate})').join(', ')}';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorText = 'Error: $e';
        _isLoading = false;
      });
    }
  }

  // Test Bookmarks API
  Future<void> _testBookmarksApi() async {
    setState(() {
      _isLoading = true;
      _responseText = '';
      _errorText = '';
    });

    try {
      final response = await _apiService.getBookmarks();

      setState(() {
        _responseText = 'Bookmarks API Response:\n'
            'Success: ${response.success}\n'
            'Message: ${response.message}\n'
            'Total Bookmarks: ${response.data.length}\n\n'
            'Bookmarks: ${response.data.map((b) => b.locationUid).join(', ')}';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorText = 'Error: $e';
        _isLoading = false;
      });
    }
  }
}
