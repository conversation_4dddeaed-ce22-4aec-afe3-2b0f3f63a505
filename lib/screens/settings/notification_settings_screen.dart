import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ecoplug/providers/notification_provider.dart';
import 'package:ecoplug/config/notification_config.dart';
import 'package:ecoplug/debug/fcm_token_console_logger.dart';
import 'package:ecoplug/debug/welcome_notification_debugger.dart';

/// Notification Settings Screen
/// Allows users to manage their notification preferences
class NotificationSettingsScreen extends ConsumerStatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  ConsumerState<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends ConsumerState<NotificationSettingsScreen> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final notificationState = ref.watch(notificationProvider);
    final notificationNotifier = ref.watch(notificationProvider.notifier);
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Settings'),
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Service Status Card
                  _buildServiceStatusCard(notificationState, theme),
                  const SizedBox(height: 20),

                  // FCM Token Card (Debug mode only)
                  if (notificationState.fcmToken != null)
                    _buildFCMTokenCard(notificationState, theme),
                  const SizedBox(height: 20),

                  // Notification Preferences
                  _buildPreferencesSection(notificationState, notificationNotifier, theme),
                  const SizedBox(height: 20),

                  // Test Notification Button (Debug mode only)
                  _buildTestSection(notificationNotifier, theme),
                ],
              ),
            ),
    );
  }

  Widget _buildServiceStatusCard(NotificationState state, ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Service Status',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...state.serviceStatus.entries.map((entry) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _formatServiceName(entry.key),
                      style: theme.textTheme.bodyMedium,
                    ),
                    Icon(
                      entry.value ? Icons.check_circle : Icons.error,
                      color: entry.value ? Colors.green : Colors.red,
                      size: 20,
                    ),
                  ],
                ),
              );
            }),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Notifications Enabled',
                  style: theme.textTheme.bodyMedium,
                ),
                Icon(
                  state.notificationsEnabled ? Icons.check_circle : Icons.error,
                  color: state.notificationsEnabled ? Colors.green : Colors.red,
                  size: 20,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFCMTokenCard(NotificationState state, ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'FCM Token',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                state.fcmToken ?? 'No token available',
                style: theme.textTheme.bodySmall?.copyWith(
                  fontFamily: 'monospace',
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(height: 8),
            ElevatedButton.icon(
              onPressed: () {
                // Copy token to clipboard
                // Clipboard.setData(ClipboardData(text: state.fcmToken ?? ''));
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Token copied to clipboard')),
                );
              },
              icon: const Icon(Icons.copy),
              label: const Text('Copy Token'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreferencesSection(
    NotificationState state,
    NotificationNotifier notifier,
    ThemeData theme,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notification Preferences',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            // Dynamically generate preference switches from configuration
            ...NotificationConfig.getUserConfigurablePreferences().map((pref) {
              return _buildPreferenceSwitch(
                pref.displayName,
                pref.description,
                state.preferences[pref.key] ?? pref.defaultEnabled,
                (value) => _updatePreference(pref.key, value, notifier),
                theme,
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildPreferenceSwitch(
    String title,
    String subtitle,
    bool value,
    Function(bool) onChanged,
    ThemeData theme,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  subtitle,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }

  Widget _buildTestSection(NotificationNotifier notifier, ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Test Notifications',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Test your notification settings with a sample notification.',
              style: theme.textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () async {
                await notifier.showTestNotification();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Test notification sent!')),
                  );
                }
              },
              icon: const Icon(Icons.notifications),
              label: const Text('Send Test Notification'),
            ),
            // Debug-only FCM token logging button
            if (kDebugMode) ...[
              const SizedBox(height: 12),
              OutlinedButton.icon(
                onPressed: () async {
                  await FCMTokenConsoleLogger.logAllTokens();
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('FCM tokens logged to debug console'),
                        duration: Duration(seconds: 2),
                      ),
                    );
                  }
                },
                icon: const Icon(Icons.terminal),
                label: const Text('Log FCM Tokens (Debug)'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.orange,
                  side: const BorderSide(color: Colors.orange),
                ),
              ),
              const SizedBox(height: 8),
              OutlinedButton.icon(
                onPressed: () async {
                  await WelcomeNotificationDebugger.quickDebugTest();
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Welcome notification debug test completed - check console'),
                        duration: Duration(seconds: 2),
                      ),
                    );
                  }
                },
                icon: const Icon(Icons.celebration),
                label: const Text('Test Welcome Notification (Debug)'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.purple,
                  side: const BorderSide(color: Colors.purple),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _updatePreference(
    String key,
    bool value,
    NotificationNotifier notifier,
  ) async {
    setState(() => _isLoading = true);

    try {
      // Create a map with the specific preference to update
      final Map<String, bool?> updates = {};

      switch (key) {
        case 'charging_updates':
          updates['chargingUpdates'] = value;
          break;
        case 'station_alerts':
          updates['stationAlerts'] = value;
          break;
        case 'promotions':
          updates['promotions'] = value;
          break;
        case 'trip_reminders':
          updates['tripReminders'] = value;
          break;
        case 'wallet_updates':
          // Handle wallet updates if needed
          break;
        case 'system_updates':
          // Handle system updates if needed
          break;
      }

      // Call updatePreferences with named parameters
      await notifier.updatePreferences(
        chargingUpdates: updates['chargingUpdates'],
        stationAlerts: updates['stationAlerts'],
        promotions: updates['promotions'],
        tripReminders: updates['tripReminders'],
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating preference: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  String _formatServiceName(String serviceName) {
    return serviceName
        .split('_')
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ');
  }
}
