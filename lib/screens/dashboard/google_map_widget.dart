import 'dart:async';
import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
// Removed cluster manager import due to conflicts
import 'package:geolocator/geolocator.dart' as geo;

import '../../services/persistent_marker_service.dart'; // Import the PersistentMarkerService
import '../../services/working_clustering_service.dart'; // Import working clustering service
import '../../services/performance_monitoring_service.dart'; // Import performance monitoring
import '../../services/google_maps_performance_service.dart'; // Import Google Maps performance optimization
import '../../providers/dashboard_notifier.dart';
import '../../providers/theme_provider.dart'; // Import theme provider
import '../../config/google_maps_styles.dart'; // Import map styles
import '../../utils/app_themes.dart';

class GoogleMapWidget extends ConsumerStatefulWidget {
  final void Function(LatLng)? onTap;
  final List<Map<String, dynamic>>? stations;
  final Function(Map<String, dynamic>)? onStationSelected;
  final Function(double, double)? onLocationUpdated;
  final Function(CameraPosition)? onCameraPositionChanged;
  final double? initialLatitude;
  final double? initialLongitude;
  final double? initialZoom;
  final bool showLocationButton;
  final Set<Polyline>? polylines;
  final LatLngBounds? fitBounds;
  final Set<Marker>? additionalMarkers;
  final Function(String)? onPolylineTapped; // New callback for polyline taps
  final Function(String)?
      onMarkerFocused; // Callback when marker is focused/selected
  final bool enableClustering; // Control clustering behavior

  const GoogleMapWidget({
    super.key,
    this.onTap,
    this.stations,
    this.onStationSelected,
    this.onLocationUpdated,
    this.onCameraPositionChanged,
    this.initialLatitude,
    this.initialLongitude,
    this.initialZoom,
    this.showLocationButton = true,
    this.polylines,
    this.fitBounds,
    this.additionalMarkers,
    this.onPolylineTapped,
    this.onMarkerFocused,
    this.enableClustering = true, // Default to true for backward compatibility
  });

  @override
  ConsumerState<GoogleMapWidget> createState() => GoogleMapWidgetState();
}

class GoogleMapWidgetState extends ConsumerState<GoogleMapWidget> {
  // Google Maps controller
  final Completer<GoogleMapController> _controller = Completer();
  GoogleMapController? _mapController;

  // Map state
  bool _isMapInitialized = false;
  Set<Marker> _markers = {};

  // Smart marker caching and diffing system
  final Map<String, Marker> _markerCache = {};
  final Map<String, Map<String, dynamic>> _stationDataCache = {};
  bool _isUpdatingMarkers = false;

  // Location state
  bool _locationPermissionGranted = false;
  bool _isLoadingLocation = false;

  // Currently selected station ID for focus state management
  String? _selectedStationId;
  String? _lastSelectedStationId; // Track last selected station for diffing

  // Location tracking
  StreamSubscription<geo.Position>? _positionStreamSubscription;
  bool _isLocationTrackingEnabled = false;

  // Custom user location marker with navigation support
  Marker? _userLocationMarker;
  geo.Position? _currentUserPosition;
  double _currentBearing = 0.0; // Current heading/direction for car rotation

  // PERFORMANCE OPTIMIZATION: Simplified pulse animation for 60fps
  Timer? _pulseTimer;
  int _pulseAnimationStep = 0;
  final List<BitmapDescriptor> _pulseAnimationFrames = [];

  // Auto-centering control flags
  bool _hasShownInitialLocation = false;
  bool _userHasInteractedWithMap = false;
  DateTime? _lastCameraAnimation;

  // Use PersistentMarkerService directly for robust marker handling
  final _persistentMarkerService = PersistentMarkerService();

  // Working clustering functionality with performance optimizations
  final _clusteringService = WorkingClusteringService();

  // PERFORMANCE OPTIMIZATION: Professional performance monitoring service
  final _performanceMonitor = ApplicationPerformanceMonitor();

  // PERFORMANCE OPTIMIZATION: Google Maps performance service
  final _mapsPerformanceService = GoogleMapsPerformanceService();

  late bool _useClusteringMode; // Will be set from widget parameter
  double _currentZoomLevel = 10.0;
  double? _lastProcessedZoomLevel; // Track last processed zoom for diffing
  Timer? _clusteringUpdateTimer; // Debounce timer for clustering updates

  // PERFORMANCE OPTIMIZATION: Removed unused tracking variables

  // Theme-related state
  bool _isDarkMode = false;
  String? _currentMapStyle;

  @override
  void initState() {
    super.initState();

    // PERFORMANCE OPTIMIZATION: Initialize performance monitoring
    _performanceMonitor.initialize();

    // Set clustering mode from widget parameter
    _useClusteringMode = widget.enableClustering;
    _initializeTheme();
    _checkLocationPermission();
    _initializeMap();
    _initializeMarkerService();
    // Initialize clustering (simplified)
    _initializeClustering();
    // Initialize markers with smart caching system
    _updateMarkersWithDiffing();
    // PERFORMANCE OPTIMIZATION: Pre-generate pulse animation
    _pregeneratePulseAnimationFrames();
  }

  // Initialize theme state
  void _initializeTheme() {
    // Get initial theme state
    final themeNotifier = ref.read(themeNotifierProvider.notifier);
    _isDarkMode = themeNotifier.isDarkMode;
    _currentMapStyle = GoogleMapsStyles.getMapStyle(_isDarkMode);
  }

  // Initialize the marker service
  Future<void> _initializeMarkerService() async {
    await _persistentMarkerService.initialize();
  }

  // Initialize clustering functionality
  void _initializeClustering() {
    // Clear cluster icon cache to ensure new borderless icons are used
    _clusteringService.clearCache();
    debugPrint(
        '🔗 WORKING CLUSTERING: Initializing clustering mode: $_useClusteringMode');
  }

  // Handle cluster tap - zoom into cluster bounds
  void _onClusterTap(List<Map<String, dynamic>> stations) {
    if (stations.isEmpty || _mapController == null) return;

    debugPrint('Cluster tapped with ${stations.length} stations');

    // Calculate bounds for all stations in the cluster
    double minLat = (stations.first['latitude'] as num?)?.toDouble() ?? 0.0;
    double maxLat = minLat;
    double minLng = (stations.first['longitude'] as num?)?.toDouble() ?? 0.0;
    double maxLng = minLng;

    for (final station in stations) {
      final lat = (station['latitude'] as num?)?.toDouble() ?? 0.0;
      final lng = (station['longitude'] as num?)?.toDouble() ?? 0.0;

      minLat = math.min(minLat, lat);
      maxLat = math.max(maxLat, lat);
      minLng = math.min(minLng, lng);
      maxLng = math.max(maxLng, lng);
    }

    // Add some padding to the bounds
    final latPadding = (maxLat - minLat) * 0.1;
    final lngPadding = (maxLng - minLng) * 0.1;

    final bounds = LatLngBounds(
      southwest: LatLng(minLat - latPadding, minLng - lngPadding),
      northeast: LatLng(maxLat + latPadding, maxLng + lngPadding),
    );

    try {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngBounds(bounds, 100.0),
      );

      // Schedule a clustering update after the camera animation completes
      Future.delayed(const Duration(milliseconds: 1000), () {
        if (mounted) {
          _scheduleClusteringUpdate();
        }
      });
    } catch (e) {
      debugPrint('Error zooming to cluster: $e');
      // Fallback: zoom to center of cluster
      final centerLat = (minLat + maxLat) / 2;
      final centerLng = (minLng + maxLng) / 2;
      _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(LatLng(centerLat, centerLng),
            16.0), // Higher zoom to trigger individual markers
      );

      // Schedule a clustering update after the fallback animation
      Future.delayed(const Duration(milliseconds: 1000), () {
        if (mounted) {
          _scheduleClusteringUpdate();
        }
      });
    }
  }

  void _initializeMap() async {
    // Map will be initialized with coordinates provided by the parent widget
    // No need to override with hardcoded coordinates here
    if (_mapController != null) {
      if (mounted) {
        setState(() {
          _isMapInitialized = true;
        });
      }

      // We don't automatically get user location on map initialization
      // The parent widget will handle location updates and smooth transitions
    }
  }

  @override
  void didUpdateWidget(GoogleMapWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if polylines have changed and force rebuild if needed
    if (oldWidget.polylines != widget.polylines) {
      if (mounted) {
        setState(() {
          // Force rebuild when polylines change
        });
      }
    }

    // Smart marker update - only update if stations actually changed
    if (widget.stations != oldWidget.stations) {
      _updateMarkersWithDiffing();
    }
  }

  @override
  void dispose() {
    // PERFORMANCE OPTIMIZATION: Stop performance monitoring
    _performanceMonitor.dispose();
    _mapsPerformanceService.dispose();

    // Cancel location tracking subscription when widget is disposed
    _stopLocationTracking();

    // Stop pulsing animation
    _stopPulsingAnimation();

    // Cancel clustering timer
    _clusteringUpdateTimer?.cancel();

    // Dispose of the map controller
    _mapController?.dispose();

    // ULTRA-PERFORMANCE: Comprehensive memory cleanup
    _markerCache.clear();
    _stationDataCache.clear();
    _cachedMarkerSet = null;
    _markers.clear();
    _isMarkerSetDirty = false;

    debugPrint('🧹 MEMORY: Cleaned up Google Maps widget resources');

    // Note: We don't dispose the PersistentMarkerService because it's a singleton
    // and might be used by other components. This helps maintain the cache.

    super.dispose();
  }

  // PERFORMANCE OPTIMIZATION: Minimize setState calls for theme changes
  void _updateMapTheme(bool isDarkMode) {
    if (_isDarkMode == isDarkMode) return; // No change needed

    // Update state variables and trigger rebuild only when necessary
    if (mounted) {
      setState(() {
        _isDarkMode = isDarkMode;
        _currentMapStyle = GoogleMapsStyles.getMapStyle(_isDarkMode);
      });
    }
  }

  // Check if location permission is granted
  Future<void> _checkLocationPermission() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await geo.Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        debugPrint('Location services are disabled');
        return;
      }

      // Check location permission
      geo.LocationPermission permission =
          await geo.Geolocator.checkPermission();
      if (permission == geo.LocationPermission.denied) {
        permission = await geo.Geolocator.requestPermission();
        if (permission == geo.LocationPermission.denied) {
          debugPrint('Location permissions are denied');
          return;
        }
      }

      if (permission == geo.LocationPermission.deniedForever) {
        return;
      }

      // Permission granted
      if (mounted) {
        setState(() {
          _locationPermissionGranted = true;
        });
      }

      // Start location tracking if map is initialized
      if (_isMapInitialized) {
        _startLocationTracking();
        // Also get current location immediately to show the car marker
        _getCurrentLocationAndShowMarker();
      }
    } catch (e) {
      debugPrint('Error checking location permission: $e');
    }
  }

  // Start tracking user's location
  void _startLocationTracking() {
    if (_isLocationTrackingEnabled || !_locationPermissionGranted) {
      return;
    }

    try {
      const locationSettings = geo.LocationSettings(
        accuracy: geo.LocationAccuracy.high,
        distanceFilter:
            100, // Optimized from 50 to 100 meters to reduce processing overhead
      );

      _positionStreamSubscription = geo.Geolocator.getPositionStream(
        locationSettings: locationSettings,
      ).listen((geo.Position position) {
        if (mounted) {
          // Calculate bearing/heading for car rotation (like Google Maps live location)
          double newBearing = _currentBearing;

          // First, try to use device heading if available (more accurate for navigation)
          if (position.heading >= 0) {
            newBearing = position.heading;
          } else if (_currentUserPosition != null) {
            // Fallback to calculated bearing between positions
            newBearing = geo.Geolocator.bearingBetween(
              _currentUserPosition!.latitude,
              _currentUserPosition!.longitude,
              position.latitude,
              position.longitude,
            );
            // Only update bearing if movement is significant (> 5 meters)
            double distance = geo.Geolocator.distanceBetween(
              _currentUserPosition!.latitude,
              _currentUserPosition!.longitude,
              position.latitude,
              position.longitude,
            );
            if (distance > 5.0) {
            } else {
              newBearing =
                  _currentBearing; // Keep previous bearing for small movements
            }
          }

          // Update current bearing
          _currentBearing = newBearing;

          // Store current position for next bearing calculation
          _currentUserPosition = position;

          // Update custom user location marker with car icon and rotation
          _updateUserLocationMarker(position);
        }

        // Only notify parent of location update if we haven't shown initial location yet
        // or if user hasn't manually interacted with the map
        if (widget.onLocationUpdated != null &&
            (!_hasShownInitialLocation || !_userHasInteractedWithMap)) {
          widget.onLocationUpdated!(position.latitude, position.longitude);

          // Mark that we've shown the initial location
          if (!_hasShownInitialLocation) {
            _hasShownInitialLocation = true;
          }
        }
      });

      _isLocationTrackingEnabled = true;

      // Start pulsing animation for car marker
      _startPulsingAnimation();
    } catch (e) {
      // Error starting location tracking
    }
  }

  // Stop tracking user's location
  void _stopLocationTracking() {
    if (_positionStreamSubscription != null) {
      _positionStreamSubscription!.cancel();
      _positionStreamSubscription = null;
      _isLocationTrackingEnabled = false;
    }

    // Stop pulsing animation
    _stopPulsingAnimation();
  }

  // AGGRESSIVE PERFORMANCE: Ultra-minimal pulse animation for maximum FPS
  Future<void> _pregeneratePulseAnimationFrames() async {
    // Reduce to only 5 animation steps for ultra-high performance
    const int minimalSteps = 5;
    for (int i = 0; i < minimalSteps; i++) {
      final double pulseFraction = i / minimalSteps;
      final double pulseRadius = 12.0 +
          (math.sin(pulseFraction * math.pi) * 6.0); // Even smaller radius
      final icon = await _createCarIconFromAsset(
          rotation: _currentBearing, pulseRadius: pulseRadius);
      _pulseAnimationFrames.add(icon);
    }
  }

  void _startPulsingAnimation() {
    _stopPulsingAnimation();
    // AGGRESSIVE PERFORMANCE: Ultra-fast animation for maximum smoothness
    _pulseTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }
      // Use minimal animation steps (5 instead of 10) for maximum performance
      _pulseAnimationStep = (_pulseAnimationStep + 1) % 5;

      // Only update if location exists and animation is visible
      if (_currentUserPosition != null && _isMapInitialized) {
        _updateUserLocationMarker(_currentUserPosition!);
      }
    });
  }

  void _stopPulsingAnimation() {
    _pulseTimer?.cancel();
  }
  // --- END PERFORMANCE OPTIMIZATION ---

  // Method to focus on a specific location with debouncing
  Future<void> focusOnLocation(double latitude, double longitude,
      [double zoom = 15.0]) async {
    if (_mapController == null) {
      return;
    }

    // Debounce camera animations to prevent conflicts
    final now = DateTime.now();
    if (_lastCameraAnimation != null &&
        now.difference(_lastCameraAnimation!).inMilliseconds < 1000) {
      return;
    }

    try {
      _lastCameraAnimation = now;

      // Update current zoom level for clustering
      _currentZoomLevel = zoom;

      // PERFORMANCE OPTIMIZATION: Use optimized camera update
      final optimizedUpdate = _mapsPerformanceService
          .createOptimizedCameraUpdate(LatLng(latitude, longitude), zoom);
      await _mapController!.animateCamera(optimizedUpdate);
      debugPrint('Focused on location: $latitude, $longitude with zoom $zoom');

      // Schedule clustering update after camera animation
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          _scheduleClusteringUpdate();
        }
      });
    } catch (e) {
      debugPrint('Error focusing on location: $e');
    }
  }

  // Add a custom marker to the map
  void addCustomMarker(Marker marker) {
    if (mounted) {
      setState(() {
        // Create a new set with all existing markers plus the new one
        final newMarkers = Set<Marker>.from(_markers);

        // Remove any existing marker with the same ID
        newMarkers.removeWhere((m) => m.markerId == marker.markerId);

        // Add the new marker
        newMarkers.add(marker);

        // Update the markers
        _markers = newMarkers;
      });
    }
  }

  // Method to focus on user's current location
  void focusOnUserLocation() {
    handleLocationButtonPress();
  }

  // Public method to select a station from external calls
  void selectStation(Map<String, dynamic> station) {
    _selectStation(station);
  }

  // Get car icon using your custom PNG asset (like Google Maps live location)
  Future<BitmapDescriptor> _getCarIcon(
      {double rotation = 0.0, double pulseRadius = 0.0}) async {
    try {
      // Use your custom car marker PNG with pulsing green circle - like Google Maps live location
      // This supports rotation for navigation direction and pulsing animation
      return await _createCarIconFromAsset(
          rotation: rotation, pulseRadius: pulseRadius);
    } catch (e) {
      // Fallback to green marker
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen);
    }
  }

  // Create car icon from your custom PNG asset with rotation support (like Google Maps)
  Future<BitmapDescriptor> _createCarIconFromAsset(
      {double rotation = 0.0, double pulseRadius = 0.0}) async {
    try {
      // Load your custom car marker PNG
      final ByteData data =
          await rootBundle.load('assets/icons/cars/car marker icons.png');
      final Uint8List bytes = data.buffer.asUint8List();

      // Decode the image with superior quality settings and higher target resolution
      final ui.Codec codec = await ui.instantiateImageCodec(
        bytes,
        allowUpscaling: false, // Prevent quality loss from upscaling
        targetWidth: 200, // Higher target resolution for better quality
        targetHeight: 280, // Maintain aspect ratio
      );
      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      final ui.Image originalImage = frameInfo.image;

      final ui.Image resizedImage =
          await _resizeImage(originalImage, 28.2, 37.0);

      // Create a new image with rotation if needed
      final ui.Image rotatedImage;
      if (rotation != 0.0) {
        rotatedImage = await _rotateImage(resizedImage, rotation);
      } else {
        rotatedImage = resizedImage;
      }

      // Create final image with pulsing green circle background
      final ui.Image finalImage =
          await _addPulsingGreenCircle(rotatedImage, pulseRadius);

      // Convert to bytes for BitmapDescriptor with high quality PNG format
      final ByteData? finalByteData =
          await finalImage.toByteData(format: ui.ImageByteFormat.png);
      final Uint8List finalBytes = finalByteData!.buffer.asUint8List();

      // Create BitmapDescriptor with reduced dimensions to accommodate smaller pulsing circle
      return BitmapDescriptor.bytes(
        finalBytes,
        width: 100.0,
        height: 100.0,
      );
    } catch (e) {
      rethrow;
    }
  }

  // Resize image to specific dimensions with superior quality preservation
  Future<ui.Image> _resizeImage(
      ui.Image image, double targetWidth, double targetHeight) async {
    // Use higher resolution for internal processing to maintain quality
    final double processingScale = 2.0; // 2x internal resolution
    final double internalWidth = targetWidth * processingScale;
    final double internalHeight = targetHeight * processingScale;

    // Create a high-resolution picture recorder
    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(recorder);

    // Calculate scaling to fit within target dimensions while maintaining aspect ratio
    final double scaleX = internalWidth / image.width;
    final double scaleY = internalHeight / image.height;
    final double scale = math.min(scaleX, scaleY);

    // Calculate the actual size after scaling
    final double scaledWidth = image.width * scale;
    final double scaledHeight = image.height * scale;

    // Center the image within the target dimensions
    final double offsetX = (internalWidth - scaledWidth) / 2;
    final double offsetY = (internalHeight - scaledHeight) / 2;

    // Use superior quality paint settings for crisp rendering
    final Paint superiorQualityPaint = Paint()
      ..filterQuality = FilterQuality.high
      ..isAntiAlias = true
      ..style = PaintingStyle.fill;

    canvas.drawImageRect(
      image,
      Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble()),
      Rect.fromLTWH(offsetX, offsetY, scaledWidth, scaledHeight),
      superiorQualityPaint,
    );

    // Create high-resolution image first
    final ui.Picture picture = recorder.endRecording();
    final ui.Image highResImage =
        await picture.toImage(internalWidth.toInt(), internalHeight.toInt());

    // Now scale down to target size with high quality
    final ui.PictureRecorder finalRecorder = ui.PictureRecorder();
    final Canvas finalCanvas = Canvas(finalRecorder);

    finalCanvas.drawImageRect(
      highResImage,
      Rect.fromLTWH(0, 0, internalWidth, internalHeight),
      Rect.fromLTWH(0, 0, targetWidth, targetHeight),
      superiorQualityPaint,
    );

    final ui.Picture finalPicture = finalRecorder.endRecording();
    return await finalPicture.toImage(
        targetWidth.toInt(), targetHeight.toInt());
  }

  // Rotate image for navigation direction (like Google Maps live location)
  Future<ui.Image> _rotateImage(ui.Image image, double rotationDegrees) async {
    final double radians = rotationDegrees * (math.pi / 180);

    // Create a picture recorder to draw the rotated image
    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(recorder);

    // Calculate the size needed for the rotated image
    final double width = image.width.toDouble();
    final double height = image.height.toDouble();

    // Move to center, rotate, then move back
    canvas.translate(width / 2, height / 2);
    canvas.rotate(radians);
    canvas.translate(-width / 2, -height / 2);

    // Draw the original image with high quality
    final Paint highQualityPaint = Paint()
      ..filterQuality = FilterQuality.high
      ..isAntiAlias = true;
    canvas.drawImage(image, Offset.zero, highQualityPaint);

    // End recording and convert to image
    final ui.Picture picture = recorder.endRecording();
    return await picture.toImage(width.toInt(), height.toInt());
  }

  // Add pulsing green circle background (like Google Maps live location)
  Future<ui.Image> _addPulsingGreenCircle(
      ui.Image carImage, double pulseRadius) async {
    // Create a high-resolution canvas to accommodate the pulsing circle with superior quality
    final double canvasSize =
        128.0; // Much larger canvas for superior quality and pulse effect
    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(recorder);

    // Calculate center position
    final Offset center = Offset(canvasSize / 2, canvasSize / 2);

    // Draw pulsing green circles (multiple layers for smooth effect)
    if (pulseRadius > 0) {
      // Outer pulse circle (more transparent)
      final Paint outerPulsePaint = Paint()
        ..color = const Color(0xFF4CAF50).withValues(alpha: 0.15)
        ..style = PaintingStyle.fill;
      canvas.drawCircle(center, pulseRadius * 1.2, outerPulsePaint);

      // Middle pulse circle
      final Paint middlePulsePaint = Paint()
        ..color = const Color(0xFF4CAF50).withValues(alpha: 0.25)
        ..style = PaintingStyle.fill;
      canvas.drawCircle(center, pulseRadius * 0.8, middlePulsePaint);

      // Inner pulse circle (more opaque)
      final Paint innerPulsePaint = Paint()
        ..color = const Color(0xFF4CAF50).withValues(alpha: 0.35)
        ..style = PaintingStyle.fill;
      canvas.drawCircle(center, pulseRadius * 0.5, innerPulsePaint);
    }

    // Draw base green circle (always visible) - reduced size to match smaller car icon
    final Paint basePaint = Paint()
      ..color = const Color(0xFF4CAF50).withValues(alpha: 0.4)
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, 23.6, basePaint);

    // Draw the car image in the center with high quality
    final double carWidth = carImage.width.toDouble();
    final double carHeight = carImage.height.toDouble();
    final Offset carPosition = Offset(
      center.dx - carWidth / 2,
      center.dy - carHeight / 2,
    );

    final Paint carPaint = Paint()
      ..filterQuality = FilterQuality.high
      ..isAntiAlias = true;
    canvas.drawImage(carImage, carPosition, carPaint);

    // End recording and convert to image
    final ui.Picture picture = recorder.endRecording();
    return await picture.toImage(canvasSize.toInt(), canvasSize.toInt());
  }

  // Get current location immediately and show car marker
  Future<void> _getCurrentLocationAndShowMarker() async {
    if (!_locationPermissionGranted) {
      return;
    }

    try {
      final position = await geo.Geolocator.getCurrentPosition(
        locationSettings: const geo.LocationSettings(
          accuracy: geo.LocationAccuracy.high,
          timeLimit: Duration(seconds: 10),
        ),
      );

      // Store initial position for bearing calculations
      _currentUserPosition = position;

      // Update the custom car marker immediately
      await _updateUserLocationMarker(position);
    } catch (e) {
      // Error getting location
    }
  }

  // Update user location marker with custom car icon and navigation rotation
  Future<void> _updateUserLocationMarker(geo.Position position) async {
    try {
      // --- PERFORMANCE OPTIMIZATION: Use pre-generated pulse animation ---
      final carIcon = _pulseAnimationFrames.isNotEmpty
          ? _pulseAnimationFrames[_pulseAnimationStep]
          : await _getCarIcon(rotation: _currentBearing, pulseRadius: 0);
      // --- END PERFORMANCE OPTIMIZATION ---

      // Create or update the user location marker
      final userMarker = Marker(
        markerId: const MarkerId('user_location'),
        position: LatLng(position.latitude, position.longitude),
        icon: carIcon,
        anchor: const Offset(0.5, 0.5), // Center the icon
        infoWindow: InfoWindow(
          title: 'Your Car Location',
          snippet: 'Current position • ${_currentBearing.toStringAsFixed(0)}°',
        ),
        zIndexInt: 1, // Use a low zIndex so other markers can appear on top
        rotation: _currentBearing, // Additional rotation support if needed
      );

      if (mounted) {
        // BALANCED PERFORMANCE: Update location with reasonable thresholds
        final bool locationChanged = _currentUserPosition == null ||
            (_currentUserPosition!.latitude - position.latitude).abs() >
                0.00001 || // More sensitive threshold
            (_currentUserPosition!.longitude - position.longitude).abs() >
                0.00001 ||
            (_currentBearing - _currentBearing).abs() >
                2.0; // More sensitive bearing threshold

        if (locationChanged) {
          setState(() {
            _userLocationMarker = userMarker;
            _currentUserPosition = position;
            // Mark cache as dirty for rebuild
            _isMarkerSetDirty = true;
          });
        } else {
          // Update without setState for micro-movements
          _userLocationMarker = userMarker;
          _currentUserPosition = position;
        }
      }
    } catch (e) {
      // Error updating user location marker
    }
  }

  // Public method to reset auto-centering behavior (useful for location button)
  void resetAutoCentering() {
    _userHasInteractedWithMap = false;
    _hasShownInitialLocation = false;
  }

  // Method to fit map bounds with optimal padding for route visibility
  void _fitBounds(LatLngBounds bounds) async {
    if (_mapController != null) {
      try {
        debugPrint('📷 GOOGLE_MAP_WIDGET: Fitting bounds with optimal padding');
        debugPrint('📷 GOOGLE_MAP_WIDGET: Bounds: $bounds');

        // Use optimal padding for complete route visibility
        const double optimalPadding = 80.0;

        await _mapController!.animateCamera(
          CameraUpdate.newLatLngBounds(
            bounds,
            optimalPadding,
          ),
        );

        debugPrint(
            '📷 GOOGLE_MAP_WIDGET: ✅ Bounds fitting completed with ${optimalPadding}px padding');
      } catch (e) {
        debugPrint('📷 GOOGLE_MAP_WIDGET: ❌ Error fitting bounds: $e');
      }
    }
  }

  // Public method to fit bounds from external calls
  void fitBounds(LatLngBounds bounds) {
    _fitBounds(bounds);
  }

  // BALANCED PERFORMANCE: Simple marker caching
  Set<Marker>? _cachedMarkerSet;
  int _lastMarkerSetHash = 0;
  bool _isMarkerSetDirty = false; // Track if rebuild is needed
  DateTime _lastCacheUpdate = DateTime.now(); // Track cache age for performance

  // AGGRESSIVE PERFORMANCE: Ultra-fast marker caching with multi-level optimization
  Set<Marker> _getAllMarkers() {
    // ULTRA-PERFORMANCE: Enhanced hash with zoom level for better cache efficiency
    final int currentHash = Object.hash(
      _markers.length,
      _userLocationMarker?.markerId.value,
      widget.additionalMarkers?.length ?? 0,
      widget.stations?.length ?? 0,
      _selectedStationId,
      _currentZoomLevel.round(), // Include zoom level for better cache invalidation
    );

    // REAL-TIME CLUSTERING: Balanced caching for responsive viewport-based clustering
    final cacheAge = DateTime.now().difference(_lastCacheUpdate).inMilliseconds;
    if (_cachedMarkerSet != null &&
        currentHash == _lastMarkerSetHash &&
        !_isMarkerSetDirty &&
        _cachedMarkerSet!.isNotEmpty &&
        cacheAge < 2000) { // 2 second cache validity for real-time clustering response
      debugPrint('⚡ REAL-TIME CACHE: Using cached markers (${_cachedMarkerSet!.length} markers, age: ${cacheAge}ms)');
      return _cachedMarkerSet!;
    }

    // LEVEL 3: Full rebuild (slowest path)
    final Set<Marker> allMarkers = Set<Marker>.from(_markers);

    // Add custom user location marker with green car icon
    if (_userLocationMarker != null) {
      allMarkers.add(_userLocationMarker!);
    }

    // Add additional markers (like trip location pins)
    if (widget.additionalMarkers != null) {
      allMarkers.addAll(widget.additionalMarkers!);
    }

    // ULTRA-PERFORMANCE: Update cache with timestamp
    _cachedMarkerSet = allMarkers;
    _lastMarkerSetHash = currentHash;
    _isMarkerSetDirty = false;
    _lastCacheUpdate = DateTime.now();

    debugPrint('🔄 CACHE UPDATE: Rebuilt marker set (${allMarkers.length} markers)');
    return allMarkers;
  }

  void _selectStation(Map<String, dynamic> station) {
    // Get the station ID
    final String id = station['id'].toString();

    // Update the selected station ID
    if (mounted) {
      setState(() {
        _selectedStationId = id;
      });
    }

    // Notify dashboard about marker focus change
    if (widget.onMarkerFocused != null) {
      widget.onMarkerFocused!(id);
    }

    // Fly to the selected station's location
    final double latitude = station['latitude'] as double? ?? 0.0;
    final double longitude = station['longitude'] as double? ?? 0.0;

    // Focus on the selected station's location
    focusOnLocation(latitude, longitude, 15.0);

    // Update markers to show focused icon for selected station
    _updateMarkersWithDiffing();

    // Call the onStationSelected callback AFTER focusing on the location
    // This ensures the map is already centered when nearest stations are loaded
    if (widget.onStationSelected != null) {
      // Always try to enrich the station with UID, even if it already has one
      // This ensures we have the most up-to-date UID from the nearest stations API
      _enrichStationWithUid(station).then((enrichedStation) {
        widget.onStationSelected!(enrichedStation);
      });
    }
  }

  // Attempt to find a UID for a station based on its coordinates and other properties
  Future<Map<String, dynamic>> _enrichStationWithUid(
      Map<String, dynamic> station) async {
    try {
      final double latitude = station['latitude'] as double? ?? 0.0;
      final double longitude = station['longitude'] as double? ?? 0.0;
      final String stationName =
          (station['name'] as String? ?? '').toLowerCase();
      final String stationAddress =
          (station['address'] as String? ?? '').toLowerCase();
      final String stationId = station['id']?.toString() ?? '';

      if (latitude == 0.0 || longitude == 0.0) {
        debugPrint('Cannot enrich station - invalid coordinates');
        return station;
      }

      // Create a new map that we can modify
      final Map<String, dynamic> enrichedStation =
          Map<String, dynamic>.from(station);

      // Use the dashboard provider to get nearest stations that might have the UID
      final dashboardState = ref.read(dashboardNotifierProvider);
      final nearestStations = dashboardState.formattedNearestStations;

      if (nearestStations.isEmpty) {
        debugPrint('No nearest stations available for UID enrichment');
        return enrichedStation;
      }

      debugPrint(
          'Searching for UID match among ${nearestStations.length} nearest stations');

      // Log the first few nearest stations for debugging
      for (int i = 0; i < math.min(3, nearestStations.length); i++) {
        final s = nearestStations[i];
        debugPrint('Nearest station $i: name=${s['name']}, uid=${s['uid']}, '
            'lat=${s['latitude']}, lng=${s['longitude']}');
      }

      // Try multiple matching strategies in priority order

      // 0. First try matching by ID if available
      if (stationId.isNotEmpty) {
        var matchingStation = nearestStations.firstWhere(
          (s) =>
              s['id']?.toString() == stationId ||
              s['stationId']?.toString() == stationId,
          orElse: () => {},
        );

        if (matchingStation.isNotEmpty) {
          _updateEnrichedStation(enrichedStation, matchingStation);
          return enrichedStation;
        }
      }

      // 1. Try exact coordinate match with wider threshold
      final double coordinateThreshold = 0.001; // About 100 meters
      var matchingStation = nearestStations.firstWhere(
        (s) {
          final stationLat = s['latitude'] as double? ?? 0.0;
          final stationLng = s['longitude'] as double? ?? 0.0;

          return (stationLat - latitude).abs() < coordinateThreshold &&
              (stationLng - longitude).abs() < coordinateThreshold;
        },
        orElse: () => {},
      );

      if (matchingStation.isNotEmpty) {
        _updateEnrichedStation(enrichedStation, matchingStation);
        return enrichedStation;
      }

      // 2. Try matching by name
      if (stationName.isNotEmpty) {
        matchingStation = nearestStations.firstWhere(
          (s) {
            final name = (s['name'] as String? ?? '').toLowerCase();
            return name.isNotEmpty &&
                (name == stationName ||
                    name.contains(stationName) ||
                    stationName.contains(name));
          },
          orElse: () => {},
        );

        if (matchingStation.isNotEmpty) {
          _updateEnrichedStation(enrichedStation, matchingStation);
          return enrichedStation;
        }
      }

      // 3. Try matching by address
      if (stationAddress.isNotEmpty) {
        matchingStation = nearestStations.firstWhere(
          (s) {
            final address = (s['address'] as String? ?? '').toLowerCase();
            return address.isNotEmpty &&
                (address == stationAddress ||
                    address.contains(stationAddress) ||
                    stationAddress.contains(address));
          },
          orElse: () => {},
        );

        if (matchingStation.isNotEmpty) {
          _updateEnrichedStation(enrichedStation, matchingStation);
          return enrichedStation;
        }
      }

      // 4. Try matching by partial address (if address is long enough)
      if (stationAddress.length > 10) {
        matchingStation = nearestStations.firstWhere(
          (s) {
            final address = (s['address'] as String? ?? '').toLowerCase();
            if (address.length < 10) return false;

            return address.contains(stationAddress.substring(0, 10)) ||
                stationAddress.contains(address.substring(0, 10));
          },
          orElse: () => {},
        );

        if (matchingStation.isNotEmpty) {
          _updateEnrichedStation(enrichedStation, matchingStation);
          return enrichedStation;
        }
      }

      // 5. Last resort: use the nearest station if it's reasonably close
      if (nearestStations.isNotEmpty) {
        final nearest = nearestStations.first;
        final nearestLat = nearest['latitude'] as double? ?? 0.0;
        final nearestLng = nearest['longitude'] as double? ?? 0.0;

        // Calculate distance between points (rough approximation)
        final latDiff = (nearestLat - latitude).abs();
        final lngDiff = (nearestLng - longitude).abs();
        final roughDistance = math.sqrt(latDiff * latDiff + lngDiff * lngDiff);

        // If within ~5km (very rough approximation)
        if (roughDistance < 0.05) {
          debugPrint(
              'Using nearest station as last resort (rough distance: ${roughDistance * 111} km)');
          _updateEnrichedStation(enrichedStation, nearest);
          return enrichedStation;
        }
      }

      debugPrint('No matching station found with UID after all strategies');
      return enrichedStation;
    } catch (e) {
      debugPrint('Error enriching station with UID: $e');
      // Return the original station if there was an error
      return station;
    }
  }

  // Helper method to update enriched station with data from matching station
  void _updateEnrichedStation(Map<String, dynamic> enrichedStation,
      Map<String, dynamic> matchingStation) {
    // Copy UID
    if (matchingStation['uid'] != null &&
        matchingStation['uid'].toString().isNotEmpty) {
      final String uid = matchingStation['uid'].toString();
      enrichedStation['uid'] = uid;
    }

    // Copy other useful fields that might be missing
    final fieldsToCheck = [
      'stationId',
      'types',
      'connectors',
      'openingTimes',
      'openStatus',
      'mapPinUrl',
      'focusedMapPinUrl'
    ];

    for (final field in fieldsToCheck) {
      if (enrichedStation[field] == null && matchingStation[field] != null) {
        enrichedStation[field] = matchingStation[field];
        debugPrint('Copied missing field from matching station: $field');
      }
    }
  }

  // Handle location button press
  Future<void> handleLocationButtonPress() async {
    if (!_locationPermissionGranted) {
      await _checkLocationPermission();
      if (!_locationPermissionGranted) {
        return;
      }
    }

    if (mounted) {
      setState(() {
        _isLoadingLocation = true;
      });
    }

    try {
      final position = await geo.Geolocator.getCurrentPosition(
        locationSettings: const geo.LocationSettings(
          accuracy: geo.LocationAccuracy.high,
          timeLimit: Duration(seconds: 5),
        ),
      );

      if (mounted) {
        setState(() {
          _isLoadingLocation = false;
        });
      }

      // Reset user interaction flag when location button is explicitly pressed
      // This allows the map to center on user location when they request it
      _userHasInteractedWithMap = false;
      debugPrint(
          '🗺️ Location button pressed, temporarily enabling auto-centering');

      // Store position for bearing calculations
      _currentUserPosition = position;

      // Update custom user location marker with car icon
      await _updateUserLocationMarker(position);

      // Focus on user's current location
      await _focusOnLocation(position.latitude, position.longitude, 15.0);

      debugPrint(
          'Moved map to user location: ${position.latitude}, ${position.longitude}');
    } catch (e) {
      debugPrint('Error getting current location: $e');
      if (mounted) {
        setState(() {
          _isLoadingLocation = false;
        });
      }
    }
  }

  // Focus map on a specific location
  Future<void> _focusOnLocation(
      double latitude, double longitude, double zoom) async {
    if (_mapController != null) {
      await _mapController!.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: LatLng(latitude, longitude),
            zoom: zoom,
          ),
        ),
      );
    }
  }

  // Handle map tap for polyline selection
  void _handleMapTap(LatLng position) {
    if (widget.polylines == null || widget.polylines!.isEmpty) return;

    // Check if tap is near any polyline
    String? nearestPolylineId = _findNearestPolyline(position);

    if (nearestPolylineId != null && widget.onPolylineTapped != null) {
      widget.onPolylineTapped!(nearestPolylineId);
    }
  }

  // Find the nearest polyline to a tap position
  String? _findNearestPolyline(LatLng tapPosition) {
    if (widget.polylines == null) return null;

    const double tapThreshold =
        0.001; // Threshold for considering a tap "near" a polyline
    String? nearestPolylineId;
    double minDistance = double.infinity;

    for (final polyline in widget.polylines!) {
      // Skip shadow polylines
      if (polyline.polylineId.value.endsWith('_shadow')) continue;

      // Check distance to each point in the polyline
      for (final point in polyline.points) {
        final distance = _calculateDistance(tapPosition, point);

        if (distance < tapThreshold && distance < minDistance) {
          minDistance = distance;
          nearestPolylineId = polyline.polylineId.value;
        }
      }
    }

    return nearestPolylineId;
  }

  // Calculate distance between two LatLng points
  double _calculateDistance(LatLng point1, LatLng point2) {
    final double latDiff = point1.latitude - point2.latitude;
    final double lngDiff = point1.longitude - point2.longitude;
    return math.sqrt(latDiff * latDiff + lngDiff * lngDiff);
  }

  // PERFORMANCE OPTIMIZATION: Smart marker update with performance tracking
  Future<void> _updateMarkersWithDiffing() async {
    if (_isUpdatingMarkers) return; // Prevent concurrent updates
    _isUpdatingMarkers = true;

    // Track this expensive operation
    return _performanceMonitor.trackOperation('marker_update', () async {
      try {
        if (widget.stations == null || widget.stations!.isEmpty) {
          // Clear all markers if no stations
          _markerCache.clear();
          _stationDataCache.clear();
          if (mounted) {
            setState(() {
              _markers = {};
            });
          }
          return;
        }

        // Create a set of current station IDs for diffing
        final currentStationIds = widget.stations!
            .map((s) => s['uid']?.toString() ?? s['id']?.toString() ?? '')
            .where((id) => id.isNotEmpty)
            .toSet();

        // Create a set of existing marker IDs
        final existingMarkerIds = _markers
            .map((m) => m.markerId.value)
            .where(
                (id) => id != 'user_location') // Exclude user location marker
            .toSet();

        // ULTRA-PERFORMANCE: Enhanced change detection with multiple optimization layers
        final hasStationChanges = currentStationIds.length !=
                existingMarkerIds.length ||
            !currentStationIds.every((id) => existingMarkerIds.contains(id));

        // REAL-TIME CLUSTERING: Highly sensitive zoom threshold for immediate viewport response
        final hasZoomChanges =
            (_currentZoomLevel - (_lastProcessedZoomLevel ?? 0)).abs() > 0.3;

        // Check if selected station changed (for focus state updates)
        final hasSelectionChanges =
            _selectedStationId != _lastSelectedStationId;

        // PERFORMANCE BOOST: Skip updates more aggressively
        if (!hasStationChanges &&
            !hasZoomChanges &&
            !hasSelectionChanges &&
            _markers.isNotEmpty &&
            _cachedMarkerSet != null) {
          debugPrint(
              '⚡ ULTRA-PERFORMANCE: No significant changes detected, skipping marker update (${_markers.length} markers cached)');
          return;
        }

        if (hasSelectionChanges) {
          debugPrint(
              '🎯 Selection changed from $_lastSelectedStationId to $_selectedStationId, updating markers');
          _lastSelectedStationId = _selectedStationId;
        }

        _lastProcessedZoomLevel = _currentZoomLevel;

        if (_useClusteringMode) {
          debugPrint(
              '🗺️ GOOGLE MAP: Updating markers with working clustering');
          debugPrint('🗺️ GOOGLE MAP: Current zoom level: $_currentZoomLevel');
          debugPrint(
              '🗺️ GOOGLE MAP: Stations count: ${widget.stations!.length}');
          debugPrint('🎯 GOOGLE MAP: Selected station: $_selectedStationId');

          // Use working clustering service to generate markers
          final newMarkers = await _clusteringService.generateClusteredMarkers(
            stations: widget.stations!,
            zoomLevel: _currentZoomLevel,
            onStationTap: (station) {
              _selectStation(station);
              if (widget.onStationSelected != null) {
                widget.onStationSelected!(station);
              }
            },
            onClusterTap: (stations) {
              _onClusterTap(stations);
            },
            createStationMarker: _createMarkerForStation,
            forceRefresh:
                hasSelectionChanges, // Force refresh when selection changes
          );

          if (mounted) {
            // BALANCED PERFORMANCE: Update markers when needed
            setState(() {
              _markers = newMarkers;
              // Mark cache as dirty for rebuild
              _isMarkerSetDirty = true;
              _cachedMarkerSet = null;
            });
          }

          debugPrint(
              '🗺️ GOOGLE MAP: Generated ${newMarkers.length} markers from working clustering');
          return;
        } else {
          // Use original marker logic (fallback)
          await _updateMarkersWithoutClustering();
          return;
        }
      } finally {
        _isUpdatingMarkers = false;
      }
    });
  }

  // REAL-TIME CLUSTERING: Viewport-based clustering with minimal delay for real-time response
  void _scheduleClusteringUpdate() {
    // Cancel existing timer to prevent multiple updates
    _clusteringUpdateTimer?.cancel();

    // REAL-TIME: Short debounce for immediate viewport-based clustering response
    _clusteringUpdateTimer = Timer(const Duration(milliseconds: 300), () {
      if (mounted && _useClusteringMode) {
        // Direct execution for real-time clustering
        _updateMarkersWithDiffing();
      }
    });
  }

  // Fallback method for non-clustering marker updates
  Future<void> _updateMarkersWithoutClustering() async {
    if (widget.stations == null || widget.stations!.isEmpty) {
      if (mounted) {
        setState(() {
          _markers = {};
        });
      }
      return;
    }

    final Set<Marker> newMarkers = {};

    for (final station in widget.stations!) {
      final marker = await _createMarkerForStation(station);
      if (marker != null) {
        newMarkers.add(marker);
      }
    }

    if (mounted) {
      setState(() {
        _markers = newMarkers;
      });
    }
  }

  // PERFORMANCE OPTIMIZATION: Removed unused methods to reduce code complexity

  // Create a single marker for a station with async icon loading
  Future<Marker?> _createMarkerForStation(Map<String, dynamic> station) async {
    try {
      final double latitude = station['latitude'] as double? ?? 0.0;
      final double longitude = station['longitude'] as double? ?? 0.0;
      final String status = station['status'] as String? ?? 'Available';
      final String id = station['id'].toString();

      if (latitude == 0.0 || longitude == 0.0) {
        return null;
      }

      // Determine if this is the selected station
      bool isSelected = _selectedStationId != null && _selectedStationId == id;

      debugPrint(
          '🎯 Creating marker for station $id: isSelected=$isSelected, selectedStationId=$_selectedStationId');

      // Get marker icon using the persistent marker service
      BitmapDescriptor markerIcon =
          await _getMarkerIconForStation(station, isSelected);

      // Create and return the marker
      return Marker(
        markerId: MarkerId(id),
        position: LatLng(latitude, longitude),
        icon: markerIcon,
        anchor: const Offset(0.5, 0.5),
        infoWindow: InfoWindow(
          title: station['name'] as String? ?? 'Unknown Station',
          snippet:
              '${station['distance']?.toStringAsFixed(2) ?? '0.0'} km • $status',
          onTap: () => _selectStation(station),
        ),
        onTap: () {
          _selectStation(station);
        },
        consumeTapEvents: true,
      );
    } catch (e) {
      debugPrint('Error creating marker for station ${station['id']}: $e');
      return null;
    }
  }

  // Get marker icon for station with smart caching
  Future<BitmapDescriptor> _getMarkerIconForStation(
      Map<String, dynamic> station, bool isSelected) async {
    try {
      final String status = station['status'] as String? ?? 'Available';

      // Get the appropriate marker URL with multiple fallback options
      List<String> potentialUrls = [];

      // First try to use mapPinUrl from the station data
      if (station['mapPinUrl'] != null &&
          (station['mapPinUrl'] as String).isNotEmpty) {
        if (isSelected) {
          // For selected stations, prioritize focused versions
          if (station['focusedMapPinUrl'] != null &&
              (station['focusedMapPinUrl'] as String).isNotEmpty) {
            potentialUrls.add(station['focusedMapPinUrl'] as String);
          }

          // Add converted focused URL as fallback
          String defaultUrl = station['mapPinUrl'] as String;
          if (defaultUrl.contains('_default')) {
            potentialUrls.add(defaultUrl.replaceAll('_default', '_focus'));
          }

          // Add standard focus icon as last resort
          potentialUrls
              .add('https://api2.eeil.online/mapicons/ecoplug_focus.png');
        } else {
          // For non-selected stations, use the provided mapPinUrl
          potentialUrls.add(station['mapPinUrl'] as String);
        }
      }

      // Add status-based fallback URLs
      if (status.toLowerCase().contains('unavailable')) {
        potentialUrls
            .add('https://api2.eeil.online/mapicons/ecoplug_unavailable.png');
      } else if (status.toLowerCase().contains('in use') ||
          status.toLowerCase().contains('charging')) {
        if (isSelected) {
          potentialUrls.add(
              'https://api2.eeil.online/mapicons/ecoplug_charging_focus_new.png');
          potentialUrls.add(
              'https://api2.eeil.online/mapicons/ecoplug_charging_focus.png');
        } else {
          potentialUrls.add(
              'https://api2.eeil.online/mapicons/ecoplug_charging_default_new.png');
          potentialUrls.add(
              'https://api2.eeil.online/mapicons/ecoplug_charging_default.png');
          potentialUrls
              .add('https://api2.eeil.online/mapicons/ecoplug_charging.png');
        }
      } else {
        if (isSelected) {
          potentialUrls
              .add('https://api2.eeil.online/mapicons/ecoplug_focus.png');
        } else {
          potentialUrls
              .add('https://api2.eeil.online/mapicons/ecoplug_available.png');
          potentialUrls
              .add('https://api2.eeil.online/mapicons/ecoplug_default.png');
        }
      }

      // Debug log the potential URLs
      debugPrint(
          '🎯 Potential URLs for station ${station['id']} (isSelected=$isSelected): $potentialUrls');

      // Try each URL in the potential URLs list until one works
      BitmapDescriptor? descriptor;
      for (final url in potentialUrls) {
        // Ensure URL starts with https://
        String normalizedUrl = url.startsWith('http') ? url : 'https://$url';

        debugPrint('🎯 Trying URL: $normalizedUrl');

        // Try to get the descriptor
        descriptor = await _persistentMarkerService
            .getBitmapDescriptorFromUrl(normalizedUrl);
        if (descriptor != null) {
          debugPrint('🎯 Successfully loaded marker from: $normalizedUrl');
          return descriptor;
        } else {
          debugPrint('🎯 Failed to load marker from: $normalizedUrl');
        }
      }

      // All URLs failed, use status-based fallback
      return await _persistentMarkerService.getMarkerDescriptorForStatus(
            status,
            focused: isSelected,
            forceRefresh: true,
          ) ??
          await _persistentMarkerService
              .createCustomMarker(AppThemes.primaryColor);
    } catch (e) {
      debugPrint('Error getting marker icon: $e');
      // Create a custom marker programmatically based on status
      Color markerColor;
      if (station['status'].toString().toLowerCase().contains('unavailable')) {
        markerColor = Colors.red;
      } else if (station['status']
              .toString()
              .toLowerCase()
              .contains('in use') ||
          station['status'].toString().toLowerCase().contains('charging')) {
        markerColor = Colors.blue;
      } else {
        markerColor = AppThemes.primaryColor;
      }

      return await _persistentMarkerService.createCustomMarker(markerColor);
    }
  }

  // PERFORMANCE OPTIMIZATION: Removed unused preload method to reduce complexity

  /// INSTANT FOCUS: Update the selected station with immediate marker focus response
  void updateSelectedStation(String stationId) {
    if (_selectedStationId == stationId) {
      debugPrint('🎯 Station $stationId already selected, skipping update');
      return;
    }

    debugPrint(
        '🎯 INSTANT FOCUS: Updating selected station from $_selectedStationId to $stationId');

    // Update the selected station ID immediately
    _selectedStationId = stationId;

    // INSTANT FOCUS: Bypass all caching and debouncing for immediate response
    if (mounted && _isMapInitialized) {
      _instantMarkerFocusUpdate();
    }
  }

  /// INSTANT FOCUS: Immediate marker focus update without any delays or caching
  void _instantMarkerFocusUpdate() {
    debugPrint('⚡ INSTANT FOCUS: Updating marker focus immediately for station $_selectedStationId');

    // INSTANT FOCUS: Force immediate cache invalidation and state update
    _isMarkerSetDirty = true;
    _cachedMarkerSet = null;
    // DON'T update _lastSelectedStationId yet - let _updateMarkersWithDiffing detect the change

    // INSTANT FOCUS: Trigger immediate setState to force marker rebuild with new focus
    if (mounted) {
      setState(() {
        // This forces _getAllMarkers() to rebuild with the new _selectedStationId
        // The clustering service will be bypassed since we're using cached markers
      });

      // INSTANT FOCUS: Also trigger immediate marker update for clustering mode
      if (_useClusteringMode) {
        _instantClusteringUpdate();
      }
    }

    debugPrint('⚡ INSTANT FOCUS: Marker focus state updated immediately');
  }

  /// INSTANT FOCUS: Immediate clustering update without debouncing for focus changes
  void _instantClusteringUpdate() {
    if (!mounted || !_useClusteringMode) return;

    // INSTANT FOCUS: Skip all debouncing and update immediately
    _clusteringUpdateTimer?.cancel();

    // INSTANT FOCUS: Force immediate marker update with focus state
    _updateMarkersWithDiffing();
  }

  /// Force marker update without diffing checks - used for focus state changes
  Future<void> _forceMarkerUpdate() async {
    if (_isUpdatingMarkers) return;
    _isUpdatingMarkers = true;

    try {
      debugPrint('🎯 Force updating markers for focus state change');

      if (widget.stations == null || widget.stations!.isEmpty) {
        if (mounted) {
          setState(() {
            _markers = {};
          });
        }
        return;
      }

      if (_useClusteringMode) {
        // Force clustering service to regenerate markers with current focus state
        final newMarkers = await _clusteringService.generateClusteredMarkers(
          stations: widget.stations!,
          zoomLevel: _currentZoomLevel,
          onStationTap: (station) {
            _selectStation(station);
            if (widget.onStationSelected != null) {
              widget.onStationSelected!(station);
            }
          },
          onClusterTap: (stations) {
            _onClusterTap(stations);
          },
          createStationMarker: _createMarkerForStation,
          forceRefresh: true, // Force refresh to bypass clustering cache
        );

        if (mounted) {
          setState(() {
            _markers = newMarkers;
            _isMarkerSetDirty = true;
            _cachedMarkerSet = null;
          });
        }
      } else {
        // Use non-clustering update
        await _updateMarkersWithoutClustering();
      }
    } finally {
      _isUpdatingMarkers = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Listen for theme changes
    final themeNotifier = ref.watch(themeNotifierProvider.notifier);
    final currentIsDarkMode = themeNotifier.isDarkMode;

    // Update map theme if it changed
    if (currentIsDarkMode != _isDarkMode) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _updateMapTheme(currentIsDarkMode);
      });
    }

    // PERFORMANCE OPTIMIZATION: Use RepaintBoundary to isolate map rendering
    return RepaintBoundary(
      child: Column(
        children: [
          Expanded(
            child: Stack(
              children: [
                // PERFORMANCE OPTIMIZATION: Wrap GoogleMap in RepaintBoundary for isolated rendering
                RepaintBoundary(
                  child: GoogleMap(
                    initialCameraPosition: CameraPosition(
                      target: LatLng(widget.initialLatitude ?? 20.5937,
                          widget.initialLongitude ?? 78.9629),
                      zoom: widget.initialZoom ?? 5.0,
                    ),
                    style: _currentMapStyle, // Apply the current map style
                    myLocationEnabled:
                        false, // Disable default location marker - using custom car icon
                    myLocationButtonEnabled: false,
                    zoomControlsEnabled: false,
                    markers: _getAllMarkers(),
                    // PERFORMANCE OPTIMIZATION: Simplified polylines assignment
                    polylines: widget.polylines ?? <Polyline>{},
                    onCameraIdle: () {
                      // REAL-TIME CLUSTERING: Enable immediate viewport-based clustering on camera idle
                      if (_useClusteringMode) {
                        _scheduleClusteringUpdate();
                        debugPrint('📹 REAL-TIME CLUSTERING: Camera idle - triggering viewport clustering at zoom: $_currentZoomLevel');
                      } else {
                        debugPrint('📹 REAL-TIME: Camera idle at zoom level: $_currentZoomLevel');
                      }
                    },
                    onMapCreated: (GoogleMapController controller) {
                      _mapController = controller;
                      _controller.complete(controller);
                      if (mounted) {
                        setState(() {
                          _isMapInitialized = true;
                        });
                      }

                      // Initialize markers once map is ready
                      _updateMarkersWithDiffing();

                      // Initialize user location marker if permission is granted
                      if (_locationPermissionGranted) {
                        _startLocationTracking();
                        _getCurrentLocationAndShowMarker();
                      }

                      // Enhanced polyline rendering with multiple delays

                      // Force immediate rebuild to ensure polylines are included
                      if (mounted) {
                        Future.delayed(const Duration(milliseconds: 100), () {
                          if (mounted) {
                            setState(() {});
                          }
                        });
                      }

                      // Additional rebuild after 500ms
                      if (mounted) {
                        Future.delayed(const Duration(milliseconds: 500), () {
                          if (mounted) {
                            setState(() {});
                          }
                        });
                      }

                      // Fit bounds if provided - immediate fitting for optimal route framing
                      if (widget.fitBounds != null) {
                        debugPrint(
                            '📷 GOOGLE_MAP_WIDGET: Route bounds provided, initiating auto-framing');

                        // Immediate bounds fitting for responsive auto-zoom
                        Future.delayed(const Duration(milliseconds: 300), () {
                          if (mounted) {
                            debugPrint(
                                '📷 GOOGLE_MAP_WIDGET: Executing immediate route auto-framing');
                            _fitBounds(widget.fitBounds!);
                          }
                        });

                        // Secondary bounds fitting to ensure optimal framing
                        Future.delayed(const Duration(milliseconds: 800), () {
                          if (mounted) {
                            debugPrint(
                                '📷 GOOGLE_MAP_WIDGET: Executing secondary route auto-framing for optimal view');
                            _fitBounds(widget.fitBounds!);
                          }
                        });
                      }

                      // PERFORMANCE OPTIMIZATION: Removed unnecessary polyline check
                    },
                    onTap: (LatLng position) {
                      // Mark that user has interacted with the map
                      _userHasInteractedWithMap = true;
                      debugPrint(
                          '🗺️ User tapped map, disabling auto-centering');

                      // Check if tap is near any polyline for route selection
                      _handleMapTap(position);

                      // Call the original onTap callback
                      if (widget.onTap != null) {
                        widget.onTap!(position);
                      }
                    },
                    onCameraMove: (CameraPosition position) {
                      // Mark that user has interacted with the map (manual zoom/pan)
                      if (!_userHasInteractedWithMap) {
                        _userHasInteractedWithMap = true;
                      }

                      // Update current zoom level for clustering
                      final oldZoomLevel = _currentZoomLevel;
                      _currentZoomLevel = position.zoom;

                      // REAL-TIME CLUSTERING: Sensitive zoom threshold for immediate viewport response
                      final zoomChanged =
                          (oldZoomLevel - _currentZoomLevel).abs() > 0.3;

                      // REAL-TIME: Update on any significant zoom changes for viewport-based clustering
                      if (zoomChanged && _useClusteringMode) {
                        _scheduleClusteringUpdate();
                        debugPrint('📹 REAL-TIME CLUSTERING: Zoom change detected (${oldZoomLevel.toStringAsFixed(1)} → ${_currentZoomLevel.toStringAsFixed(1)})');
                      }

                      // Notify parent of camera position changes if callback is provided
                      if (widget.onCameraPositionChanged != null) {
                        widget.onCameraPositionChanged!(position);
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
