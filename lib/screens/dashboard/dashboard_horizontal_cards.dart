import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:ecoplug/utils/app_themes.dart';
import 'package:ecoplug/screens/station/station_list_page.dart';
import 'package:ecoplug/screens/station/station_details_page.dart';
import 'package:ecoplug/screens/dashboard/filter_dialog.dart';
import 'package:ecoplug/screens/dashboard/google_map_widget.dart';
import 'package:ecoplug/services/location_service.dart';
import 'package:ecoplug/repositories/station_repository.dart';
import 'package:ecoplug/services/connectivity_monitor.dart';
import 'package:ecoplug/services/api_bridge.dart';
import 'package:ecoplug/utils/map_marker_utils.dart';
import 'package:ecoplug/services/ongoing_sessions_service.dart';

class DashboardHorizontalCards extends ConsumerStatefulWidget {
  const DashboardHorizontalCards({super.key});

  @override
  ConsumerState<DashboardHorizontalCards> createState() =>
      _DashboardHorizontalCardsState();
}

class _DashboardHorizontalCardsState
    extends ConsumerState<DashboardHorizontalCards>
    with TickerProviderStateMixin {
  // Core services
  final LocationService _locationService = LocationService();
  final StationRepository _stationRepository = StationRepository();
  final ConnectivityMonitor _connectivityMonitor = ConnectivityMonitor();
  final ApiBridge _apiBridge = ApiBridge();
  final OngoingSessionsService _ongoingSessionsService =
      OngoingSessionsService();

  // Map and location state
  final GlobalKey<GoogleMapWidgetState> _googleMapKey =
      GlobalKey<GoogleMapWidgetState>();
  double? _currentLatitude;
  double? _currentLongitude;
  LatLng? _userActivityLocation;
  bool _hasLocationUpdate = false;

  // Track last focused station to ensure map updates
  LatLng? _lastFocusedLocation;
  DateTime? _lastMapFocusTime;

  // Debouncing for map updates during rapid swiping
  Timer? _mapUpdateTimer;
  Timer? _mapRetryTimer; // Track retry timers to prevent accumulation
  int _lastProcessedPageIndex = -1;
  int _mapRetryCount = 0; // Track retry attempts to prevent infinite loops
  static const int _maxMapRetries = 3; // Maximum retry attempts

  // Focused marker tracking for synchronization
  String? _currentFocusedStationId;

  // PERFORMANCE OPTIMIZATION: Cache for station ID mapping (nullable values)
  final Map<String, String?> _stationIdMappingCache = {};

  // Swipe threshold variables
  double _swipeStartPosition = 0.0;
  bool _isSwipeInProgress = false;
  static const double _swipeThreshold =
      75.0; // Minimum swipe distance in pixels

  // Station data
  List<Map<String, dynamic>> _stations = [];
  List<Map<String, dynamic>> _filteredStations = [];
  List<Map<String, dynamic>> _allMapStations =
      []; // All marker API stations for map display
  bool _isLoadingStations = false;
  bool _isLoadingNearestStations = false;
  bool _isLoadingMapMarkers = false;
  String? _errorMessage;
  bool _showingNearestStations = true;

  // Search and filter state
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  final bool _isSearchLoading = false;
  final bool _showFilterOptions = false;
  final Map<String, bool> _selectedConnectorFilters = {};
  String _selectedPowerOutput = 'All';
  final double _searchRadius =
      50.0; // Default 50km radius - matching dashboard_screen.dart

  // UI state
  final PageController _pageController = PageController();
  final bool _isSearchExpanded = false;

  // Animation controllers
  late AnimationController _searchAnimationController;
  late Animation<double> _searchAnimation;

  // Battery pulsing animation controllers (separate from refresh) - copied from dashboard_screen.dart
  late AnimationController _batteryPulseAnimationController;
  late Animation<double> _batteryPulseAnimation;

  // Active sessions state
  bool _hasActiveSessions = false;
  bool _isCheckingActiveSessions = false;
  Timer? _ongoingSessionsTimer;

  // Constants
  static const double _indiaLatitude = 20.5937;
  static const double _indiaLongitude = 78.9629;

  @override
  void initState() {
    super.initState();
    debugPrint(
        '🚀 DashboardHorizontalCards: initState() called - starting initialization...');

    // Initialize synchronous operations first
    _initializeConnectorFilters();
    _initializeAnimations();
    _checkOngoingSessions();
    _startOngoingSessionsTimer();

    // Start loading map markers immediately and ensure they're displayed
    debugPrint('🗺️ Starting to load all map markers...');
    _loadAllMapMarkersWithForceUpdate();

    // Use post-frame callback for location-dependent operations
    WidgetsBinding.instance.addPostFrameCallback((_) {
      debugPrint('🌍 Starting location initialization and station loading...');
      _initializeLocationAndLoadStations();
      
      // Ensure markers are displayed after initial build
      _scheduleInitialMarkersCheck();
    });

    debugPrint('🚀 DashboardHorizontalCards: initState() completed');
  }

  void _initializeAnimations() {
    _searchAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _searchAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _searchAnimationController,
      curve: Curves.easeInOut,
    ));

    // Initialize battery pulse animation controller (separate from refresh) - copied from dashboard_screen.dart
    _batteryPulseAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000), // 1 second pulse
    );

    _batteryPulseAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _batteryPulseAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  void _initializeConnectorFilters() {
    _selectedConnectorFilters.clear();
    for (String connectorType in FilterDialog.availableConnectorTypes) {
      _selectedConnectorFilters[connectorType] = false;
    }
  }

  Future<void> _initializeLocationAndLoadStations() async {
    debugPrint(
        '🌍 DashboardHorizontalCards: Starting location initialization...');

    setState(() {
      _isLoadingStations = true;
      _isLoadingNearestStations = true;
    });

    try {
      // Follow official Geolocator documentation pattern for first-time permission handling
      final position = await _determinePosition();

      if (position != null && mounted) {
        setState(() {
          _currentLatitude = position.latitude;
          _currentLongitude = position.longitude;
          _userActivityLocation = LatLng(position.latitude, position.longitude);
          _hasLocationUpdate = true;
        });

        debugPrint(
            '✅ Location obtained: ${position.latitude}, ${position.longitude}');

        // Force map rebuild with new coordinates
        debugPrint('🗺️ Triggering map rebuild with user location');
        setState(() {
          // This will trigger _buildGoogleMap() with the new coordinates and proper zoom
        });

        // Focus map on user location immediately after obtaining it
        // Multiple attempts to ensure it works
        _focusMapOnUserLocation();
        
        // Additional delayed attempts to ensure focus works
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            _focusMapOnUserLocation();
          }
        });
        
        Future.delayed(const Duration(milliseconds: 1000), () {
          if (mounted) {
            _focusMapOnUserLocation();
          }
        });

        // Load nearest stations with user's actual location
        await _loadNearestStations();
      } else {
        debugPrint(
            '❌ Location detection failed - user needs to manually enable location');
        setState(() {
          _errorMessage =
              'Location access required to find nearby stations. Please enable location services and tap the location button.';
          _isLoadingStations = false;
          _isLoadingNearestStations = false;
        });
      }
    } catch (e) {
      debugPrint('❌ Error getting location: $e');
      setState(() {
        _errorMessage =
            'Unable to access location. Please enable location services and tap the location button to find nearby stations.';
        _isLoadingStations = false;
        _isLoadingNearestStations = false;
      });
    }
  }

  /// Determine the current position of the device following official Geolocator documentation pattern.
  /// This method properly handles first-time permission requests and location service checks.
  Future<Position?> _determinePosition() async {
    bool serviceEnabled;
    LocationPermission permission;

    debugPrint('📍 Starting position determination...');

    // Test if location services are enabled.
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      // Location services are not enabled don't continue
      // accessing the position and request users of the
      // App to enable the location services.
      debugPrint('❌ Location services are disabled');
      return null;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      debugPrint('📍 Location permission denied, requesting permission...');
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        // Permissions are denied, next time you could try
        // requesting permissions again (this is also where
        // Android's shouldShowRequestPermissionRationale
        // returned true. According to Android guidelines
        // your App should show an explanatory UI now.
        debugPrint('❌ Location permissions are denied');
        return null;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      // Permissions are denied forever, handle appropriately.
      debugPrint(
          '❌ Location permissions are permanently denied, we cannot request permissions.');
      return null;
    }

    // When we reach here, permissions are granted and we can
    // continue accessing the position of the device.
    try {
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          timeLimit: Duration(seconds: 15), // Extended timeout for first launch
        ),
      );
      debugPrint(
          '✅ Position determined successfully: ${position.latitude}, ${position.longitude}');
      return position;
    } catch (e) {
      debugPrint('❌ Failed to get current position: $e');
      return null;
    }
  }

  /// Check for ongoing charging sessions
  Future<void> _checkOngoingSessions() async {
    if (!mounted) return;

    setState(() {
      _isCheckingActiveSessions = true;
    });

    try {
      debugPrint(
          '🔋 DashboardHorizontalCards: Checking for ongoing sessions...');

      final sessions = await _ongoingSessionsService.getOngoingSessions();

      if (mounted) {
        setState(() {
          _hasActiveSessions = sessions?.hasActiveSessions ?? false;
          _isCheckingActiveSessions = false;
        });

        if (_hasActiveSessions && sessions != null) {
          debugPrint(
              '🔋 DashboardHorizontalCards: Found ${sessions.activeSessionsCount} active session(s)');
          // Start pulsing animation if there are active sessions
          _startBatteryPulsingAnimation();
        } else {
          debugPrint('🔋 DashboardHorizontalCards: No active sessions found');
        }
      }
    } catch (e) {
      debugPrint(
          '❌ DashboardHorizontalCards: Error checking ongoing sessions: $e');
      if (mounted) {
        setState(() {
          _hasActiveSessions = false;
          _isCheckingActiveSessions = false;
        });
      }
    }
  }

  /// Start timer for periodic ongoing sessions checking
  void _startOngoingSessionsTimer() {
    _ongoingSessionsTimer?.cancel();
    _ongoingSessionsTimer = Timer.periodic(
      const Duration(seconds: 30), // Check every 30 seconds
      (timer) {
        if (mounted) {
          _checkOngoingSessions();
        } else {
          timer.cancel();
        }
      },
    );
  }

  /// Start pulsing animation for battery icon - copied from dashboard_screen.dart
  void _startBatteryPulsingAnimation() {
    if (!_batteryPulseAnimationController.isAnimating) {
      _batteryPulseAnimationController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    debugPrint('🗺️ DashboardHorizontalCards: Starting disposal...');

    // Cancel all timers to prevent memory leaks
    _mapUpdateTimer?.cancel();
    _mapRetryTimer?.cancel();
    _ongoingSessionsTimer?.cancel();

    // Dispose animation controllers
    _searchAnimationController.dispose();
    _batteryPulseAnimationController.dispose();

    // Dispose other controllers
    _pageController.dispose();
    _searchController.dispose();

    // Reset state to prevent any lingering references
    _mapRetryCount = 0;
    _lastProcessedPageIndex = -1;
    _lastFocusedLocation = null;
    _lastMapFocusTime = null;

    debugPrint('🗺️ DashboardHorizontalCards: Disposal completed');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Google Map as background
          _buildGoogleMap(),



          // Top search and filter bar
          _buildTopSearchBar(),

          // Active Sessions Battery Icon - Top Left Position
          if (_hasActiveSessions)
            Positioned(
              left: 16,
              top: MediaQuery.of(context).padding.top + 80, // Below search bar
              child: _buildActiveSessionsBatteryIcon(),
            ),

          // Horizontal station cards at bottom
          _buildHorizontalStationCards(),

          // Floating action buttons
          _buildFloatingActionButtons(),
        ],
      ),
    );
  }

  Widget _buildGoogleMap() {
    // Use higher zoom when user location is available, lower zoom for default location
    final double initialZoom =
        (_currentLatitude != null && _currentLongitude != null) ? 14.0 : 5.0;

    final double mapLatitude = _currentLatitude ?? _indiaLatitude;
    final double mapLongitude = _currentLongitude ?? _indiaLongitude;

    debugPrint(
        '🗺️ Building GoogleMap with ${_allMapStations.length} stations');
    debugPrint('🗺️ Loading state: $_isLoadingMapMarkers');
    debugPrint(
        '🗺️ Initial coordinates: $mapLatitude, $mapLongitude (zoom: $initialZoom)');
    debugPrint('🗺️ User location available: ${_currentLatitude != null && _currentLongitude != null}');

    // Log first few stations for debugging
    if (_allMapStations.isNotEmpty) {
      debugPrint('🗺️ First station: ${_allMapStations.first}');
      debugPrint('🗺️ Markers ready for display: ${_allMapStations.length}');
    } else {
      debugPrint('🗺️ No markers available yet');
    }

    return GoogleMapWidget(
      key: _googleMapKey,
      initialLatitude: mapLatitude,
      initialLongitude: mapLongitude,
      initialZoom: initialZoom,
      stations: _allMapStations, // Show all marker API stations on map
      onStationSelected: _onStationSelected,
      onCameraPositionChanged: _onCameraPositionChanged,
      onMarkerFocused: _onMarkerFocused,
      enableClustering: true,
    );
  }

  Widget _buildTopSearchBar() {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 16,
      left: 16,
      right: 16,
      child: Container(
        height: 56,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(28),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(25),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Search icon/button
            _buildSearchButton(),

            // Filter button
            _buildFilterButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchButton() {
    return Expanded(
      child: GestureDetector(
        onTap: _onSearchTapped,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          child: Row(
            children: [
              Icon(
                Icons.search,
                color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Search stations...',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFilterButton() {
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        color: _hasActiveFilters
            ? AppThemes.primaryColor.withAlpha(25)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(28),
      ),
      child: IconButton(
        onPressed: _showFilterDialog,
        icon: Icon(
          Icons.tune,
          color: _hasActiveFilters
              ? AppThemes.primaryColor
              : Theme.of(context).colorScheme.onSurface.withAlpha(153),
          size: 24,
        ),
      ),
    );
  }

  Widget _buildHorizontalStationCards() {
    if (_isLoadingStations || _isLoadingNearestStations) {
      return _buildLoadingIndicator();
    }

    if (_errorMessage != null) {
      return _buildErrorState();
    }

    if (_filteredStations.isEmpty) {
      return _buildEmptyState();
    }

    return Positioned(
      bottom: 100, // Moved up to avoid navigation bar overlap
      left: 0,
      right: 0,
      child: SizedBox(
        height: 210, // Optimized height with buttons at bottom edge
        child: GestureDetector(
          onHorizontalDragStart: (details) {
            _swipeStartPosition = details.globalPosition.dx;
            _isSwipeInProgress = true;
          },
          onHorizontalDragUpdate: (details) {
            if (!_isSwipeInProgress) return;

            final currentPosition = details.globalPosition.dx;
            final swipeDistance = (currentPosition - _swipeStartPosition).abs();

            // Only allow PageView to handle the gesture if swipe distance exceeds threshold
            if (swipeDistance < _swipeThreshold) {
              // Prevent PageView from responding to small movements
              return;
            }
          },
          onHorizontalDragEnd: (details) {
            _isSwipeInProgress = false;
          },
          child: PageView.builder(
            controller: _pageController,
            onPageChanged: _onStationPageChanged,
            itemCount: _filteredStations.length,
            physics:
                const BouncingScrollPhysics(), // Smoother scrolling for rapid swipes
            pageSnapping: true, // Ensure pages snap properly
            itemBuilder: (context, index) {
              return _buildStationCard(_filteredStations[index], index);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildStationCard(Map<String, dynamic> station, int index) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final stationName = station['name'] ?? 'Unknown Station';
    final address = station['address'] ?? 'No Address';
    final distance = station['distance'] ?? 0.0;
    final availability =
        station['status'] ?? station['availability'] ?? 'Available';

    // Determine color based on availability status
    Color availabilityColor;
    IconData statusIcon;
    if (availability == 'Available') {
      availabilityColor = AppThemes.primaryColor;
      statusIcon = Icons.check_circle;
    } else if (availability == 'In Use') {
      availabilityColor = Colors.orange;
      statusIcon = Icons.flash_on;
    } else {
      availabilityColor = Colors.red;
      statusIcon = Icons.cancel;
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(28), // More curved edges
          side: isDarkMode
              ? BorderSide(color: AppThemes.darkBorder, width: 1)
              : BorderSide.none,
        ),
        color: Theme.of(context).colorScheme.surface,
        elevation: isDarkMode ? 0 : 8,
        shadowColor: Colors.black.withAlpha(30),
        child: InkWell(
          onTap: () =>
              _navigateToStationDetails(station), // Navigate to station details
          borderRadius: BorderRadius.circular(28),
          child: Container(
            padding: const EdgeInsets.fromLTRB(
                16, 16, 16, 8), // Small bottom padding for buttons at edge
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(28),
              gradient: isDarkMode
                  ? null
                  : LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Theme.of(context).colorScheme.surface,
                        Theme.of(context).colorScheme.surface.withAlpha(250),
                      ],
                    ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize
                  .max, // Expand to fill space for bottom-aligned buttons
              children: [
                // Header with station icon, name and status
                Row(
                  children: [
                    // Station icon
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppThemes.primaryColor.withAlpha(25),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.ev_station,
                        color: AppThemes.primaryColor,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Station name and status
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            stationName,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 2),
                          Row(
                            children: [
                              Icon(
                                statusIcon,
                                color: availabilityColor,
                                size: 14,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                availability,
                                style: TextStyle(
                                  color: availabilityColor,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    // Distance badge
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withAlpha(25),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.location_on,
                            color: Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withAlpha(153),
                            size: 12,
                          ),
                          const SizedBox(width: 2),
                          Text(
                            '${distance.toStringAsFixed(1)} km',
                            style: TextStyle(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurface
                                  .withAlpha(153),
                              fontSize: 11,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 4), // Reduced for compact layout

                // Address with location icon
                Row(
                  children: [
                    Icon(
                      Icons.place_outlined,
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withAlpha(153),
                      size: 15,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        address,
                        style: TextStyle(
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withAlpha(153),
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),

                const SizedBox(
                    height:
                        12), // More spacing to drag connector section towards bottom

                // Connector types section with API icons
                _buildConnectorTypesSection(station),

                // Spacer to push buttons to bottom edge
                const Spacer(),

                // Action buttons row with enhanced design
                Container(
                  width: double.infinity,
                  padding:
                      EdgeInsets.zero, // Zero padding for ultra-compact buttons
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.onSurface.withAlpha(8),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    children: [
                      // Start charging button - moved to left side
                      Expanded(
                        flex: 2,
                        child: ElevatedButton.icon(
                          onPressed: () => _navigateToStationDetails(station),
                          icon: const Icon(
                            Icons.power,
                            size: 18,
                            color: Colors.white,
                          ),
                          label: const Text(
                            'Start Charging',
                            style: TextStyle(
                              fontWeight: FontWeight.w700,
                              fontSize: 13,
                              color: Colors.white,
                            ),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppThemes.primaryColor,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            padding: const EdgeInsets.symmetric(
                                vertical: 6), // Reduced for compact layout
                            elevation: 3,
                            shadowColor: AppThemes.primaryColor.withAlpha(100),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Directions button - moved to right side
                      Expanded(
                        flex: 1,
                        child: ElevatedButton.icon(
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                Colors.blue, // Same as station list
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(
                                  16), // Same curved radius as Start Charging button
                            ),
                            padding: const EdgeInsets.symmetric(
                                vertical: 6), // Reduced for compact layout
                            minimumSize: const Size.fromHeight(
                                38), // Reduced height for compact layout
                            elevation:
                                3, // Same elevation as Start Charging button
                          ),
                          onPressed: () => _openDirectionsToStation(
                              station), // Open directions only
                          icon: const Icon(
                            Icons.directions,
                            size: 18,
                            color: Colors.white,
                          ),
                          label: const Text(
                            'Directions',
                            style: TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Focus map on user's current location
  void _focusMapOnUserLocation() {
    if (_currentLatitude != null && _currentLongitude != null) {
      _focusMapOnUserLocationImmediate(_currentLatitude!, _currentLongitude!);
    }
  }

  /// Focus map on specific coordinates immediately with multiple attempts
  void _focusMapOnUserLocationImmediate(double latitude, double longitude) {
    debugPrint('🗺️ Focusing map on user location: $latitude, $longitude');

    // Immediate focus attempt
    if (_googleMapKey.currentState != null) {
      _googleMapKey.currentState!.focusOnLocation(
        latitude,
        longitude,
        14.0, // Good zoom level for user location
      );
    }

    // Add a small delay to ensure map is fully initialized and focus again
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted && _googleMapKey.currentState != null) {
        debugPrint('🗺️ Secondary user location focus attempt');
        _googleMapKey.currentState!.focusOnLocation(
          latitude,
          longitude,
          14.0, // Good zoom level for user location
        );
      }
    });

    // Final focus attempt after marker updates
    Future.delayed(const Duration(milliseconds: 1200), () {
      if (mounted && _googleMapKey.currentState != null) {
        debugPrint('🗺️ Final user location focus attempt');
        _googleMapKey.currentState!.focusOnLocation(
          latitude,
          longitude,
          14.0, // Good zoom level for user location
        );
      }
    });
  }

  Future<void> _loadNearestStations() async {
    if (!mounted) return;

    debugPrint('🔄 Loading nearest stations...');
    debugPrint('📍 Current location: $_currentLatitude, $_currentLongitude');
    debugPrint('📍 User activity location: $_userActivityLocation');

    try {
      setState(() {
        _isLoadingNearestStations = true;
        _isSearching = false;
      });

      final LatLng locationToFetch = _userActivityLocation ??
          LatLng(_currentLatitude!, _currentLongitude!);

      debugPrint(
          '📍 Location to fetch stations from: ${locationToFetch.latitude}, ${locationToFetch.longitude}');

      final apiResponse = await _connectivityMonitor.executeApiCall(
        () => _stationRepository.getNearestStations(
          locationToFetch.latitude,
          locationToFetch.longitude,
          radius: _searchRadius,
        ),
        context: mounted ? context : null,
        errorMessage:
            'Unable to load nearby stations. Please check your connection.',
        showErrorOnFailure: false,
      );

      if (apiResponse.success == true && apiResponse.data != null && mounted) {
        debugPrint('API returned ${apiResponse.data!.length} stations');

        final formattedStations = apiResponse.data!.map((station) {
          String status = station.status ?? 'Available';
          final connectorTypeStr = station.getConnectorTypesString();
          final connectorTypes = station.getConnectorTypes();

          // Convert ConnectorType objects to Maps for UI compatibility
          final connectorTypesMap = connectorTypes
              .map((connectorType) => {
                    'name': connectorType.name,
                    'icon': connectorType.icon,
                    'power': connectorType.power,
                    'power_type': connectorType.power,
                    'guns': connectorType.guns,
                    'available_guns': connectorType.availableGuns,
                  })
              .toList();

          double distance = station.distance ?? 0.0;
          final String extractedUid = station.uid;

          return {
            'id': station.stationId.toString(),
            'name': station.name,
            'latitude': station.latitude,
            'longitude': station.longitude,
            'address': station.address,
            'city': station.city,
            'uid': extractedUid,
            'distance': distance,
            'status': status,
            'availability': status,
            'connectorType': connectorTypeStr,
            'connectorTypes': connectorTypesMap,
            'freeGuns': status == 'Available' ? 1 : 0,
            'mapPinUrl': status == 'Available'
                ? 'https://api2.eeil.online/mapicons/ecoplug_available.png'
                : (status == 'In Use'
                    ? 'https://api2.eeil.online/mapicons/ecoplug_charging.png'
                    : 'https://api2.eeil.online/mapicons/ecoplug_unavailable.png'),
            'focusedMapPinUrl':
                'https://api2.eeil.online/mapicons/ecoplug_focus.png',
          };
        }).toList();

        setState(() {
          _stations = formattedStations;
          _filteredStations = List.from(formattedStations);
          _showingNearestStations = true;
          _errorMessage = null;
        });

        debugPrint('✅ Successfully loaded ${_stations.length} stations');

        // Set initial focus on first station if available
        if (_filteredStations.isNotEmpty && _currentFocusedStationId == null) {
          final firstStationId = _filteredStations[0]['id'].toString();
          debugPrint(
              '🎯 Setting initial focus on first station: $firstStationId');
          debugPrint('🎯 First station data: ${_filteredStations[0]}');
          _updateFocusedMarker(firstStationId);

          // Also ensure the PageController is at the first page
          if (_pageController.hasClients) {
            _pageController.animateToPage(
              0,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          }
        }
      } else {
        setState(() {
          _errorMessage = 'No stations found in this area';
        });
      }
    } catch (e) {
      debugPrint('❌ Error loading nearest stations: $e');
      setState(() {
        _errorMessage = 'Failed to load stations';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingStations = false;
          _isLoadingNearestStations = false;
        });
      }
    }
  }

  void _onMarkerTapped(String markerId) {
    debugPrint('🎯 Marker tapped: $markerId');

    // Find the station with matching ID
    final stationIndex =
        _filteredStations.indexWhere((station) => station['id'] == markerId);

    if (stationIndex != -1) {
      // Cancel any pending map updates and retries to avoid conflicts
      _mapUpdateTimer?.cancel();
      _mapRetryTimer?.cancel();
      _mapRetryCount = 0; // Reset retry count for marker tap

      // Reset tracking to allow immediate update
      _lastProcessedPageIndex = -1;

      // Animate to the corresponding station card
      _pageController.animateToPage(
        stationIndex,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _onCameraPositionChanged(CameraPosition position) {
    // Handle camera position changes if needed
    debugPrint('📷 Camera position changed: ${position.target}');
    
    // Ensure markers are visible after camera changes
    _ensureMarkersVisibility();
  }

  /// Ensure markers are visible on the map
  void _ensureMarkersVisibility() {
    if (!mounted || _allMapStations.isEmpty) return;

    // Check if markers should be visible and force update if needed
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted && _allMapStations.isNotEmpty) {
        debugPrint('🗺️ Ensuring ${_allMapStations.length} markers are visible');
        
        // Trigger a small state update to refresh markers
        setState(() {
          // This will cause the GoogleMapWidget to rebuild with current markers
        });
      }
    });
  }

  /// Schedule initial markers visibility check
  void _scheduleInitialMarkersCheck() {
    // Multiple checks at different intervals to ensure markers appear
    final checkIntervals = [500, 1000, 2000, 3000];
    
    for (int interval in checkIntervals) {
      Future.delayed(Duration(milliseconds: interval), () {
        if (mounted && _allMapStations.isNotEmpty) {
          debugPrint('🗺️ Initial markers check at ${interval}ms - ${_allMapStations.length} markers');
          _ensureMarkersVisibility();
        }
      });
    }
  }

  /// Schedule periodic markers visibility check
  void _schedulePeriodicMarkersCheck() {
    // Check every 5 seconds for the first minute to ensure markers stay visible
    int checkCount = 0;
    const maxChecks = 12; // 12 checks over 1 minute

    Timer.periodic(const Duration(seconds: 5), (timer) {
      checkCount++;

      if (!mounted || checkCount >= maxChecks) {
        timer.cancel();
        return;
      }

      if (_allMapStations.isNotEmpty) {
        debugPrint('🗺️ Periodic markers check #$checkCount - ${_allMapStations.length} markers');
        _ensureMarkersVisibility();

        // Also verify marker focus state
        _verifyMarkerFocusState();
      }
    });
  }

  /// Verify and fix marker focus state if needed
  void _verifyMarkerFocusState() {
    if (_currentFocusedStationId != null && _googleMapKey.currentState != null) {
      final mapStationId = _findCorrespondingMapStationId(_currentFocusedStationId!);
      final idToUse = mapStationId ?? _currentFocusedStationId!;

      // Ensure the map widget has the correct focused station
      _googleMapKey.currentState!.updateSelectedStation(idToUse);
    }
  }

  void _onSearchTapped() {
    debugPrint('🔍 Search tapped - navigating to station list');

    // Animate search expansion
    _searchAnimationController.forward().then((_) {
      // Navigate to station list page
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const StationListPage(),
        ),
      ).then((_) {
        // Reset animation when returning
        _searchAnimationController.reverse();
      });
    });
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => FilterDialog(
        selectedConnectorFilters: Map.from(_selectedConnectorFilters),
        selectedPowerOutput: _selectedPowerOutput,
        onApplyFilters: (connectorFilters, powerOutput) {
          setState(() {
            _selectedConnectorFilters.clear();
            _selectedConnectorFilters.addAll(connectorFilters);
            _selectedPowerOutput = powerOutput;
          });
          _applyFilters();
        },
      ),
    );
  }

  bool get _hasActiveFilters {
    bool hasConnectorFilter =
        _selectedConnectorFilters.values.any((selected) => selected);
    bool hasPowerOutputFilter = _selectedPowerOutput != 'All';
    return hasConnectorFilter || hasPowerOutputFilter;
  }

  // Generate user-friendly feedback message when no stations match filters - matching dashboard_screen.dart
  String _generateFilterFeedbackMessage() {
    List<String> activeFilters = [];

    // Check power output filter
    if (_selectedPowerOutput != 'All') {
      activeFilters.add('$_selectedPowerOutput power output');
    }

    // Check connector type filters
    List<String> selectedConnectors = _selectedConnectorFilters.entries
        .where((entry) => entry.value)
        .map((entry) => entry.key)
        .toList();

    if (selectedConnectors.isNotEmpty) {
      if (selectedConnectors.length == 1) {
        activeFilters.add('${selectedConnectors.first} connector');
      } else {
        activeFilters.add('${selectedConnectors.join(', ')} connectors');
      }
    }

    if (activeFilters.isEmpty) {
      return 'No stations found in this area';
    } else if (activeFilters.length == 1) {
      return 'No stations found with ${activeFilters.first} in this area';
    } else {
      return 'No stations found with ${activeFilters.join(' and ')} in this area';
    }
  }

  // Clear all active filters and reload unfiltered data - matching dashboard_screen.dart
  Future<void> _clearAllFilters() async {
    setState(() {
      // Clear connector filters
      for (String key in _selectedConnectorFilters.keys) {
        _selectedConnectorFilters[key] = false;
      }
      // Reset power output filter
      _selectedPowerOutput = 'All';
      // Clear filtered results
      _filteredStations = [];
      _isSearching = false;
    });

    // Reload unfiltered data
    await _loadNearestStations();

    debugPrint('✅ All filters cleared and data reloaded');
  }

  void _onStationSelected(Map<String, dynamic> station) {
    debugPrint('🎯 Station selected from map: ${station['name']}');

    // Get station coordinates
    final double lat = station['latitude'] as double? ?? 0.0;
    final double lng = station['longitude'] as double? ?? 0.0;

    debugPrint('📍 Marker coordinates: $lat, $lng');

    if (lat != 0.0 && lng != 0.0) {
      // Load nearest stations from the tapped marker's location
      _loadNearestStationsFromLocation(lat, lng);
    } else {
      // Fallback to just finding the station in current list
      _onMarkerTapped(station['id']);
    }
  }

  /// Load nearest stations from a specific location (used when marker is tapped)
  Future<void> _loadNearestStationsFromLocation(double lat, double lng) async {
    if (!mounted) return;

    debugPrint('🔄 Loading nearest stations from marker location: $lat, $lng');

    try {
      setState(() {
        _isLoadingNearestStations = true;
        _isSearching = false;
        // Update user activity location to the tapped marker's location
        _userActivityLocation = LatLng(lat, lng);
      });

      final apiResponse = await _connectivityMonitor.executeApiCall(
        () => _stationRepository.getNearestStations(
          lat,
          lng,
          radius: _searchRadius,
        ),
        context: mounted ? context : null,
        errorMessage:
            'Unable to load nearby stations. Please check your connection.',
        showErrorOnFailure: false,
      );

      if (apiResponse.success == true && apiResponse.data != null && mounted) {
        debugPrint(
            'API returned ${apiResponse.data!.length} stations from marker location');

        final formattedStations = apiResponse.data!.map((station) {
          String status = station.status ?? 'Available';
          final connectorTypeStr = station.getConnectorTypesString();
          final connectorTypes = station.getConnectorTypes();

          // Convert ConnectorType objects to Maps for UI compatibility
          final connectorTypesMap = connectorTypes
              .map((connectorType) => {
                    'name': connectorType.name,
                    'icon': connectorType.icon,
                    'power': connectorType.power,
                    'power_type': connectorType.power,
                    'guns': connectorType.guns,
                    'available_guns': connectorType.availableGuns,
                  })
              .toList();

          double distance = station.distance ?? 0.0;
          final String extractedUid = station.uid;

          return {
            'id': station.stationId?.toString() ?? '',
            'uid': extractedUid,
            'name': station.name ?? 'Unknown Station',
            'address': station.address ?? 'Address not available',
            'latitude': station.latitude ?? 0.0,
            'longitude': station.longitude ?? 0.0,
            'status': status,
            'distance': distance,
            'connectorTypeStr': connectorTypeStr,
            'connectorTypes': connectorTypesMap,
            'types': connectorTypesMap,
          };
        }).toList();

        setState(() {
          _stations = formattedStations;
          _filteredStations = List.from(formattedStations);
          _showingNearestStations = true;
          _errorMessage = null;
        });

        debugPrint(
            '✅ Successfully loaded ${_stations.length} stations from marker location');

        // Focus on the first station in the list and set marker focus
        if (_filteredStations.isNotEmpty) {
          final firstStationId = _filteredStations[0]['id'].toString();
          debugPrint(
              '🎯 Setting focus on first station from marker location: $firstStationId');

          // Set marker focus immediately
          _updateFocusedMarkerImmediate(firstStationId);

          // Animate to first page
          if (_pageController.hasClients) {
            _pageController.animateToPage(
              0,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          }
        }
      } else {
        setState(() {
          _errorMessage = 'No stations found in this area';
        });
      }
    } catch (e) {
      debugPrint('❌ Error loading nearest stations from marker location: $e');
      setState(() {
        _errorMessage = 'Failed to load stations from this location';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingStations = false;
          _isLoadingNearestStations = false;
        });
      }
    }
  }

  /// PERFORMANCE OPTIMIZED: Fast station page change handler
  void _onStationPageChanged(int index) {
    // PERFORMANCE: Quick validation without debug prints
    if (index < 0 ||
        index >= _filteredStations.length ||
        _lastProcessedPageIndex == index) {
      return;
    }

    // Cancel any pending map updates
    _mapUpdateTimer?.cancel();
    _mapRetryTimer?.cancel();
    _mapRetryCount = 0;

    final station = _filteredStations[index];
    final stationId = station['id']?.toString() ?? '';
    final lat = station['latitude'] as double? ?? 0.0;
    final lng = station['longitude'] as double? ?? 0.0;

    // PERFORMANCE: Quick coordinate validation
    if (lat == 0.0 || lng == 0.0) {
      return;
    }

    // Update tracking
    _lastProcessedPageIndex = index;

    // INSTANT FOCUS: Immediate marker focus update without any delay
    _updateFocusedMarkerImmediate(stationId);

    // INSTANT FOCUS: Immediate camera update without debouncing for focus changes
    _updateMapForStationWithFocus(lat, lng, station['name'] ?? '', stationId);
  }

  /// PERFORMANCE OPTIMIZED: Enhanced map update with focus synchronization
  void _updateMapForStationWithFocus(
      double lat, double lng, String stationName, String stationId) {
    if (!mounted) return;

    // PERFORMANCE: Update map camera position only
    _updateMapForStation(lat, lng, stationName);

    // Note: Marker focus already updated in _onStationPageChanged for performance
  }

  /// Update the focused marker state and refresh map markers
  void _updateFocusedMarker(String stationId) {
    if (_currentFocusedStationId == stationId) {
      return; // Skip debug print for performance
    }

    debugPrint('🎯 Updating focused marker from $_currentFocusedStationId to $stationId');

    // Update the focused station ID
    _currentFocusedStationId = stationId;

    // Find the corresponding station in _allMapStations to ensure ID match
    final mapStationId = _findCorrespondingMapStationId(stationId);

    // Use the mapped ID if found, otherwise use the original ID
    final idToUse = mapStationId ?? stationId;
    debugPrint('🎯 Using map station ID: $idToUse (mapped from $stationId)');

    // PERFORMANCE OPTIMIZATION: Remove delay for instant focus updates
    // Immediately notify the map widget to update marker focus states
    final mapState = _googleMapKey.currentState;
    if (mapState != null) {
      mapState.updateSelectedStation(idToUse);
      debugPrint('🎯 Map widget notified of selected station: $idToUse');
    } else {
      debugPrint('❌ Map widget not available for focus update');
    }
  }

  /// ENHANCED: Find corresponding station ID with improved matching logic
  String? _findCorrespondingMapStationId(String filteredStationId) {
    // PERFORMANCE: Check cache first
    if (_stationIdMappingCache.containsKey(filteredStationId)) {
      return _stationIdMappingCache[filteredStationId];
    }

    String? result;

    // First try direct ID match
    final directMatch = _allMapStations.firstWhere(
      (station) => station['id'].toString() == filteredStationId,
      orElse: () => <String, dynamic>{},
    );

    if (directMatch.isNotEmpty) {
      result = directMatch['id'].toString();
      debugPrint('🎯 Direct ID match found: $filteredStationId -> $result');
    } else {
      // If no direct match, try coordinate-based matching with improved precision
      final filteredStation = _filteredStations.firstWhere(
        (station) => station['id'].toString() == filteredStationId,
        orElse: () => <String, dynamic>{},
      );

      if (filteredStation.isNotEmpty) {
        final lat = filteredStation['latitude'] as double? ?? 0.0;
        final lng = filteredStation['longitude'] as double? ?? 0.0;

        if (lat != 0.0 && lng != 0.0) {
          // Try coordinate matching with multiple precision levels
          final precisionLevels = [0.00001, 0.0001, 0.001]; // Increasing tolerance

          for (final precision in precisionLevels) {
            final coordinateMatch = _allMapStations.firstWhere(
              (station) {
                final mapLat = station['latitude'] as double? ?? 0.0;
                final mapLng = station['longitude'] as double? ?? 0.0;
                final latDiff = (lat - mapLat).abs();
                final lngDiff = (lng - mapLng).abs();
                return latDiff < precision && lngDiff < precision;
              },
              orElse: () => <String, dynamic>{},
            );

            if (coordinateMatch.isNotEmpty) {
              result = coordinateMatch['id'].toString();
              debugPrint('🎯 Coordinate match found with precision $precision: $filteredStationId -> $result');
              debugPrint('   Filtered station coords: $lat, $lng');
              debugPrint('   Map station coords: ${coordinateMatch['latitude']}, ${coordinateMatch['longitude']}');
              break;
            }
          }

          // If still no match, try name-based matching as last resort
          if (result == null) {
            final stationName = filteredStation['name']?.toString() ?? '';
            if (stationName.isNotEmpty) {
              final nameMatch = _allMapStations.firstWhere(
                (station) {
                  final mapName = station['name']?.toString() ?? '';
                  return mapName.toLowerCase().trim() == stationName.toLowerCase().trim();
                },
                orElse: () => <String, dynamic>{},
              );

              if (nameMatch.isNotEmpty) {
                result = nameMatch['id'].toString();
                debugPrint('🎯 Name match found: $filteredStationId -> $result (name: $stationName)');
              }
            }
          }
        }
      }
    }

    // Log if no match found for debugging
    if (result == null) {
      debugPrint('❌ No matching map station found for filtered station ID: $filteredStationId');
      if (_filteredStations.isNotEmpty && _allMapStations.isNotEmpty) {
        debugPrint('   Available filtered station IDs: ${_filteredStations.take(3).map((s) => s['id']).join(', ')}...');
        debugPrint('   Available map station IDs: ${_allMapStations.take(3).map((s) => s['id']).join(', ')}...');
      }
    }

    // PERFORMANCE: Cache the result for future lookups (even if null to avoid repeated searches)
    _stationIdMappingCache[filteredStationId] = result;

    return result;
  }

  /// INSTANT FOCUS: Immediately update focused marker state without any delay
  void _updateFocusedMarkerImmediate(String stationId) {
    if (_currentFocusedStationId == stationId) {
      return; // Skip if already focused for performance
    }

    debugPrint('⚡ INSTANT FOCUS: Station slide detected - updating marker focus for $stationId');

    // Update the focused station ID
    _currentFocusedStationId = stationId;

    // INSTANT FOCUS: Cache the map station ID lookup
    final mapStationId = _findCorrespondingMapStationId(stationId);

    // Use the mapped ID if found, otherwise use the original ID
    final idToUse = mapStationId ?? stationId;

    // INSTANT FOCUS: Direct map update with immediate response
    final mapState = _googleMapKey.currentState;
    if (mapState != null) {
      mapState.updateSelectedStation(idToUse);
      debugPrint('⚡ INSTANT FOCUS: Map marker focus updated immediately for station $idToUse');
    } else {
      debugPrint('❌ INSTANT FOCUS: Map widget not available - marker focus update skipped');
    }
  }

  /// Handle marker focus from map tap - synchronize dashboard slide
  void _onMarkerFocused(String stationId) {
    debugPrint('🎯 Marker focused: $stationId');

    // Update the focused station ID immediately to ensure proper state
    _currentFocusedStationId = stationId;

    // Find the station index in filtered stations using multiple matching strategies
    int stationIndex = _filteredStations.indexWhere(
      (station) => station['id'].toString() == stationId,
    );

    // If direct ID match fails, try reverse mapping
    if (stationIndex == -1) {
      debugPrint('🔍 Direct ID match failed, trying reverse mapping...');

      // Look for a station in filtered stations that maps to this marker ID
      for (int i = 0; i < _filteredStations.length; i++) {
        final filteredStationId = _filteredStations[i]['id'].toString();
        final mappedId = _findCorrespondingMapStationId(filteredStationId);
        if (mappedId == stationId) {
          stationIndex = i;
          debugPrint('🎯 Found station via reverse mapping: index $i, filteredId: $filteredStationId');
          break;
        }
      }
    }

    if (stationIndex != -1 && stationIndex != _lastProcessedPageIndex) {
      debugPrint('🎯 Synchronizing dashboard slide to index: $stationIndex');

      // Cancel any pending map updates to prevent conflicts
      _mapUpdateTimer?.cancel();
      _mapRetryTimer?.cancel();
      _mapRetryCount = 0;

      // Reset tracking to allow immediate update
      _lastProcessedPageIndex = -1;

      // Animate to the corresponding slide
      if (_pageController.hasClients) {
        _pageController.animateToPage(
          stationIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    } else if (stationIndex == -1) {
      debugPrint('❌ Station $stationId not found in filtered stations after all matching attempts');
      debugPrint('   Available filtered station IDs: ${_filteredStations.map((s) => s['id']).take(5).join(', ')}...');
      debugPrint('   Available map station IDs: ${_allMapStations.map((s) => s['id']).take(5).join(', ')}...');

      // Station not in current filtered list - find it in all map stations
      final mapStation = _allMapStations.firstWhere(
        (station) => station['id'].toString() == stationId,
        orElse: () => <String, dynamic>{},
      );

      if (mapStation.isNotEmpty) {
        debugPrint(
            '🎯 Found station in map stations, loading nearest stations from its location');
        final double lat = mapStation['latitude'] as double? ?? 0.0;
        final double lng = mapStation['longitude'] as double? ?? 0.0;

        if (lat != 0.0 && lng != 0.0) {
          // Load nearest stations from the tapped marker's location
          _loadNearestStationsFromLocation(lat, lng);
        }
      } else {
        debugPrint('❌ Station $stationId not found in map stations either');
      }
    } else {
      debugPrint(
          '🎯 Station $stationId already at current slide index: $stationIndex');
    }
  }

  /// PERFORMANCE OPTIMIZED: Fast map camera update
  void _updateMapForStation(double lat, double lng, String stationName) {
    if (!mounted) return;

    final mapState = _googleMapKey.currentState;
    if (mapState == null) {
      // PERFORMANCE: Reduced retry attempts and faster retry for better responsiveness
      if (_mapRetryCount >= 2) {
        _mapRetryCount = 0;
        return;
      }

      _mapRetryTimer?.cancel();
      _mapRetryCount++;

      // PERFORMANCE: Faster retry delay (50ms instead of 100ms)
      _mapRetryTimer = Timer(const Duration(milliseconds: 50), () {
        if (mounted) {
          _updateMapForStation(lat, lng, stationName);
        } else {
          _mapRetryCount = 0;
        }
      });
      return;
    }

    _mapRetryCount = 0;

    try {
      // PERFORMANCE: Update tracking efficiently
      _lastFocusedLocation = LatLng(lat, lng);
      _lastMapFocusTime = DateTime.now();

      // PERFORMANCE: Direct map focus without debug prints
      mapState.focusOnLocation(lat, lng, 15.0);
    } catch (e) {
      _mapRetryCount = 0;
    }
  }

  /// Reset all map-related state and timers to prevent memory leaks
  void _resetMapState() {
    debugPrint('🗺️ Resetting map state and cleaning up timers...');

    // Cancel all map-related timers
    _mapUpdateTimer?.cancel();
    _mapRetryTimer?.cancel();

    // Reset state variables
    _mapRetryCount = 0;
    _lastProcessedPageIndex = -1;
    _lastFocusedLocation = null;
    _lastMapFocusTime = null;

    debugPrint('🗺️ Map state reset completed');
  }

  /// Handle app lifecycle changes to prevent map freezing during background/foreground transitions
  void _handleAppLifecycleChange() {
    debugPrint(
        '🗺️ App lifecycle changed, resetting map state to prevent freezing...');
    _resetMapState();
  }

  /// Force map recovery if it becomes unresponsive
  Future<void> _recoverMapIfNeeded() async {
    if (!mounted) return;

    try {
      debugPrint('🗺️ Attempting map recovery...');

      // Reset all map state
      _resetMapState();

      // Give the map widget time to stabilize
      await Future.delayed(const Duration(milliseconds: 300));

      // Try to refresh the current station if we have one
      if (_filteredStations.isNotEmpty && _pageController.hasClients) {
        final currentIndex = _pageController.page?.round() ?? 0;
        if (currentIndex < _filteredStations.length) {
          final station = _filteredStations[currentIndex];
          final lat = station['latitude'] as double? ?? 0.0;
          final lng = station['longitude'] as double? ?? 0.0;
          final name = station['name'] as String? ?? 'Unknown Station';

          if (lat != 0.0 && lng != 0.0) {
            debugPrint('🗺️ Recovering map focus to current station: $name');
            _updateMapForStation(lat, lng, name);
          }
        }
      }

      debugPrint('🗺️ Map recovery completed');
    } catch (e) {
      debugPrint('❌ Error during map recovery: $e');
    }
  }

  Widget _buildLoadingIndicator() {
    return Positioned(
      bottom: 100, // Moved up to avoid navigation bar overlap
      left: 0,
      right: 0,
      child: Container(
        height: 180, // Reduced height to match new layout
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 40,
                height: 40,
                child: CircularProgressIndicator(
                  valueColor:
                      AlwaysStoppedAnimation<Color>(AppThemes.primaryColor),
                  strokeWidth: 3,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Loading stations...',
                style: TextStyle(
                  fontSize: 16,
                  color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    // Check if this is a location-related error
    final isLocationError = _errorMessage?.contains('location') == true;

    return Positioned(
      bottom: 100, // Moved up to avoid navigation bar overlap
      left: 0,
      right: 0,
      child: Container(
        height: 220, // Increased height for additional button
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Center(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  isLocationError ? Icons.location_off : Icons.error_outline,
                  size: 48,
                  color: isLocationError
                      ? Colors.orange
                      : Theme.of(context).colorScheme.error,
                ),
                const SizedBox(height: 16),
                Text(
                  _errorMessage ?? 'Something went wrong',
                  style: TextStyle(
                    fontSize: 16,
                    color: Theme.of(context).colorScheme.onSurface,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                if (isLocationError) ...[
                  // Location-specific action button
                  ElevatedButton.icon(
                    onPressed: _getCurrentLocation,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppThemes.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                    ),
                    icon: const Icon(Icons.my_location, size: 20),
                    label: const Text('Enable Location'),
                  ),
                  const SizedBox(height: 12),
                  // Alternative: Browse all stations
                  TextButton(
                    onPressed: _navigateToStationList,
                    child: Text(
                      'Browse All Stations',
                      style: TextStyle(
                        color: AppThemes.primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ] else ...[
                  // Generic retry button for other errors
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _errorMessage = null;
                      });
                      _initializeLocationAndLoadStations();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppThemes.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Retry'),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Positioned(
      bottom: 100, // Moved up to avoid navigation bar overlap
      left: 0,
      right: 0,
      child: Container(
        height: 180, // Reduced height to match new layout
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.ev_station_outlined,
                size: 48,
                color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
              ),
              const SizedBox(height: 16),
              Text(
                'No stations found',
                style: TextStyle(
                  fontSize: 16,
                  color: Theme.of(context).colorScheme.onSurface,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Try adjusting your filters or search in a different area',
                style: TextStyle(
                  fontSize: 14,
                  color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFloatingActionButtons() {
    return Positioned(
      right: 16,
      bottom:
          380, // Moved upward toward middle of screen for better accessibility
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FloatingActionButton(
            heroTag: "refresh",
            onPressed: _loadNearestStations,
            backgroundColor: AppThemes.primaryColor,
            child: const Icon(Icons.refresh, color: Colors.white),
          ),
          const SizedBox(height: 12),
          FloatingActionButton(
            heroTag: "location",
            onPressed: _getCurrentLocation,
            backgroundColor: Theme.of(context).colorScheme.surface,
            child: Icon(
              Icons.my_location,
              color: AppThemes.primaryColor,
            ),
          ),
          const SizedBox(height: 12),
          FloatingActionButton(
            heroTag: "list",
            onPressed: _navigateToStationList,
            backgroundColor: Theme.of(context).colorScheme.surface,
            child: Icon(
              Icons.list,
              color: AppThemes.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _getCurrentLocation() async {
    debugPrint('🎯 Target button pressed - getting current location...');

    // Show loading state
    setState(() {
      _isLoadingNearestStations = true;
    });

    try {
      // Force location service to get fresh location
      _locationService.invalidateLocation();

      // Use the proper position determination method that handles permissions correctly
      Position? position = await _determinePosition();

      if (position != null && mounted) {
        setState(() {
          _currentLatitude = position.latitude;
          _currentLongitude = position.longitude;
          _userActivityLocation = LatLng(position.latitude, position.longitude);
          _hasLocationUpdate = true;
        });

        debugPrint(
            '✅ Target button: Location obtained: ${position.latitude}, ${position.longitude}');

        // Force map rebuild with new coordinates
        setState(() {
          // This will trigger _buildGoogleMap() with the new coordinates and proper zoom
        });

        // Focus map on user location with multiple attempts
        _focusMapOnUserLocationImmediate(position.latitude, position.longitude);

        // Reload stations for new location
        await _loadNearestStations();

        // Location updated successfully - no snackbar needed
      } else {
        debugPrint('❌ Target button: Unable to get location');
        // Location error - user will see error state in UI
      }
    } catch (e) {
      debugPrint('❌ Error getting current location: $e');
      // Location error - user will see error state in UI
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingNearestStations = false;
        });
      }
    }
  }

  void _navigateToStationList() {
    debugPrint('🔍 Navigating to station list page');
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const StationListPage(),
      ),
    );
  }

  /// FIXED: Map marker loading with proper state management and forced update
  Future<void> _loadAllMapMarkersWithForceUpdate() async {
    if (!mounted) return;

    setState(() {
      _isLoadingMapMarkers = true;
    });

    try {
      debugPrint('🗺️ Loading all marker API stations for map display...');
      final markers = await _apiBridge.getApiStationMarkers();

      debugPrint('🗺️ API returned ${markers.length} raw markers');

      if (mounted) {
        if (markers.isNotEmpty) {
          final formattedMarkers =
              MapMarkerUtils.convertMarkersToMapFormat(markers);

          debugPrint(
              '🗺️ Formatted ${formattedMarkers.length} markers for map display');

          setState(() {
            _allMapStations = formattedMarkers;
            _isLoadingMapMarkers = false;
          });

          debugPrint(
              '✅ Successfully set ${_allMapStations.length} stations in state');

          // Clear cache when new markers are loaded
          _stationIdMappingCache.clear();

          // Force map widget to update markers immediately after loading
          _forceMapMarkersUpdate();
          
          // Additional immediate visibility check
          _ensureMarkersVisibility();
          
          // Schedule periodic checks to ensure markers remain visible
          _schedulePeriodicMarkersCheck();
        } else {
          debugPrint('⚠️ API returned empty markers list');
          setState(() {
            _allMapStations = [];
            _isLoadingMapMarkers = false;
          });
        }
      } else {
        debugPrint('⚠️ Widget not mounted, skipping marker update');
      }
    } catch (e) {
      debugPrint('❌ Error loading marker stations: $e');
      debugPrint('❌ Error type: ${e.runtimeType}');
      debugPrint('❌ Error details: ${e.toString()}');

      if (mounted) {
        setState(() {
          _allMapStations = [];
          _isLoadingMapMarkers = false;
        });
      }
    }
  }

  /// FIXED: Map marker loading with proper state management (legacy method for compatibility)
  Future<void> _loadAllMapMarkers() async {
    return _loadAllMapMarkersWithForceUpdate();
  }

  /// Force map widget to update and display markers immediately
  void _forceMapMarkersUpdate() {
    if (!mounted || _allMapStations.isEmpty) return;

    debugPrint('🗺️ Force updating ${_allMapStations.length} markers on map');

    // Immediate state update to ensure markers are passed to map widget
    setState(() {
      // This triggers a rebuild of GoogleMapWidget with current _allMapStations
    });

    // Verify markers are properly formatted and have required fields
    _validateMarkersData();

    // Additional attempts with different strategies
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted && _allMapStations.isNotEmpty) {
        debugPrint('🗺️ Secondary marker update attempt');
        setState(() {
          // Force another rebuild to ensure markers stick
        });
      }
    });

    // Final verification
    Future.delayed(const Duration(milliseconds: 1000), () {
      if (mounted && _allMapStations.isNotEmpty) {
        debugPrint('🗺️ Final marker verification - ${_allMapStations.length} markers should be visible');
        debugPrint('🗺️ Sample marker data: ${_allMapStations.first}');
        
        // Log marker coordinates for debugging
        final sampleMarkers = _allMapStations.take(3).map((marker) => {
          'id': marker['id'],
          'name': marker['name'],
          'lat': marker['latitude'],
          'lng': marker['longitude'],
        }).toList();
        debugPrint('🗺️ Sample marker coordinates: $sampleMarkers');
      }
    });
  }

  /// Validate that markers have all required fields for display
  void _validateMarkersData() {
    if (_allMapStations.isEmpty) {
      debugPrint('⚠️ No markers to validate');
      return;
    }

    int validMarkers = 0;
    int invalidMarkers = 0;

    for (var marker in _allMapStations) {
      final hasId = marker['id'] != null;
      final hasLat = marker['latitude'] != null && marker['latitude'] is double;
      final hasLng = marker['longitude'] != null && marker['longitude'] is double;
      final hasName = marker['name'] != null;

      if (hasId && hasLat && hasLng && hasName) {
        validMarkers++;
      } else {
        invalidMarkers++;
        debugPrint('❌ Invalid marker: $marker');
      }
    }

    debugPrint('✅ Marker validation: $validMarkers valid, $invalidMarkers invalid');
  }

  Future<void> _applyFilters() async {
    // Check if any filters are selected - matching dashboard_screen.dart logic
    bool hasConnectorFilter =
        _selectedConnectorFilters.values.any((selected) => selected);
    bool hasPowerOutputFilter = _selectedPowerOutput != 'All';

    if (!hasConnectorFilter && !hasPowerOutputFilter) {
      // No filters selected, reload unfiltered data
      debugPrint('🔍 No filters selected, fetching all stations from server');
      await _loadNearestStations();
      return;
    }

    debugPrint('🔍 Applying server-side filters...');

    setState(() {
      _isLoadingStations = true;
      _isSearching = false;
    });

    try {
      final LatLng locationToFetch = _userActivityLocation ??
          LatLng(_currentLatitude ?? _indiaLatitude,
              _currentLongitude ?? _indiaLongitude);

      // Prepare filter parameters
      String? powerOutput =
          _selectedPowerOutput != 'All' ? _selectedPowerOutput : null;
      // Convert display names to standard codes for API
      List<String> selectedDisplayNames = _selectedConnectorFilters.entries
          .where((entry) => entry.value)
          .map((entry) => entry.key)
          .toList();

      List<String>? selectedConnectorStandards = selectedDisplayNames.isNotEmpty
          ? FilterDialog.getStandardCodes(selectedDisplayNames)
          : null;

      // Debug logging for filter parameters
      debugPrint('🔍 Power Output Filter: $powerOutput');
      debugPrint('🔍 Selected Connector Filters: $_selectedConnectorFilters');
      debugPrint('🔍 Connector Standards for API: $selectedConnectorStandards');
      debugPrint('🔍 Selected Display Names: $selectedDisplayNames');

      if (selectedConnectorStandards?.isEmpty == true) {
        selectedConnectorStandards = null;
        debugPrint('🔍 No connector filters selected, setting to null');
      }

      // Make API call with filter parameters using connectivity monitor - matching dashboard_screen.dart
      final apiResponse = await _connectivityMonitor.executeApiCall(
        () => _stationRepository.getNearestStations(
          locationToFetch.latitude,
          locationToFetch.longitude,
          radius: _searchRadius,
          powerOutput: powerOutput,
          connectorStandards: selectedConnectorStandards,
        ),
        context: mounted ? context : null,
        errorMessage: 'Unable to apply filters. Please check your connection.',
        showErrorOnFailure: false,
      );

      debugPrint(
          '🔍 API Response: success=${apiResponse.success}, data count=${apiResponse.data?.length ?? 0}');

      if (apiResponse.success == true && apiResponse.data != null && mounted) {
        final formattedStations = apiResponse.data!.map((station) {
          String status = station.status ?? 'Available';
          final connectorTypeStr = station.getConnectorTypesString();
          final connectorTypes = station.getConnectorTypes();

          // Convert ConnectorType objects to Maps for UI compatibility
          final connectorTypesMap = connectorTypes
              .map((connectorType) => {
                    'name': connectorType.name,
                    'icon': connectorType.icon,
                    'power': connectorType.power,
                    'power_type': connectorType.power,
                    'guns': connectorType.guns,
                    'available_guns': connectorType.availableGuns,
                  })
              .toList();

          double distance = station.distance ?? 0.0;

          return {
            'id': station.stationId.toString(),
            'name': station.name,
            'latitude': station.latitude,
            'longitude': station.longitude,
            'address': station.address,
            'city': station.city,
            'uid': station.uid,
            'distance': distance,
            'status': status,
            'availability': status,
            'connectorType': connectorTypeStr,
            'connectorTypes': connectorTypesMap,
            'freeGuns': status == 'Available' ? 1 : 0,
            'mapPinUrl': status == 'Available'
                ? 'https://api2.eeil.online/mapicons/ecoplug_available.png'
                : (status == 'In Use'
                    ? 'https://api2.eeil.online/mapicons/ecoplug_charging.png'
                    : 'https://api2.eeil.online/mapicons/ecoplug_unavailable.png'),
            'focusedMapPinUrl':
                'https://api2.eeil.online/mapicons/ecoplug_focus.png',
          };
        }).toList();

        setState(() {
          _filteredStations = formattedStations;
          _stations = formattedStations; // Update base stations too
          _errorMessage = null;
          _isLoadingStations = false;
        });

        // Reset page controller to first station
        if (_pageController.hasClients) {
          _pageController.animateToPage(
            0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }

        // Reset focus to first station after filtering
        if (_filteredStations.isNotEmpty) {
          final firstStationId = _filteredStations[0]['id'].toString();
          debugPrint(
              '🎯 Resetting focus to first filtered station: $firstStationId');
          _currentFocusedStationId = null; // Clear current focus first
          _updateFocusedMarker(firstStationId);
        }

        debugPrint(
            '✅ Filters applied successfully: ${_filteredStations.length} stations');
      } else {
        // Generate user-friendly message based on selected filters
        String filterMessage = _generateFilterFeedbackMessage();

        setState(() {
          _filteredStations = [];
          _stations = [];
          _errorMessage = filterMessage;
        });
        debugPrint(
            '❌ Server-side filtering returned no results: ${apiResponse.message}');
      }
    } catch (e) {
      setState(() {
        _filteredStations = [];
        _stations = [];
        _isLoadingStations = false;
        _errorMessage = 'Error applying filters. Please try again.';
      });
      debugPrint('❌ Error during server-side filtering: $e');
    }
  }

  void _openDirectionsToStation(Map<String, dynamic> station) {
    final stationLat = station['latitude'] as double? ?? 0.0;
    final stationLng = station['longitude'] as double? ?? 0.0;
    final stationName = station['name']?.toString() ?? 'Charging Station';
    final stationAddress = station['address']?.toString() ?? '';

    debugPrint('🎯 Station card tapped: $stationName');
    debugPrint('📍 Coordinates: $stationLat, $stationLng');
    debugPrint('🔌 Connector types: ${station['connectorTypes']}');

    // Focus map on station location with higher zoom level for better visibility
    if (stationLat != 0.0 &&
        stationLng != 0.0 &&
        _googleMapKey.currentState != null) {
      _googleMapKey.currentState!.focusOnLocation(stationLat, stationLng, 17.0);
      debugPrint(
          '🗺️ Focused map on station location: $stationLat, $stationLng');
    }

    // Open Google Maps with directions
    _openGoogleMapsDirections(
        stationLat, stationLng, stationName, stationAddress);
  }

  Future<void> _openGoogleMapsDirections(
      double lat, double lng, String stationName, String address) async {
    try {
      // Create Google Maps URL with directions
      final String googleMapsUrl =
          'https://www.google.com/maps/dir/?api=1&destination=$lat,$lng&destination_place_id=&travelmode=driving';

      // Alternative URL with place name for better user experience
      final String encodedStationName = Uri.encodeComponent(stationName);
      final String encodedAddress = Uri.encodeComponent(address);
      final String googleMapsUrlWithName =
          'https://www.google.com/maps/dir/?api=1&destination=$encodedStationName,$encodedAddress&destination_place_id=&travelmode=driving';

      final Uri uri =
          Uri.parse(address.isNotEmpty ? googleMapsUrlWithName : googleMapsUrl);

      debugPrint('🗺️ Opening Google Maps: $uri');

      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        // Fallback to basic Google Maps URL
        final Uri fallbackUri =
            Uri.parse('https://maps.google.com/?q=$lat,$lng');
        if (await canLaunchUrl(fallbackUri)) {
          await launchUrl(fallbackUri, mode: LaunchMode.externalApplication);
        } else {
          throw 'Could not launch Google Maps';
        }
      }
    } catch (e) {
      debugPrint('❌ Error opening Google Maps: $e');
      // Error opening maps - user will see the error in the UI state
    }
  }

  void _navigateToStationDetails(Map<String, dynamic> station) {
    final String extractedUid = station['uid']?.toString() ?? '';

    debugPrint('🔒 Navigating to station details - UID: $extractedUid');

    if (extractedUid.isNotEmpty) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => StationDetailsPage(uid: extractedUid),
        ),
      );
    } else {
      debugPrint('❌ Station UID not available for navigation');
      // Station details not available - user will see this in the UI
    }
  }

  // Build modern neon dark green battery icon for top-left position - copied from dashboard_screen.dart
  Widget _buildActiveSessionsBatteryIcon() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Modern neon dark green color palette
    const neonGreen = Color(0xFF00FF41); // Bright neon green
    const darkGreen = Color(0xFF0D4F3C); // Dark green base
    const glowGreen = Color(0xFF00CC33); // Glow effect green

    return Tooltip(
      message: 'Active Charging Sessions',
      child: Material(
        elevation: 8,
        shadowColor: isDarkMode
            ? neonGreen.withValues(alpha: 0.3)
            : Colors.black.withValues(alpha: 0.2),
        shape: const CircleBorder(),
        clipBehavior: Clip.antiAlias,
        child: InkWell(
          onTap: _navigateToActiveSessions,
          customBorder: const CircleBorder(),
          splashColor: neonGreen.withValues(alpha: 0.2),
          highlightColor: glowGreen.withValues(alpha: 0.1),
          child: Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [
                  darkGreen,
                  darkGreen.withValues(alpha: 0.8),
                  Colors.black.withValues(alpha: 0.9),
                ],
                stops: const [0.0, 0.7, 1.0],
              ),
              border: Border.all(
                color: neonGreen.withValues(alpha: 0.6),
                width: 1.5,
              ),
              boxShadow: [
                // Neon glow effect
                BoxShadow(
                  color: neonGreen.withValues(alpha: 0.4),
                  blurRadius: 12,
                  spreadRadius: 3,
                ),
                // Inner glow
                BoxShadow(
                  color: glowGreen.withValues(alpha: 0.3),
                  blurRadius: 6,
                  spreadRadius: 1,
                ),
                // Depth shadow
                BoxShadow(
                  color: isDarkMode
                      ? Colors.black.withValues(alpha: 0.5)
                      : Colors.black.withValues(alpha: 0.2),
                  blurRadius: 8,
                  spreadRadius: 1,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Modern battery icon with neon effect
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        neonGreen.withValues(alpha: 0.1),
                        Colors.transparent,
                      ],
                      stops: const [0.0, 1.0],
                    ),
                  ),
                  child: Icon(
                    Icons.battery_charging_full,
                    color: neonGreen,
                    size: 28,
                    shadows: [
                      Shadow(
                        color: neonGreen.withValues(alpha: 0.8),
                        blurRadius: 4,
                      ),
                      Shadow(
                        color: glowGreen.withValues(alpha: 0.6),
                        blurRadius: 8,
                      ),
                    ],
                  ),
                ),
                // Pulsing neon animation overlay
                AnimatedBuilder(
                  animation: _batteryPulseAnimation,
                  builder: (context, child) {
                    final pulseValue = _batteryPulseAnimation.value;
                    return Container(
                      width: 56 + (pulseValue * 12),
                      height: 56 + (pulseValue * 12),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: neonGreen.withValues(
                            alpha: 0.4 * (1 - pulseValue),
                          ),
                          width: 2,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: neonGreen.withValues(
                              alpha: 0.3 * (1 - pulseValue),
                            ),
                            blurRadius: 8 + (pulseValue * 4),
                            spreadRadius: pulseValue * 2,
                          ),
                        ],
                      ),
                    );
                  },
                ),
                // Additional inner pulse for enhanced neon effect
                AnimatedBuilder(
                  animation: _batteryPulseAnimation,
                  builder: (context, child) {
                    final innerPulse = _batteryPulseAnimation.value * 0.5;
                    return Container(
                      width: 40 + (innerPulse * 8),
                      height: 40 + (innerPulse * 8),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: glowGreen.withValues(
                            alpha: 0.2 * (1 - innerPulse),
                          ),
                          width: 1,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Navigate to active sessions screen
  void _navigateToActiveSessions() {
    Navigator.pushNamed(context, '/active-sessions');
  }

  Widget _buildConnectorTypesSection(Map<String, dynamic> station) {
    // Extract connector types from the API structure
    final dynamic types = station['types'] ?? station['connectorTypes'];

    debugPrint('🔌 Building connector section for: ${station['name']}');
    debugPrint('🔌 Raw types data: $types');
    debugPrint('🔌 Types data type: ${types.runtimeType}');

    if (types == null) {
      debugPrint('🔌 No connector types found, using fallback');
      return _buildFallbackConnectorSection();
    }

    List<Map<String, dynamic>> connectors = [];

    // Handle both Map and List formats from API
    if (types is Map) {
      // API format: "types": {"0": {...}, "1": {...}}
      connectors = types.values.whereType<Map<String, dynamic>>().toList();
      debugPrint('🔌 Processed Map format: ${connectors.length} connectors');
    } else if (types is List) {
      // Handle List of Maps (converted from ConnectorType objects)
      connectors = types.whereType<Map<String, dynamic>>().toList();
      debugPrint('🔌 Processed List format: ${connectors.length} connectors');
    }

    if (connectors.isEmpty) {
      debugPrint('🔌 No valid connectors found, using fallback');
      return _buildFallbackConnectorSection();
    }

    debugPrint('🔌 Final connectors: $connectors');

    // Adopt station list sheet design - simple row without container box
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(2), // Reduced for compact layout
          decoration: BoxDecoration(
            color:
                const Color(0xFF3D7AF5).withAlpha(26), // Same as station list
            borderRadius: BorderRadius.circular(4),
          ),
          child: const Icon(
            Icons.bolt,
            color: Color(0xFF3D7AF5),
            size: 16,
          ),
        ),
        const SizedBox(width: 4),
        Expanded(
          child: Text(
            _getConnectorTypesText(connectors),
            style: const TextStyle(
              color: Color(0xFF3D7AF5),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  /// Extract connector types text from connectors list (matching station list sheet logic)
  String _getConnectorTypesText(List<Map<String, dynamic>> connectors) {
    if (connectors.isEmpty) {
      return 'Various';
    }

    Set<String> connectorNames = {};
    for (var connector in connectors) {
      final name = connector['name']?.toString();
      if (name != null && name.isNotEmpty) {
        connectorNames.add(name);
      }
    }

    return connectorNames.isNotEmpty ? connectorNames.join(', ') : 'Various';
  }

  Widget _buildFallbackConnectorSection() {
    // Adopt station list sheet design for fallback as well
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: const Color(0xFF3D7AF5).withAlpha(26),
            borderRadius: BorderRadius.circular(4),
          ),
          child: const Icon(
            Icons.bolt,
            color: Color(0xFF3D7AF5),
            size: 16,
          ),
        ),
        const SizedBox(width: 4),
        const Expanded(
          child: Text(
            'Charging Available',
            style: TextStyle(
              color: Color(0xFF3D7AF5),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
