import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dashboard_screen.dart';
import 'dashboard_horizontal_cards.dart';

/// Dashboard switcher to toggle between original and horizontal card variants
class DashboardSwitcher extends StatefulWidget {
  const DashboardSwitcher({super.key});

  @override
  State<DashboardSwitcher> createState() => _DashboardSwitcherState();
}

class _DashboardSwitcherState extends State<DashboardSwitcher> {
  bool _useHorizontalCards = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDashboardPreference();
  }

  Future<void> _loadDashboardPreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      setState(() {
        _useHorizontalCards = prefs.getBool('use_horizontal_cards') ?? false;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading dashboard preference: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveDashboardPreference(bool useHorizontalCards) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('use_horizontal_cards', useHorizontalCards);
    } catch (e) {
      debugPrint('Error saving dashboard preference: $e');
    }
  }

  void _toggleDashboard() {
    setState(() {
      _useHorizontalCards = !_useHorizontalCards;
    });
    _saveDashboardPreference(_useHorizontalCards);
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      body: Stack(
        children: [
          // Current dashboard variant
          _useHorizontalCards 
              ? const DashboardHorizontalCards()
              : const DashboardScreen(),
          
          // Dashboard switcher button (for testing/management)
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            right: 16,
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface.withAlpha(230),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(25),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: IconButton(
                onPressed: _toggleDashboard,
                icon: Icon(
                  _useHorizontalCards 
                      ? Icons.view_list 
                      : Icons.view_carousel,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                tooltip: _useHorizontalCards 
                    ? 'Switch to Bottom Sheet View'
                    : 'Switch to Horizontal Cards View',
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Static method to get current dashboard preference
class DashboardPreferences {
  static Future<bool> getUseHorizontalCards() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('use_horizontal_cards') ?? false;
    } catch (e) {
      debugPrint('Error getting dashboard preference: $e');
      return false;
    }
  }

  static Future<void> setUseHorizontalCards(bool value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('use_horizontal_cards', value);
    } catch (e) {
      debugPrint('Error setting dashboard preference: $e');
    }
  }
}
