import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:ecoplug/models/station.dart';
import 'package:ecoplug/core/api/api_service.dart';
import 'package:ecoplug/services/api_bridge.dart';
import 'package:ecoplug/screens/station/station_details_page.dart';
import 'package:ecoplug/services/location_service.dart';
import '../../utils/dark_mode_wrapper.dart';
import '../../utils/app_themes.dart';
import '../dashboard/filter_dialog.dart'; // Import FilterDialog
import 'dart:async'; // Import Timer

class StationListPage extends StatefulWidget {
  const StationListPage({super.key});

  @override
  StationListPageState createState() => StationListPageState();
}

class StationListPageState extends State<StationListPage> {
  // Current search text.
  String _searchText = '';

  // API service instance
  final ApiService _apiService = ApiService();
  final ApiBridge _apiBridge = ApiBridge();
  final LocationService _locationService = LocationService();

  // List of stations
  List<Station> _stations = [];
  bool _isLoading = true;
  bool _isLoadingMore = false; // Track loading more stations
  bool _hasMore = true; // Track if more stations are available
  int _currentPage = 1;
  final int _limit = 20; // Number of stations per page
  String? _errorMessage;

  // User location for API calls
  double? _userLatitude;
  double? _userLongitude;

  // Scroll controller for pagination
  final ScrollController _scrollController = ScrollController();

  // Debouncer for search input
  Timer? _debounce;

  // Filter state for connector types and power output (same as dashboard)
  final Map<String, bool> _selectedConnectorFilters = {};
  String _selectedPowerOutput = 'All';

  // Initialize connector filters
  void _initializeConnectorFilters() {
    _selectedConnectorFilters.clear();
    for (String connectorType in FilterDialog.availableConnectorTypes) {
      _selectedConnectorFilters[connectorType] = false;
    }
  }

  // Check if any filters are currently active
  bool get _hasActiveFilters {
    bool hasConnectorFilter =
        _selectedConnectorFilters.values.any((selected) => selected);
    bool hasPowerOutputFilter = _selectedPowerOutput != 'All';
    return hasConnectorFilter || hasPowerOutputFilter;
  }

  @override
  void initState() {
    super.initState();
    _initializeConnectorFilters(); // Initialize filter state
 
    _initializeLocationAndLoadStations();

    // Add listener for scroll controller
    _scrollController.addListener(() {
      // Check if scrolled to the bottom and not already loading more
      if (_scrollController.position.pixels >=
              _scrollController.position.maxScrollExtent - 200 &&
          !_isLoadingMore &&
          _hasMore &&
          _searchText.isEmpty) {
        // Only paginate when not searching
        _loadStations();
      }
    });
  }

  /// Initialize user location and load stations
  Future<void> _initializeLocationAndLoadStations() async {
    debugPrint('🌍 StationListPage: Starting location initialization...');
    try {
      // Get user location first
      final position = await _locationService.getCurrentLocation();
      if (position != null) {
        setState(() {
          _userLatitude = position.latitude;
          _userLongitude = position.longitude;
        });
        debugPrint(
            '🌍 StationListPage: Location obtained successfully - lat: $_userLatitude, lng: $_userLongitude');
      } else {
        debugPrint(
            '🌍 StationListPage: Location service returned null position');
      }
    } catch (e) {
      debugPrint('🌍 StationListPage: Error getting user location: $e');
    }

    // Debug log final location state before loading stations
    debugPrint(
        '🌍 StationListPage: Final location state - lat: $_userLatitude, lng: $_userLongitude');

    // Load stations regardless of location success
    _loadStations(isInitialLoad: true);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  Future<void> _loadStations(
      {bool isInitialLoad = false, bool isSearch = false}) async {
    if (_isLoadingMore || (isSearch && _isLoading)) {
      return; // Prevent multiple simultaneous loads
    }

    setState(() {
      if (isInitialLoad || isSearch) {
        _isLoading = true;
        _stations.clear(); // Clear stations for initial load or new search
        _currentPage = 1;
        _hasMore = true;
      } else {
        _isLoadingMore = true;
      }
      _errorMessage = null;
    });

    try {
      List<Station> newStations;
      if (_searchText.isNotEmpty) {
        // Use API search if search text is present
        final searchResponse =
            await _apiService.searchStationsByName(_searchText);
        if (searchResponse.success && searchResponse.data != null) {
          newStations = searchResponse.data!.map((searchStation) {
            // Create connectors from types
            final connectors = searchStation.types
                    ?.map((type) => Connector(
                          id: '${searchStation.uid ?? searchStation.stationId}_${type.name}',
                          name: type.name ?? 'Connector type not specified',
                          type: type.name ?? '',
                          power: '',
                          availableGuns: 1,
                          status: searchStation.status ?? 'Available',
                          icon: type.icon,
                        ))
                    .toList() ??
                [
                  Connector(
                    id: '${searchStation.uid ?? searchStation.stationId}_default',
                    name: 'Standard Connector',
                    type: 'Standard',
                    power: '',
                    availableGuns: 1,
                    status: 'Available',
                  )
                ];

            return Station(
              id: (searchStation.uid?.isNotEmpty == true)
                  ? searchStation.uid!
                  : searchStation.stationId?.toString() ?? '',
              name: searchStation.name ?? 'Station name not specified',
              address: searchStation.address ?? 'Address not specified',
              latitude: searchStation.latitude ?? 0.0,
              longitude: searchStation.longitude ?? 0.0,
              distance: 0.0, // Search results don't include distance
              status: searchStation.status ?? '',
              rating: 0.0,
              reviews: 0,
              images: [],
              evses: [],
              connectors: connectors,
              uid: searchStation.uid,
              city: searchStation.city,
              state: null,
              types: searchStation.types?.map((t) => t.toJson()).toList(),
            );
          }).toList();
        } else {
          newStations = [];
        }
        _hasMore = false; // API search doesn't support pagination
      } else {
        // Debug log the coordinates being passed to API
        debugPrint(
            '🚀 StationListPage: Making API call with coordinates - lat: $_userLatitude, lng: $_userLongitude');
        debugPrint('🚀 StationListPage: Page: $_currentPage, Limit: $_limit');

        // Prepare filter parameters for server-side filtering
        String? powerOutput =
            _selectedPowerOutput != 'All' ? _selectedPowerOutput : null;

        List<String> selectedConnectorStandards = [];
        bool hasConnectorFilter =
            _selectedConnectorFilters.values.any((selected) => selected);
        if (hasConnectorFilter) {
          // Get selected connector display names
          List<String> selectedDisplayNames = _selectedConnectorFilters.entries
              .where((entry) => entry.value)
              .map((entry) => entry.key)
              .toList();

          // Convert to standard codes for API
          selectedConnectorStandards =
              FilterDialog.getStandardCodes(selectedDisplayNames);
        }

        // Debug log filter parameters
        if (powerOutput != null || selectedConnectorStandards.isNotEmpty) {
          debugPrint(
              '🔍 StationListPage: Applying filters - Power: $powerOutput, Connectors: $selectedConnectorStandards');
        }

        // Enhanced debugging for API comparison with Postman
        debugPrint('🔍 StationListPage: === API CALL COMPARISON ===');
        debugPrint(
            '🔍 StationListPage: Coordinates - lat: $_userLatitude, lng: $_userLongitude');
        debugPrint('🔍 StationListPage: Page: $_currentPage, Limit: $_limit');
        debugPrint(
            '🔍 StationListPage: PowerOutput (null means excluded): $powerOutput');
        debugPrint(
            '🔍 StationListPage: ConnectorStandards: $selectedConnectorStandards');

        // Check authentication status
        try {
          final apiService = _apiService;
          final token = await apiService.getToken();
          debugPrint(
              '🔐 StationListPage: Auth token exists: ${token != null && token.isNotEmpty}');
          if (token != null && token.length > 20) {
            debugPrint(
                '🔐 StationListPage: Auth token preview: ${token.substring(0, 20)}...');
          }
        } catch (e) {
          debugPrint('🔐 StationListPage: Error checking auth token: $e');
        }

        // Construct expected URL for comparison
        final baseUrl = 'https://api2.eeil.online/api/v1/user/station/paginate';
        final params = <String>[];
        params.add('page=$_currentPage');
        params.add('limit=$_limit');
        if (_userLatitude != null && _userLongitude != null) {
          params.add('latitude=$_userLatitude');
          params.add('longitude=$_userLongitude');
        }
        if (powerOutput != null) {
          params.add('power_output=$powerOutput');
        }
        if (selectedConnectorStandards.isNotEmpty) {
          params.add('standard=${selectedConnectorStandards.join(',')}');
        }
        final expectedUrl = '$baseUrl?${params.join('&')}';
        debugPrint('🔍 StationListPage: Expected URL: $expectedUrl');

        // Fetch paginated stations using ApiBridge with user location and filters
        final paginatedResponse = await _apiBridge.getPaginatedStations(
          _currentPage,
          _limit,
          latitude: _userLatitude,
          longitude: _userLongitude,
          powerOutput: powerOutput,
          connectorStandards: selectedConnectorStandards.isNotEmpty
              ? selectedConnectorStandards
              : null,
        );

        debugPrint(
            '🚀 StationListPage: API response received - Success: ${paginatedResponse.success}');
        debugPrint(
            '🚀 StationListPage: API response message: ${paginatedResponse.message}');
        debugPrint(
            '🚀 StationListPage: API response data length: ${paginatedResponse.data?.length ?? 0}');

        // Debug log each station UID for troubleshooting
        if (paginatedResponse.data != null) {
          for (int i = 0; i < paginatedResponse.data!.length; i++) {
            final station = paginatedResponse.data![i];
            debugPrint(
                '🚀 StationListPage: Station $i - Name: ${station.name}, UID: "${station.uid}"');
          }
        }

        // The getPaginatedStations returns ApiResponse<List<Station>>
        if (paginatedResponse.success && paginatedResponse.data != null) {
          final fetchedStations = paginatedResponse.data!;

          // Debug log distance values received in UI
          debugPrint(
              '🎯 StationListPage: Received ${fetchedStations.length} stations');
          for (int i = 0; i < fetchedStations.length && i < 3; i++) {
            final station = fetchedStations[i];
            debugPrint(
                '🎯 StationListPage: Station ${i + 1} - ${station.name}, Distance: ${station.distance}');
          }

          if (isInitialLoad || isSearch) {
            newStations = fetchedStations;
          } else {
            newStations = [..._stations, ...fetchedStations];
          }

          // For now, assume there are more pages if we got a full page
          _hasMore = fetchedStations.length >= _limit;
        } else {
          debugPrint(
              '🚨 StationListPage: API call failed - ${paginatedResponse.message}');

          // Check if it's an authentication error
          if (paginatedResponse.message.contains('401') == true ||
              paginatedResponse.message.contains('Unauthorized') == true) {
            debugPrint(
                '🚨 StationListPage: Authentication error - user needs to log in');
            setState(() {
              _errorMessage =
                  'Please log in to view stations with distance information';
            });
          }

          // Generate user-friendly message for filter results
          if (_hasActiveFilters) {
            setState(() {
              _errorMessage = _generateFilterFeedbackMessage();
            });
          }

          newStations = isInitialLoad ? [] : _stations;
          _hasMore = false;
        }
      }

      setState(() {
        // If it wasn't an initial load or search, the service handles appending.
        // We just need to update our local state reference.
        _stations = newStations;
        if (!isInitialLoad && !isSearch) {
          _currentPage++;
        }
      });
    } catch (e) {
      debugPrint('🚨 StationListPage: Exception in _loadStations: $e');
      debugPrint('🚨 StationListPage: Exception type: ${e.runtimeType}');
      setState(() {
        _errorMessage = "Failed to load stations: $e";
        _hasMore = false; // Stop pagination on error
      });
    } finally {
      setState(() {
        _isLoading = false;
        _isLoadingMore = false;
      });
    }
  }

  // Debounced search function
  void _onSearchChanged(String value) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      if (_searchText != value) {
        // Only trigger search if text actually changed
        setState(() {
          _searchText = value;
        });
        _loadStations(isSearch: true); // Trigger API search
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // NO CLIENT-SIDE FILTERING - Server already handles all filtering
    // Use the stations directly from the server response
    List<Station> filteredStations = _stations;

    // Sorting is still done locally after fetching
    filteredStations.sort((a, b) => a.distance.compareTo(b.distance));

    return DarkModeWrapper(
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Station List'),
          backgroundColor: Theme.of(context)
              .colorScheme
              .surface, // Use theme-adaptive surface color
          foregroundColor: Theme.of(context)
              .colorScheme
              .onSurface, // Use theme-adaptive text color
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: Theme.of(context)
                  .colorScheme
                  .onSurface, // Use theme-adaptive icon color
            ),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        backgroundColor: Theme.of(context)
            .colorScheme
            .surface, // Use theme-adaptive surface color
        body: Column(
          children: [
            // Completely redesigned search bar with no extra layers
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              child: TextField(
                onTap: () {},
                onSubmitted: (_) {},
                onChanged: _onSearchChanged,
                style: TextStyle(
                  color: Theme.of(context)
                      .colorScheme
                      .onSurface, // Use theme-adaptive text color
                  fontSize: 15,
                ),
                decoration: InputDecoration(
                  hintText: 'Search charger, city...',
                  hintStyle: TextStyle(
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withAlpha(153), // 0.6 * 255 = 153
                    fontSize: 15,
                  ),
                  prefixIcon: Icon(
                    Icons.search,
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withAlpha(179), // 0.7 * 255 = 179
                    size: 20,
                  ),
                  suffixIcon: _buildSearchBarSuffixIcons(),
                  filled: true,
                  fillColor: Theme.of(context)
                      .colorScheme
                      .surfaceContainerHighest, // Use theme-adaptive fill color
                  contentPadding:
                      const EdgeInsets.symmetric(vertical: 15, horizontal: 16),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? AppThemes.darkBorder.withAlpha(100)
                          : AppThemes.primaryColor.withAlpha(30),
                      width: 1.5,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? AppThemes.darkBorder.withAlpha(100)
                          : AppThemes.primaryColor.withAlpha(30),
                      width: 1.5,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: AppThemes.primaryColor,
                      width: 2.0,
                    ),
                  ),
                ),
              ),
            ),

            // No local filtering - server handles all filtering via connector/power filters

            // Station list.
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _errorMessage != null
                      ? Center(child: Text(_errorMessage!))
                      : filteredStations.isEmpty
                          ? const Center(child: Text('No stations found'))
                          : ListView.builder(
                              controller:
                                  _scrollController, // Assign scroll controller
                              itemCount: filteredStations.length +
                                  (_isLoadingMore
                                      ? 1
                                      : 0), // Add space for loader
                              itemBuilder: (context, index) {
                                if (index == filteredStations.length) {
                                  // If it's the last item and we're loading more, show indicator
                                  return _buildLoadingIndicator();
                                }
                                final station = filteredStations[index];
                                return _buildStationCard(station);
                              },
                            ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build suffix icons for search bar (filter button + clear button when active)
  Widget _buildSearchBarSuffixIcons() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Clear filters button (cross) - shown when filters are active
        if (_hasActiveFilters)
          Container(
            margin: const EdgeInsets.only(right: 4),
            child: Material(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(8),
              child: InkWell(
                onTap: _clearAllFilters,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? Colors.red.shade800.withAlpha(100)
                        : Colors.red.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isDarkMode
                          ? Colors.red.shade600.withAlpha(150)
                          : Colors.red.shade200,
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    Icons.close_rounded,
                    color:
                        isDarkMode ? Colors.red.shade300 : Colors.red.shade600,
                    size: 18,
                  ),
                ),
              ),
            ),
          ),

        // Filter button
        Container(
          margin: const EdgeInsets.only(right: 8),
          child: Material(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            child: InkWell(
              onTap: () => _showFilterOptions(context),
              borderRadius: BorderRadius.circular(8),
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: _hasActiveFilters
                    ? BoxDecoration(
                        color: isDarkMode
                            ? AppThemes.primaryColor.withAlpha(100)
                            : AppThemes.primaryColor.withAlpha(50),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: AppThemes.primaryColor.withAlpha(150),
                          width: 1,
                        ),
                      )
                    : null,
                child: Icon(
                  Icons.filter_alt_rounded,
                  color: AppThemes.primaryColor,
                  size: 20,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Show FilterDialog for connector types and power output
  void _showFilterOptions(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => FilterDialog(
        selectedConnectorFilters: Map.from(_selectedConnectorFilters),
        selectedPowerOutput: _selectedPowerOutput,
        onApplyFilters: (connectorFilters, powerOutput) {
          setState(() {
            _selectedConnectorFilters.clear();
            _selectedConnectorFilters.addAll(connectorFilters);
            _selectedPowerOutput = powerOutput;
          });

          // Apply filters and reload stations
          _applyFilters();
        },
      ),
    );
  }

  /// Clear all connector and power output filters
  Future<void> _clearAllFilters() async {
    setState(() {
      // Clear connector filters
      for (String key in _selectedConnectorFilters.keys) {
        _selectedConnectorFilters[key] = false;
      }
      // Reset power output filter
      _selectedPowerOutput = 'All';
    });

    // Reload stations without filters
    _loadStations(isInitialLoad: true);

    debugPrint('✅ Station list filters cleared and data reloaded');
  }

  /// Apply filters using server-side filtering
  Future<void> _applyFilters() async {
    // Reset pagination and reload with filters
    setState(() {
      _currentPage = 1;
      _hasMore = true;
    });

    // Reload stations with filters
    _loadStations(isInitialLoad: true);

    debugPrint(
        '🔍 Station list filters applied - reloading with server-side filtering');
  }

  /// Generate user-friendly feedback message when no stations match filters
  String _generateFilterFeedbackMessage() {
    List<String> activeFilters = [];

    // Check power output filter
    if (_selectedPowerOutput != 'All') {
      activeFilters.add('$_selectedPowerOutput power output');
    }

    // Check connector type filters
    List<String> selectedConnectors = _selectedConnectorFilters.entries
        .where((entry) => entry.value)
        .map((entry) => entry.key)
        .toList();

    if (selectedConnectors.isNotEmpty) {
      if (selectedConnectors.length == 1) {
        activeFilters.add('${selectedConnectors.first} connector');
      } else {
        activeFilters.add('${selectedConnectors.join(', ')} connectors');
      }
    }

    if (activeFilters.isEmpty) {
      return 'No stations found';
    } else if (activeFilters.length == 1) {
      return 'No stations found with ${activeFilters.first}';
    } else {
      return 'No stations found with ${activeFilters.join(' and ')}';
    }
  }

  Widget _buildLoadingIndicator() {
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  /// Extract connector types text from station API data
  String _getConnectorTypesText(Station station) {
    // First try to get connector types from the types field (direct API data)
    if (station.types != null && station.types!.isNotEmpty) {
      Set<String> connectorNames = {};

      for (var type in station.types!) {
        if (type is Map<String, dynamic> && type['name'] != null) {
          connectorNames.add(type['name'].toString());
        } else if (type is Map && type['name'] != null) {
          connectorNames.add(type['name'].toString());
        }
      }

      if (connectorNames.isNotEmpty) {
        return connectorNames.join(', ');
      }
    }

    // Fallback to connectors field if types is not available
    if (station.connectors.isNotEmpty) {
      return station.connectors
          .map((c) => c.type)
          .where((type) => type.isNotEmpty)
          .toSet()
          .join(', ');
    }

    // Default fallback
    return 'Various';
  }

  /// Get the display text for station status
  String _getStatusDisplayText(String status) {
    final statusLower = status.toLowerCase().trim();

    switch (statusLower) {
      case 'available':
      case 'online':
      case 'operational':
        return 'Available';
      case 'unavailable':
      case 'offline':
      case 'closed':
      case 'out of service':
      case 'maintenance':
        return 'Unavailable';
      case 'in use':
      case 'charging':
      case 'occupied':
      case 'busy':
        return 'In Use';
      case 'reserved':
      case 'booked':
        return 'Reserved';
      case 'faulted':
      case 'error':
        return 'Error';
      case 'preparing':
        return 'Preparing';
      default:
        // Fallback to contains() for partial matches
        if (statusLower.contains('available') ||
            statusLower.contains('online')) {
          return 'Available';
        } else if (statusLower.contains('unavailable') ||
            statusLower.contains('offline') ||
            statusLower.contains('closed')) {
          return 'Unavailable';
        } else if (statusLower.contains('charging') ||
            statusLower.contains('busy') ||
            statusLower.contains('use')) {
          return 'In Use';
        } else if (statusLower.contains('reserved')) {
          return 'Reserved';
        } else if (statusLower.contains('fault') ||
            statusLower.contains('error')) {
          return 'Error';
        } else {
          return status.isNotEmpty ? status : 'Unknown';
        }
    }
  }

  /// Get the color for station status
  Color _getStatusColor(String status) {
    final statusLower = status.toLowerCase().trim();

    switch (statusLower) {
      case 'available':
      case 'online':
      case 'operational':
        return AppThemes.primaryColor;
      case 'unavailable':
      case 'offline':
      case 'closed':
      case 'out of service':
      case 'maintenance':
        return Colors.red;
      case 'in use':
      case 'charging':
      case 'occupied':
      case 'busy':
        return Colors.orange;
      case 'reserved':
      case 'booked':
        return Colors.blue;
      case 'faulted':
      case 'error':
        return Colors.red;
      case 'preparing':
        return Colors.blue;
      default:
        // Fallback to contains() for partial matches
        if (statusLower.contains('available') ||
            statusLower.contains('online')) {
          return AppThemes.primaryColor;
        } else if (statusLower.contains('unavailable') ||
            statusLower.contains('offline') ||
            statusLower.contains('closed')) {
          return Colors.red;
        } else if (statusLower.contains('charging') ||
            statusLower.contains('busy') ||
            statusLower.contains('use')) {
          return Colors.orange;
        } else if (statusLower.contains('reserved')) {
          return Colors.blue;
        } else if (statusLower.contains('fault') ||
            statusLower.contains('error')) {
          return Colors.red;
        } else {
          return Colors.grey;
        }
    }
  }

  Widget _buildStationCard(Station station) {
    final colorScheme = Theme.of(context).colorScheme;

    // Get proper status display text and color
    final statusDisplayText = _getStatusDisplayText(station.status);
    final statusColor = _getStatusColor(station.status);

    debugPrint(
        '🏷️ StationCard: ${station.name} - Raw status: "${station.status}" -> Display: "$statusDisplayText"');

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 2,
      color: colorScheme.surface, // Use theme surface color
      child: InkWell(
        onTap: () {
          // Navigate to station details with proper UID validation
          final stationUid = station.uid ?? station.id;
          if (stationUid.isNotEmpty) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => StationDetailsPage(
                  uid: stationUid,
                  station: station,
                ),
              ),
            );
          } else {
            // Show error if no valid UID
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Station ID not available'),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          station.name,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color:
                                colorScheme.onSurface, // Use theme text color
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          station.address,
                          style: TextStyle(
                            color: colorScheme
                                .onSurfaceVariant, // Use theme secondary text color
                            fontSize: 14,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor.withAlpha(26), // 0.1 * 255 = ~26
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: statusColor.withAlpha(77), // 0.3 * 255 = ~77
                        width: 0.5,
                      ),
                    ),
                    child: Text(
                      statusDisplayText,
                      style: TextStyle(
                        color: statusColor,
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: const Color(0xFF3D7AF5)
                              .withAlpha(26), // 0.1 * 255 = ~26
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Icon(
                          Icons.bolt,
                          color: Color(0xFF3D7AF5),
                          size: 16,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        // Use connector types directly from API data
                        _getConnectorTypesText(station),
                        style: const TextStyle(
                          color: Color(0xFF3D7AF5),
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines:
                            1, // Prevent long lists from wrapping excessively
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                  // Conditional distance display - only show if distance > 0
                  Builder(
                    builder: (context) {
                      // Debug log distance value for each station
                      debugPrint(
                          '🎯 StationCard: ${station.name} - Distance: ${station.distance}');

                      if (station.distance > 0) {
                        debugPrint(
                            '🎯 StationCard: Showing distance UI for ${station.name}');
                        return Row(
                          children: [
                            Icon(
                              Icons.location_on,
                              color: colorScheme
                                  .onSurfaceVariant, // Use theme icon color
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${station.distance.toStringAsFixed(2)} km',
                              style: TextStyle(
                                color: colorScheme
                                    .onSurfaceVariant, // Use theme secondary text color
                                fontSize: 14,
                              ),
                            ),
                          ],
                        );
                      } else {
                        debugPrint(
                            '🎯 StationCard: Hiding distance UI for ${station.name} (distance = ${station.distance})');
                        return const SizedBox.shrink();
                      }
                    },
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: ElevatedButton.icon(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppThemes
                            .primaryColor, // Always use app theme color for charging
                        foregroundColor: Colors.white, // Always use white text
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        minimumSize: const Size.fromHeight(45),
                        elevation: 2, // Always show elevation
                      ),
                      onPressed: () {
                        // Always allow navigation - let backend handle availability validation
                        final stationUid = station.uid ?? station.id;
                        if (stationUid.isNotEmpty) {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => StationDetailsPage(
                                uid: stationUid,
                                station: station,
                              ),
                            ),
                          );
                        } else {
                          // Show error if no valid UID
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Station ID not available'),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      },
                      icon: const Icon(Icons.power,
                          size: 18, color: Colors.white),
                      label: const Text(
                        'Start Charging',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 13,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    flex: 1,
                    child: ElevatedButton.icon(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors
                            .blue, // Use blue color for directions button to match design pattern
                        foregroundColor:
                            Colors.white, // White text on blue background
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        minimumSize: const Size.fromHeight(45),
                        elevation: 2,
                      ),
                      onPressed: () async {
                        final uri = Uri.parse(
                            'https://www.google.com/maps/dir/?api=1&destination=${station.latitude},${station.longitude}');
                        try {
                          if (await canLaunchUrl(uri)) {
                            await launchUrl(uri,
                                mode: LaunchMode.externalApplication);
                          } else {
                            if (mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Could not open directions'),
                                ),
                              );
                            }
                          }
                        } catch (e) {
                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Error: $e'),
                              ),
                            );
                          }
                        }
                      },
                      icon: const Icon(Icons.directions, size: 18),
                      label: const Text(
                        'Directions',
                        style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
