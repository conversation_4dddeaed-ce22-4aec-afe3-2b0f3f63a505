import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../services/saved_stations_service.dart';
import '../../../models/station.dart';
import '../../../widgets/station_card.dart';
import '../../station/station_details_page.dart';

class SavedStationsPage extends StatefulWidget {
  const SavedStationsPage({super.key});

  @override
  State<SavedStationsPage> createState() => _SavedStationsPageState();
}

class _SavedStationsPageState extends State<SavedStationsPage> {
  final SavedStationsService _savedStationsService = SavedStationsService();

  List<Station> _savedStations = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadSavedStations();
  }

  /// Load saved stations from API
  Future<void> _loadSavedStations() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final stations = await _savedStationsService.getSavedStations();

      setState(() {
        _savedStations = stations;
        _isLoading = false;
      });

      debugPrint('✅ Loaded ${stations.length} saved stations');
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load saved stations: $e';
        _isLoading = false;
      });
      debugPrint('❌ Error loading saved stations: $e');
    }
  }

  /// Handle pull-to-refresh
  Future<void> _onRefresh() async {
    HapticFeedback.lightImpact();
    await _loadSavedStations();
  }

  /// Navigate to station details
  void _navigateToStationDetails(Station station) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StationDetailsPage(
          uid: station.uid!,
          station: station,
        ),
      ),
    ).then((_) {
      // Refresh the list when returning from station details
      // in case bookmark status changed
      _loadSavedStations();
    });
  }

  /// Remove station from bookmarks
  Future<void> _removeBookmark(Station station) async {
    // Show confirmation dialog
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Remove Bookmark'),
          content: Text('Remove "${station.name}" from your saved stations?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Remove'),
            ),
          ],
        );
      },
    );

    if (confirmed == true) {
      HapticFeedback.mediumImpact();

      final success = await _savedStationsService.removeBookmark(station.uid!);

      if (success) {
        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Removed "${station.name}" from saved stations'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }

        // Refresh the list
        _loadSavedStations();
      } else {
        // Show error message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to remove bookmark. Please try again.'),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Saved Stations',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: isDarkMode ? const Color(0xFF0E0E0E) : Colors.white,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: isDarkMode ? Colors.white : Colors.black,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: isDarkMode ? Colors.white : Colors.black,
            ),
            onPressed: _onRefresh,
            tooltip: 'Refresh',
          ),
        ],
      ),
      backgroundColor: isDarkMode ? const Color(0xFF0E0E0E) : Colors.grey.shade50,
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_errorMessage != null) {
      return _buildErrorState();
    }

    if (_savedStations.isEmpty) {
      return _buildEmptyState();
    }

    return _buildStationsList();
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            'Loading saved stations...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: isDarkMode ? Colors.red.shade300 : Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Error Loading Stations',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadSavedStations,
              icon: const Icon(Icons.refresh),
              label: const Text('Try Again'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bookmark_border,
              size: 80,
              color: isDarkMode ? Colors.grey.shade600 : Colors.grey.shade400,
            ),
            const SizedBox(height: 24),
            Text(
              'No Saved Stations',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'You haven\'t saved any charging stations yet.\nTap the bookmark icon on any station to save it here.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade600,
                height: 1.5,
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(Icons.explore),
              label: const Text('Explore Stations'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStationsList() {
    return RefreshIndicator(
      onRefresh: _onRefresh,
      child: Column(
        children: [
          // Header with count
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Icon(
                  Icons.bookmark,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  '${_savedStations.length} Saved Station${_savedStations.length == 1 ? '' : 's'}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black87,
                  ),
                ),
                const Spacer(),
                if (_savedStations.isNotEmpty)
                  TextButton.icon(
                    onPressed: _showClearAllDialog,
                    icon: const Icon(Icons.clear_all, size: 18),
                    label: const Text('Clear All'),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.red,
                    ),
                  ),
              ],
            ),
          ),
          // Stations list
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.only(bottom: 8),
              itemCount: _savedStations.length,
              itemBuilder: (context, index) {
                final station = _savedStations[index];
                return Dismissible(
                  key: Key(station.uid!),
                  direction: DismissDirection.endToStart,
                  background: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(right: 20),
                    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.delete,
                          color: Colors.white,
                          size: 28,
                        ),
                        SizedBox(height: 4),
                        Text(
                          'Remove',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  confirmDismiss: (direction) async {
                    await _removeBookmark(station);
                    return false; // Don't auto-dismiss, we handle it manually
                  },
                  child: StationCard(
                    station: station,
                    onTap: () => _navigateToStationDetails(station),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Show dialog to clear all bookmarks
  Future<void> _showClearAllDialog() async {
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Clear All Bookmarks'),
          content: const Text('Are you sure you want to remove all saved stations? This action cannot be undone.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Clear All'),
            ),
          ],
        );
      },
    );

    if (confirmed == true) {
      _clearAllBookmarks();
    }
  }

  /// Clear all bookmarks
  Future<void> _clearAllBookmarks() async {
    HapticFeedback.mediumImpact();

    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    int successCount = 0;
    final totalCount = _savedStations.length;

    for (final station in _savedStations) {
      final success = await _savedStationsService.removeBookmark(station.uid!);
      if (success) successCount++;
    }

    // Close loading dialog
    if (mounted) Navigator.of(context).pop();

    // Show result
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Removed $successCount of $totalCount bookmarks'),
          backgroundColor: successCount == totalCount ? Colors.green : Colors.orange,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }

    // Refresh the list
    _loadSavedStations();
  }
}
