 import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:line_icons/line_icons.dart';

class HistoryPage extends StatefulWidget {
  const HistoryPage({super.key});

  @override
  State<HistoryPage> createState() => _HistoryPageState();
}

class _HistoryPageState extends State<HistoryPage>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  String _selectedFilter = 'All';
  final List<String> _filterOptions = ['All', 'Charging', 'Wallet', 'Refund'];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
        CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic));

    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Enhanced mock data with different transaction types
    final List<Map<String, dynamic>> historyData = [
      {
        'id': 'TXN001',
        'stationName': 'Central Mall Charging Station',
        'date': DateTime.now().subtract(const Duration(hours: 2)),
        'cost': 199.50,
        'kwh': 12.5,
        'type': 'Charging',
        'status': 'Completed',
        'icon': LineIcons.lightningBolt,
        'color': const Color(0xFF4CAF50),
      },
      {
        'id': 'TXN002',
        'stationName': 'Wallet Top-up',
        'date': DateTime.now().subtract(const Duration(days: 1, hours: 3)),
        'cost': 500.00,
        'kwh': 0.0,
        'type': 'Wallet',
        'status': 'Completed',
        'icon': LineIcons.wallet,
        'color': const Color(0xFF2196F3),
      },
      {
        'id': 'TXN003',
        'stationName': 'Highway Plaza Charger',
        'date': DateTime.now().subtract(const Duration(days: 3, hours: 1)),
        'cost': 90.00,
        'kwh': 6.0,
        'type': 'Charging',
        'status': 'Completed',
        'icon': LineIcons.lightningBolt,
        'color': const Color(0xFF4CAF50),
      },
      {
        'id': 'TXN004',
        'stationName': 'Transaction Refund',
        'date': DateTime.now().subtract(const Duration(days: 4)),
        'cost': 25.00,
        'kwh': 0.0,
        'type': 'Refund',
        'status': 'Refunded',
        'icon': LineIcons.undo,
        'color': const Color(0xFFFF9800),
      },
      {
        'id': 'TXN005',
        'stationName': 'E Plug energy Office DC Charger',
        'date': DateTime.now().subtract(const Duration(days: 5)),
        'cost': 210.75,
        'kwh': 14.3,
        'type': 'Charging',
        'status': 'Completed',
        'icon': LineIcons.lightningBolt,
        'color': const Color(0xFF4CAF50),
      },
    ];

    // Filter data based on selected filter
    final filteredData = _selectedFilter == 'All'
        ? historyData
        : historyData.where((item) => item['type'] == _selectedFilter).toList();

    // Calculate summary statistics
    final totalSessions =
        historyData.where((item) => item['type'] == 'Charging').length;
    final totalCost = historyData.fold<double>(
        0.0, (sum, item) => sum + (item['cost'] as double));
    final totalKwh = historyData.fold<double>(
        0.0, (sum, item) => sum + (item['kwh'] as double));

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: const Text(
          'Billing History',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarBrightness: isDarkMode ? Brightness.dark : Brightness.light,
        ),
        actions: [
          IconButton(
            icon: Icon(
              LineIcons.filter,
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
            onPressed: () => _showFilterBottomSheet(context),
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: isDarkMode
                ? [
                    const Color(0xFF1A1A1A),
                    const Color(0xFF121212),
                  ]
                : [
                    const Color(0xFFF8F9FA),
                    const Color(0xFFE3F2FD),
                  ],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: RefreshIndicator(
                onRefresh: _handleRefresh,
                color: const Color(0xFF4CAF50),
                child: CustomScrollView(
                  physics: const BouncingScrollPhysics(),
                  slivers: [
                    // Header spacing
                    const SliverToBoxAdapter(child: SizedBox(height: 20)),

                    // Summary cards
                    SliverToBoxAdapter(
                      child: _buildSummarySection(
                          totalSessions, totalKwh, totalCost, isDarkMode),
                    ),

                    // Filter chips
                    SliverToBoxAdapter(
                      child: _buildFilterChips(isDarkMode),
                    ),

                    // Transaction list
                    SliverPadding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      sliver: SliverList(
                        delegate: SliverChildBuilderDelegate(
                          (context, index) {
                            if (index == 0) {
                              return _buildSectionHeader(
                                  'Recent Transactions', isDarkMode);
                            }
                            final item = filteredData[index - 1];
                            return _buildTransactionCard(
                                item, index - 1, isDarkMode);
                          },
                          childCount: filteredData.length + 1,
                        ),
                      ),
                    ),

                    // Bottom spacing
                    const SliverToBoxAdapter(child: SizedBox(height: 100)),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Handle refresh indicator
  Future<void> _handleRefresh() async {
    await Future.delayed(const Duration(seconds: 1));
    // In a real app, you would refresh data from the API here
    if (mounted) {
      setState(() {
        // Refresh the data
      });
    }
  }

  // Build summary section with stats cards
  Widget _buildSummarySection(
      int totalSessions, double totalKwh, double totalCost, bool isDarkMode) {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryCard(
              'Sessions',
              totalSessions.toString(),
              LineIcons.lightningBolt,
              const Color(0xFF4CAF50),
              isDarkMode,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildSummaryCard(
              'Energy',
              '${totalKwh.toStringAsFixed(1)} kWh',
              Icons.battery_charging_full,
              const Color(0xFF2196F3),
              isDarkMode,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildSummaryCard(
              'Total Spent',
              '₹${totalCost.toStringAsFixed(0)}',
              LineIcons.wallet,
              const Color(0xFFFF9800),
              isDarkMode,
            ),
          ),
        ],
      ),
    );
  }

  // Build individual summary card
  Widget _buildSummaryCard(
      String title, String value, IconData icon, Color color, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color.withOpacity(0.8),
            color,
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              color: Colors.white.withOpacity(0.9),
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // Build filter chips
  Widget _buildFilterChips(bool isDarkMode) {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _filterOptions.length,
        itemBuilder: (context, index) {
          final option = _filterOptions[index];
          final isSelected = _selectedFilter == option;

          return Container(
            margin: const EdgeInsets.only(right: 8),
            child: FilterChip(
              label: Text(
                option,
                style: TextStyle(
                  color: isSelected
                      ? Colors.white
                      : (isDarkMode ? Colors.white70 : Colors.black87),
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedFilter = option;
                });
              },
              backgroundColor: isDarkMode ? Colors.grey[800] : Colors.grey[200],
              selectedColor: const Color(0xFF4CAF50),
              checkmarkColor: Colors.white,
              elevation: isSelected ? 4 : 1,
              pressElevation: 8,
            ),
          );
        },
      ),
    );
  }

  // Build section header
  Widget _buildSectionHeader(String title, bool isDarkMode) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: isDarkMode ? Colors.white : Colors.black87,
        ),
      ),
    );
  }

  // Build transaction card
  Widget _buildTransactionCard(
      Map<String, dynamic> item, int index, bool isDarkMode) {
    final stationName = item['stationName'] as String;
    final cost = item['cost'] as double;
    final kwh = item['kwh'] as double;
    final date = item['date'] as DateTime;
    final type = item['type'] as String;
    final status = item['status'] as String;
    final icon = item['icon'] as IconData;
    final color = item['color'] as Color;

    final dateStr = _formatDate(date);
    final timeStr = _formatTime(date);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[900] : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(isDarkMode ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            // Handle transaction tap
            _showTransactionDetails(item);
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Icon
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),

                // Details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        stationName,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: isDarkMode ? Colors.white : Colors.black87,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '$dateStr • $timeStr',
                        style: TextStyle(
                          fontSize: 12,
                          color:
                              isDarkMode ? Colors.grey[400] : Colors.grey[600],
                        ),
                      ),
                      if (kwh > 0) ...[
                        const SizedBox(height: 4),
                        Text(
                          '${kwh.toStringAsFixed(1)} kWh',
                          style: TextStyle(
                            fontSize: 12,
                            color: isDarkMode
                                ? Colors.grey[400]
                                : Colors.grey[600],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                // Amount and status
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      type == 'Refund'
                          ? '-₹${cost.toStringAsFixed(2)}'
                          : '₹${cost.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: type == 'Refund'
                            ? Colors.orange
                            : (isDarkMode ? Colors.white : Colors.black87),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: color.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        status,
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                          color: color,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Show filter bottom sheet
  void _showFilterBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey[900]
              : Colors.white,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[400],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'Filter Transactions',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            ...List.generate(_filterOptions.length, (index) {
              final option = _filterOptions[index];
              return ListTile(
                leading: Radio<String>(
                  value: option,
                  groupValue: _selectedFilter,
                  onChanged: (value) {
                    setState(() {
                      _selectedFilter = value!;
                    });
                    Navigator.pop(context);
                  },
                  activeColor: const Color(0xFF4CAF50),
                ),
                title: Text(option),
                onTap: () {
                  setState(() {
                    _selectedFilter = option;
                  });
                  Navigator.pop(context);
                },
              );
            }),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // Show transaction details
  void _showTransactionDetails(Map<String, dynamic> transaction) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Transaction Details'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('ID: ${transaction['id']}'),
            const SizedBox(height: 8),
            Text('Station: ${transaction['stationName']}'),
            const SizedBox(height: 8),
            Text('Type: ${transaction['type']}'),
            const SizedBox(height: 8),
            Text('Status: ${transaction['status']}'),
            const SizedBox(height: 8),
            Text(
                'Amount: ₹${(transaction['cost'] as double).toStringAsFixed(2)}'),
            if ((transaction['kwh'] as double) > 0) ...[
              const SizedBox(height: 8),
              Text(
                  'Energy: ${(transaction['kwh'] as double).toStringAsFixed(1)} kWh'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  // Format date as "25 Mar" or something similar
  String _formatDate(DateTime date) {
    // You can use intl package for localization, but here's a quick example:
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return '${date.day} ${months[date.month - 1]}';
  }

  // Format time as "2:30 PM" for example
  String _formatTime(DateTime date) {
    final hour = date.hour;
    final minute = date.minute.toString().padLeft(2, '0');
    final isAm = hour < 12;
    final displayHour = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);

    return '$displayHour:$minute ${isAm ? 'AM' : 'PM'}';
  }
}
