import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

import '../../../utils/app_themes.dart';
import '../../../core/api/api_config.dart';
import '../../../services/auth_manager.dart';

// Vehicle model for API response
class VehicleData {
  final int id;
  final String name;
  final String? variants;
  final String batteryCapacity;
  final String vehicleImage;
  final String? brandName;
  final bool isSelected;

  VehicleData({
    required this.id,
    required this.name,
    this.variants,
    required this.batteryCapacity,
    required this.vehicleImage,
    this.brandName,
    this.isSelected = false,
  });

  factory VehicleData.fromJson(Map<String, dynamic> json) {
    return VehicleData(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      variants: json['variants'],
      batteryCapacity: json['battery_capacity'] ?? '',
      vehicleImage: json['vehicle_image'] ?? '',
      brandName: json['brand_name'], // This field is only in "All" array
    );
  }

  VehicleData copyWith({bool? isSelected}) {
    return VehicleData(
      id: id,
      name: name,
      variants: variants,
      batteryCapacity: batteryCapacity,
      vehicleImage: vehicleImage,
      brandName: brandName,
      isSelected: isSelected ?? this.isSelected,
    );
  }
}

// API Response model
class VehicleApiResponse {
  final Map<String, List<VehicleData>> data;
  final bool success;

  VehicleApiResponse({
    required this.data,
    required this.success,
  });

  factory VehicleApiResponse.fromJson(Map<String, dynamic> json) {
    final Map<String, List<VehicleData>> vehicleData = {};

    if (json['data'] != null) {
      final data = json['data'] as Map<String, dynamic>;
      data.forEach((key, value) {
        if (value is List) {
          vehicleData[key] = value
              .map((item) => VehicleData.fromJson(item as Map<String, dynamic>))
              .toList();
        }
      });
    }

    return VehicleApiResponse(
      data: vehicleData,
      success: json['success'] ?? false,
    );
  }
}

class ManageVehiclesPage extends ConsumerStatefulWidget {
  const ManageVehiclesPage({super.key});

  @override
  ConsumerState<ManageVehiclesPage> createState() => _ManageVehiclesPageState();
}

class _ManageVehiclesPageState extends ConsumerState<ManageVehiclesPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  VehicleApiResponse? _vehicleResponse;
  bool _isLoading = true;
  String? _errorMessage;
  int? _selectedVehicleId; // Changed to single selection
  VehicleData? _selectedVehicle; // Store selected vehicle data
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _licensePlateController = TextEditingController();
  String _searchQuery = '';
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _fetchVehicles();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _licensePlateController.dispose();
    super.dispose();
  }

  Future<void> _fetchVehicles() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final authManager = AuthManager();
      final token = await authManager.getToken();

      final url = '${ApiConfig.apiUrl}${ApiConfig.vehicleCatalog}';
      debugPrint('🚗 VEHICLES: Fetching from URL: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      debugPrint('🚗 VEHICLES: Response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        debugPrint('🚗 VEHICLES: Response data keys: ${jsonData.keys}');
        debugPrint('🚗 VEHICLES: Success: ${jsonData['success']}');

        final vehicleResponse = VehicleApiResponse.fromJson(jsonData);

        setState(() {
          _vehicleResponse = vehicleResponse;
          _isLoading = false;

          // Initialize tab controller after data is loaded
          _tabController = TabController(
            length: vehicleResponse.data.keys.length,
            vsync: this,
          );
        });
      } else {
        throw Exception('Failed to load vehicles: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load vehicles: $e';
      });
    }
  }

  List<VehicleData> _getFilteredVehicles(List<VehicleData> vehicles) {
    if (_searchQuery.isEmpty) return vehicles;

    return vehicles.where((vehicle) {
      return vehicle.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             (vehicle.brandName?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        centerTitle: true,
        title: const Text(
          'Manage Vehicles',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: isDarkMode ? AppThemes.darkBackground : Colors.white,
        foregroundColor: isDarkMode ? Colors.white : Colors.black87,
        actions: [
          if (_selectedVehicleId != null)
            TextButton(
              onPressed: () => _showLicensePlateBottomSheet(context, isDarkMode),
              child: Text(
                'Add Vehicle',
                style: TextStyle(
                  color: AppThemes.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      backgroundColor: isDarkMode ? AppThemes.darkBackground : Colors.grey.shade50,
      body: _buildBody(isDarkMode),
    );
  }

  Widget _buildBody(bool isDarkMode) {
    if (_isLoading) {
      return _buildLoadingState(isDarkMode);
    }

    if (_errorMessage != null) {
      return _buildErrorState(isDarkMode);
    }

    if (_vehicleResponse == null || _vehicleResponse!.data.isEmpty) {
      return _buildEmptyState(isDarkMode);
    }

    return Column(
      children: [
        _buildSearchBar(isDarkMode),
        _buildTabBar(isDarkMode),
        const SizedBox(height: 8), // Add spacing between tab bar and content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: _vehicleResponse!.data.entries.map((entry) {
              final vehicles = _getFilteredVehicles(entry.value);
              return _buildVehicleGrid(vehicles, isDarkMode);
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingState(bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: AppThemes.primaryColor,
          ),
          const SizedBox(height: 16),
          Text(
            'Loading vehicles...',
            style: TextStyle(
              fontSize: 16,
              color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(bool isDarkMode) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'Oops! Something went wrong',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade700,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _fetchVehicles,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppThemes.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(bool isDarkMode) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.directions_car_outlined,
              size: 64,
              color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'No vehicles available',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Check back later for available vehicles',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade700,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar(bool isDarkMode) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? AppThemes.darkCard : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDarkMode ? AppThemes.darkBorder : Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: TextField(
        controller: _searchController,
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
        decoration: InputDecoration(
          hintText: 'Search vehicles...',
          hintStyle: TextStyle(
            color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade600,
          ),
          prefixIcon: Icon(
            Icons.search,
            color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade600,
          ),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                  icon: Icon(
                    Icons.clear,
                    color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade600,
                  ),
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
        style: TextStyle(
          color: isDarkMode ? Colors.white : Colors.black87,
        ),
      ),
    );
  }

  Widget _buildTabBar(bool isDarkMode) {
    if (_vehicleResponse == null) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: isDarkMode ? AppThemes.darkCard : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDarkMode ? AppThemes.darkBorder : Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabAlignment: TabAlignment.start,
        indicator: BoxDecoration(
          color: AppThemes.primaryColor,
          borderRadius: BorderRadius.circular(8),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        indicatorPadding: const EdgeInsets.all(4),
        labelColor: Colors.white,
        unselectedLabelColor: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade700,
        dividerColor: Colors.transparent, // Remove any default divider lines
        overlayColor: WidgetStateProperty.all(Colors.transparent), // Remove tap overlay
        labelStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.normal,
        ),
        tabs: _vehicleResponse!.data.keys.map((brand) {
          final count = _vehicleResponse!.data[brand]?.length ?? 0;
          return Tab(
            text: '$brand ($count)',
          );
        }).toList(),
      ),
    );
  }

  Widget _buildVehicleGrid(List<VehicleData> vehicles, bool isDarkMode) {
    if (vehicles.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.search_off,
                size: 48,
                color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade400,
              ),
              const SizedBox(height: 16),
              Text(
                'No vehicles found',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: isDarkMode ? Colors.white : Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Try adjusting your search',
                style: TextStyle(
                  fontSize: 14,
                  color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade700,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.85, // Increased for more compact cards
          crossAxisSpacing: 8, // Reduced spacing
          mainAxisSpacing: 8, // Reduced spacing
        ),
        itemCount: vehicles.length,
        itemBuilder: (context, index) {
          final vehicle = vehicles[index];
          final isSelected = _selectedVehicleId == vehicle.id;

          return _buildVehicleCard(vehicle, isSelected, isDarkMode);
        },
      ),
    );
  }

  Widget _buildVehicleCard(VehicleData vehicle, bool isSelected, bool isDarkMode) {
    return GestureDetector(
      onTap: () {
        setState(() {
          if (isSelected) {
            // Deselect if already selected
            _selectedVehicleId = null;
            _selectedVehicle = null;
          } else {
            // Single selection - select this vehicle and show bottom sheet
            _selectedVehicleId = vehicle.id;
            _selectedVehicle = vehicle;
          }
        });

        // Show bottom sheet when vehicle is selected
        if (!isSelected) {
          _showLicensePlateBottomSheet(context, isDarkMode);
        }
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          color: isDarkMode ? AppThemes.darkCard : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? AppThemes.primaryColor
                : (isDarkMode ? AppThemes.darkBorder : Colors.grey.shade300),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isDarkMode
              ? []
              : [
                  BoxShadow(
                    color: Colors.black.withAlpha(10),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Vehicle Image
            Expanded(
              flex: 3,
              child: Stack(
                children: [
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade100,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(12),
                        topRight: Radius.circular(12),
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(12),
                        topRight: Radius.circular(12),
                      ),
                      child: _buildVehicleImage(vehicle.vehicleImage),
                    ),
                  ),
                  // Selection indicator
                  if (isSelected)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: AppThemes.primaryColor,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            // Vehicle Details - More compact
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(8), // Reduced padding
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      vehicle.name,
                      style: TextStyle(
                        fontSize: 13, // Slightly smaller
                        fontWeight: FontWeight.w600,
                        color: isDarkMode ? Colors.white : Colors.black87,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2), // Reduced spacing
                    Row(
                      children: [
                        Icon(
                          Icons.battery_charging_full,
                          size: 12, // Smaller icon
                          color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade600,
                        ),
                        const SizedBox(width: 2), // Reduced spacing
                        Expanded(
                          child: Text(
                            '${vehicle.batteryCapacity} kWh',
                            style: TextStyle(
                              fontSize: 11, // Smaller text
                              color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade600,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    if (vehicle.variants != null && vehicle.variants!.isNotEmpty)
                      Text(
                        _formatVariants(vehicle.variants!),
                        style: TextStyle(
                          fontSize: 10, // Smaller text
                          color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVehicleImage(String imageUrl) {
    if (imageUrl.isEmpty || imageUrl == 'https://api.eeil.online/uploads/') {
      return Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.grey.shade200,
        child: Icon(
          Icons.directions_car,
          size: 48,
          color: Colors.grey.shade400,
        ),
      );
    }

    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: double.infinity,
      height: double.infinity,
      fit: BoxFit.cover,
      placeholder: (context, url) => Container(
        color: Colors.grey.shade200,
        child: Center(
          child: CircularProgressIndicator(
            strokeWidth: 2,
            color: AppThemes.primaryColor,
          ),
        ),
      ),
      errorWidget: (context, url, error) => Container(
        color: Colors.grey.shade200,
        child: Icon(
          Icons.directions_car,
          size: 48,
          color: Colors.grey.shade400,
        ),
      ),
    );
  }

  String _formatVariants(String variants) {
    // Remove quotes and clean up the variants string
    String cleaned = variants.replaceAll('"', '').replaceAll('\\', '');
    if (cleaned.isEmpty) return '';

    // Split by comma and take first few variants
    List<String> variantList = cleaned.split(',').map((e) => e.trim()).toList();
    if (variantList.length > 2) {
      return '${variantList.take(2).join(', ')}...';
    }
    return variantList.join(', ');
  }

  // Show bottom sheet for license plate input
  void _showLicensePlateBottomSheet(BuildContext context, bool isDarkMode) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildLicensePlateBottomSheet(isDarkMode),
    );
  }

  Widget _buildLicensePlateBottomSheet(bool isDarkMode) {
    return Container(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Container(
        decoration: BoxDecoration(
          color: isDarkMode ? AppThemes.darkCard : Colors.white,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Handle bar
                Center(
                  child: Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: isDarkMode ? Colors.grey.shade600 : Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // Selected vehicle info
                if (_selectedVehicle != null) ...[
                  Row(
                    children: [
                      Container(
                        width: 60,
                        height: 40,
                        decoration: BoxDecoration(
                          color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: _buildVehicleImage(_selectedVehicle!.vehicleImage),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _selectedVehicle!.name,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: isDarkMode ? Colors.white : Colors.black87,
                              ),
                            ),
                            Text(
                              '${_selectedVehicle!.batteryCapacity} kWh',
                              style: TextStyle(
                                fontSize: 14,
                                color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                ],

                // License plate input
                Text(
                  'License Plate Number',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
                const SizedBox(height: 12),
                TextFormField(
                  controller: _licensePlateController,
                  decoration: InputDecoration(
                    hintText: 'Enter license plate (e.g., ABC-1234)',
                    hintStyle: TextStyle(
                      color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade600,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: isDarkMode ? AppThemes.darkBorder : Colors.grey.shade300,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: isDarkMode ? AppThemes.darkBorder : Colors.grey.shade300,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: AppThemes.primaryColor,
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: isDarkMode ? AppThemes.darkBackground : Colors.grey.shade50,
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                  ),
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                  textCapitalization: TextCapitalization.characters,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter a license plate number';
                    }
                    if (value.trim().length < 3) {
                      return 'License plate must be at least 3 characters';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),

                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          _licensePlateController.clear();
                          setState(() {
                            _selectedVehicleId = null;
                            _selectedVehicle = null;
                          });
                          Navigator.pop(context);
                        },
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          side: BorderSide(
                            color: isDarkMode ? AppThemes.darkBorder : Colors.grey.shade300,
                          ),
                        ),
                        child: Text(
                          'Cancel',
                          style: TextStyle(
                            color: isDarkMode ? Colors.white : Colors.black87,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => _saveVehicle(isDarkMode),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppThemes.primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 0,
                        ),
                        child: const Text(
                          'Add Vehicle',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _saveVehicle(bool isDarkMode) {
    if (_formKey.currentState?.validate() ?? false) {
      // TODO: Implement actual vehicle saving logic
      final licensePlate = _licensePlateController.text.trim();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Vehicle "${_selectedVehicle?.name}" added with license plate: $licensePlate'),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
        ),
      );

      _licensePlateController.clear();
      setState(() {
        _selectedVehicleId = null;
        _selectedVehicle = null;
      });
      Navigator.pop(context);
    }
  }
}
