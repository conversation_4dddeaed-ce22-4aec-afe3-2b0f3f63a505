import 'package:flutter/material.dart';

import '../../../utils/app_themes.dart';

/// A modern loading animation for the Profile screen.
/// This widget displays animated shimmers and placeholders that match the profile screen layout.
class ProfileLoadingAnimation extends StatefulWidget {
  const ProfileLoadingAnimation({super.key});

  @override
  State<ProfileLoadingAnimation> createState() => _ProfileLoadingAnimationState();
}

class _ProfileLoadingAnimationState extends State<ProfileLoadingAnimation> with SingleTickerProviderStateMixin {
  late AnimationController _shimmerController;

  @override
  void initState() {
    super.initState();
    _shimmerController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    // Auto-repeat the shimmer animation
    _shimmerController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _shimmerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final shimmerGradient = LinearGradient(
      colors: isDarkMode
          ? [
              Colors.grey.shade800,
              Colors.grey.shade700,
              Colors.grey.shade800,
            ]
          : [
              Colors.grey.shade200,
              Colors.grey.shade100,
              Colors.grey.shade200,
            ],
      stops: const [0.1, 0.3, 0.4],
      begin: const Alignment(-1.0, -0.3),
      end: const Alignment(1.0, 0.3),
      tileMode: TileMode.clamp,
    );

    return SingleChildScrollView(
      physics: const NeverScrollableScrollPhysics(),
      child: AnimatedBuilder(
        animation: _shimmerController,
        builder: (context, child) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // User header shimmer
              _buildUserHeaderShimmer(isDarkMode, shimmerGradient),
              const SizedBox(height: 16),

              // Animated cards shimmer (Wallet Balance and Vehicles)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: _buildAnimatedCardsShimmer(isDarkMode, shimmerGradient),
              ),
              const SizedBox(height: 16),

              // Toggles container shimmer
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: _buildTogglesShimmer(isDarkMode, shimmerGradient),
              ),
              const SizedBox(height: 16),

              // Menu items shimmer
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: _buildMenuItemsShimmer(isDarkMode, shimmerGradient),
              ),
              const SizedBox(height: 24),

              // Logout button shimmer
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: _buildLogoutButtonShimmer(isDarkMode, shimmerGradient),
              ),
              const SizedBox(height: 24),
            ],
          );
        },
      ),
    );
  }

  Widget _buildUserHeaderShimmer(bool isDarkMode, LinearGradient shimmerGradient) {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
      decoration: BoxDecoration(
        color: isDarkMode ? AppThemes.darkCard : Colors.white,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
        boxShadow: isDarkMode
            ? null
            : [
                BoxShadow(
                  color: Colors.black.withAlpha(12),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
      ),
      child: Column(
        children: [
          // Avatar shimmer
          ShaderMask(
            blendMode: BlendMode.srcIn,
            shaderCallback: (bounds) {
              return shimmerGradient.createShader(
                Rect.fromLTWH(
                  _shimmerController.value * bounds.width * 2 - bounds.width,
                  0,
                  bounds.width * 2,
                  bounds.height,
                ),
              );
            },
            child: Container(
              width: 90,
              height: 90,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Name shimmer
          ShaderMask(
            blendMode: BlendMode.srcIn,
            shaderCallback: (bounds) {
              return shimmerGradient.createShader(
                Rect.fromLTWH(
                  _shimmerController.value * bounds.width * 2 - bounds.width,
                  0,
                  bounds.width * 2,
                  bounds.height,
                ),
              );
            },
            child: Container(
              width: 180,
              height: 24,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(height: 8),

          // Email shimmer
          ShaderMask(
            blendMode: BlendMode.srcIn,
            shaderCallback: (bounds) {
              return shimmerGradient.createShader(
                Rect.fromLTWH(
                  _shimmerController.value * bounds.width * 2 - bounds.width,
                  0,
                  bounds.width * 2,
                  bounds.height,
                ),
              );
            },
            child: Container(
              width: 220,
              height: 16,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(height: 8),

          // Phone shimmer
          ShaderMask(
            blendMode: BlendMode.srcIn,
            shaderCallback: (bounds) {
              return shimmerGradient.createShader(
                Rect.fromLTWH(
                  _shimmerController.value * bounds.width * 2 - bounds.width,
                  0,
                  bounds.width * 2,
                  bounds.height,
                ),
              );
            },
            child: Container(
              width: 140,
              height: 16,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Edit Profile button shimmer
          ShaderMask(
            blendMode: BlendMode.srcIn,
            shaderCallback: (bounds) {
              return shimmerGradient.createShader(
                Rect.fromLTWH(
                  _shimmerController.value * bounds.width * 2 - bounds.width,
                  0,
                  bounds.width * 2,
                  bounds.height,
                ),
              );
            },
            child: Container(
              width: 140,
              height: 40,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedCardsShimmer(bool isDarkMode, LinearGradient shimmerGradient) {
    return Row(
      children: [
        Expanded(
          child: _buildCardShimmer(isDarkMode, shimmerGradient, true),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildCardShimmer(isDarkMode, shimmerGradient, false),
        ),
      ],
    );
  }

  Widget _buildCardShimmer(bool isDarkMode, LinearGradient shimmerGradient, bool isFirst) {
    final cardColor = isFirst
        ? const Color.fromARGB(255, 212, 226, 252)
        : const Color.fromARGB(255, 222, 243, 226);

    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: Duration(milliseconds: 600 + (isFirst ? 0 : 100)),
      curve: Curves.easeOutCubic,
      builder: (context, value, child) {
        return Opacity(
          opacity: value,
          child: Transform.translate(
            offset: Offset(0, 20 * (1 - value)),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isDarkMode ? AppThemes.darkCard : cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isDarkMode ? Colors.white24 : Colors.transparent,
                  width: 1.5,
                ),
                boxShadow: isDarkMode
                    ? []
                    : [
                        BoxShadow(
                          color: Colors.black.withAlpha(15),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      // Icon circle shimmer
                      ShaderMask(
                        blendMode: BlendMode.srcIn,
                        shaderCallback: (bounds) {
                          return shimmerGradient.createShader(
                            Rect.fromLTWH(
                              _shimmerController.value * bounds.width * 2 -
                                  bounds.width,
                              0,
                              bounds.width * 2,
                              bounds.height,
                            ),
                          );
                        },
                        child: Container(
                          width: 36,
                          height: 36,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      const Spacer(),
                      // Chevron shimmer
                      ShaderMask(
                        blendMode: BlendMode.srcIn,
                        shaderCallback: (bounds) {
                          return shimmerGradient.createShader(
                            Rect.fromLTWH(
                              _shimmerController.value * bounds.width * 2 -
                                  bounds.width,
                              0,
                              bounds.width * 2,
                              bounds.height,
                            ),
                          );
                        },
                        child: Container(
                          width: 20,
                          height: 20,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  // Title shimmer
                  ShaderMask(
                    blendMode: BlendMode.srcIn,
                    shaderCallback: (bounds) {
                      return shimmerGradient.createShader(
                        Rect.fromLTWH(
                          _shimmerController.value * bounds.width * 2 -
                              bounds.width,
                          0,
                          bounds.width * 2,
                          bounds.height,
                        ),
                      );
                    },
                    child: Container(
                      width: 60,
                      height: 14,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(7),
                        color: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(height: 4),
                  // Value shimmer
                  ShaderMask(
                    blendMode: BlendMode.srcIn,
                    shaderCallback: (bounds) {
                      return shimmerGradient.createShader(
                        Rect.fromLTWH(
                          _shimmerController.value * bounds.width * 2 -
                              bounds.width,
                          0,
                          bounds.width * 2,
                          bounds.height,
                        ),
                      );
                    },
                    child: Container(
                      width: 80,
                      height: 18,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(9),
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTogglesShimmer(bool isDarkMode, LinearGradient shimmerGradient) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: const Duration(milliseconds: 700),
      curve: Curves.easeOutCubic,
      builder: (context, value, child) {
        return Opacity(
          opacity: value,
          child: Transform.translate(
            offset: Offset(0, 20 * (1 - value)),
            child: Container(
              decoration: BoxDecoration(
                color: isDarkMode ? AppThemes.darkCard : Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color:
                      isDarkMode ? AppThemes.darkBorder : Colors.grey.shade300,
                  width: 1.5,
                ),
              ),
              child: Column(
                children: [
                  _buildToggleShimmer(shimmerGradient),
                  Container(
                    height: 1,
                    color: Colors.grey.shade300,
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                  ),
                  _buildToggleShimmer(shimmerGradient),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildToggleShimmer(LinearGradient shimmerGradient) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      child: Row(
        children: [
          // Icon shimmer
          ShaderMask(
            blendMode: BlendMode.srcIn,
            shaderCallback: (bounds) {
              return shimmerGradient.createShader(
                Rect.fromLTWH(
                  _shimmerController.value * bounds.width * 2 - bounds.width,
                  0,
                  bounds.width * 2,
                  bounds.height,
                ),
              );
            },
            child: Container(
              width: 24,
              height: 24,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(width: 16),
          // Label shimmer
          Expanded(
            child: ShaderMask(
              blendMode: BlendMode.srcIn,
              shaderCallback: (bounds) {
                return shimmerGradient.createShader(
                  Rect.fromLTWH(
                    _shimmerController.value * bounds.width * 2 - bounds.width,
                    0,
                    bounds.width * 2,
                    bounds.height,
                  ),
                );
              },
              child: Container(
                height: 16,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.white,
                ),
              ),
            ),
          ),
          // Switch shimmer
          ShaderMask(
            blendMode: BlendMode.srcIn,
            shaderCallback: (bounds) {
              return shimmerGradient.createShader(
                Rect.fromLTWH(
                  _shimmerController.value * bounds.width * 2 - bounds.width,
                  0,
                  bounds.width * 2,
                  bounds.height,
                ),
              );
            },
            child: Container(
              width: 40,
              height: 24,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItemsShimmer(bool isDarkMode, LinearGradient shimmerGradient) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: const Duration(milliseconds: 800),
      curve: Curves.easeOutCubic,
      builder: (context, value, child) {
        return Opacity(
          opacity: value,
          child: Transform.translate(
            offset: Offset(0, 20 * (1 - value)),
            child: Container(
              decoration: BoxDecoration(
                color: isDarkMode ? AppThemes.darkCard : Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color:
                      isDarkMode ? AppThemes.darkBorder : Colors.grey.shade300,
                  width: 1.5,
                ),
              ),
              child: Column(
                children: List.generate(
                  8, // Number of menu items
                  (index) => Column(
                    children: [
                      _buildMenuItemShimmer(shimmerGradient, index),
                      if (index < 7)
                        Container(
                          height: 1,
                          color: Colors.grey.withAlpha(50),
                          margin: const EdgeInsets.symmetric(horizontal: 16),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMenuItemShimmer(LinearGradient shimmerGradient, int index) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: Duration(milliseconds: 800 + (index * 50)),
      curve: Curves.easeOutCubic,
      builder: (context, value, child) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
          child: Row(
            children: [
              // Icon shimmer
              ShaderMask(
                blendMode: BlendMode.srcIn,
                shaderCallback: (bounds) {
                  return shimmerGradient.createShader(
                    Rect.fromLTWH(
                      _shimmerController.value * bounds.width * 2 -
                          bounds.width,
                      0,
                      bounds.width * 2,
                      bounds.height,
                    ),
                  );
                },
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              // Label shimmer
              Expanded(
                child: ShaderMask(
                  blendMode: BlendMode.srcIn,
                  shaderCallback: (bounds) {
                    return shimmerGradient.createShader(
                      Rect.fromLTWH(
                        _shimmerController.value * bounds.width * 2 -
                            bounds.width,
                        0,
                        bounds.width * 2,
                        bounds.height,
                      ),
                    );
                  },
                  child: Container(
                    height: 16,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              // Chevron shimmer
              ShaderMask(
                blendMode: BlendMode.srcIn,
                shaderCallback: (bounds) {
                  return shimmerGradient.createShader(
                    Rect.fromLTWH(
                      _shimmerController.value * bounds.width * 2 -
                          bounds.width,
                      0,
                      bounds.width * 2,
                      bounds.height,
                    ),
                  );
                },
                child: Container(
                  width: 20,
                  height: 20,
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLogoutButtonShimmer(bool isDarkMode, LinearGradient shimmerGradient) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: const Duration(milliseconds: 900),
      curve: Curves.easeOutCubic,
      builder: (context, value, child) {
        return Opacity(
          opacity: value,
          child: Transform.translate(
            offset: Offset(0, 20 * (1 - value)),
            child: ShaderMask(
              blendMode: BlendMode.srcIn,
              shaderCallback: (bounds) {
                return shimmerGradient.createShader(
                  Rect.fromLTWH(
                    _shimmerController.value * bounds.width * 2 - bounds.width,
                    0,
                    bounds.width * 2,
                    bounds.height,
                  ),
                );
              },
              child: Container(
                width: double.infinity,
                height: 50,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.white,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
