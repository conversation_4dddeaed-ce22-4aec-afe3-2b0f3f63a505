import 'package:flutter/material.dart';
import '../../../repositories/user_repository.dart';
import '../../../services/service_locator.dart';

/// A screen for updating user profile information
///
/// This screen provides a secure and user-friendly interface for updating
/// the user's name and email address. It leverages the UserRepositoryExtension
/// for secure profile updates with proper error handling.
class ProfileUpdateScreen extends StatefulWidget {
  const ProfileUpdateScreen({super.key});

  @override
  ProfileUpdateScreenState createState() => ProfileUpdateScreenState();
}

class ProfileUpdateScreenState extends State<ProfileUpdateScreen> {
  final UserRepository _userRepository = UserRepository();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _gstNumberController = TextEditingController();
  final TextEditingController _businessNameController = TextEditingController();

  bool _isLoading = false;
  String? _errorMessage;
  String? _successMessage;

  // GST details
  String? _gstNumber;
  String? _businessName;
  bool _hasGstDetails = false;

  @override
  void initState() {
    super.initState();
    _loadUserProfile();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _gstNumberController.dispose();
    _businessNameController.dispose();
    super.dispose();
  }

  /// Load the user's current profile data
  Future<void> _loadUserProfile() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final profileResponse = await _userRepository.getUserProfile();

      if (profileResponse.success && profileResponse.data != null) {
        setState(() {
          // Set name and email from profile response
          final name = profileResponse.data!.name;
          final email = profileResponse.data!.email;
          _nameController.text = name;
          _emailController.text = email;

          // Set GST details if available from the API response
          // Access the data from the raw response map to get GST details
          final userData =
              profileResponse.rawData?['data'] as Map<String, dynamic>?;
          if (userData != null) {
            _gstNumber = userData['gst_no'] as String?;
            _businessName = userData['business_name'] as String?;
          }
          _gstNumberController.text = _gstNumber ?? '';
          _businessNameController.text = _businessName ?? '';
          _hasGstDetails = _gstNumber != null && _gstNumber!.isNotEmpty;
        });
      } else {
        setState(() {
          _errorMessage = 'Failed to load profile: ${profileResponse.message}';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading profile: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Update the user's profile using the extension method
  ///
  /// This method demonstrates the secure pattern for profile updates:
  /// 1. Validate user input client-side
  /// 2. Get the user ID securely from the token service
  /// 3. Use the extension method with named parameters
  /// 4. Handle success and error states appropriately
  Future<void> _updateProfile() async {
    // Clear previous messages
    setState(() {
      _errorMessage = null;
      _successMessage = null;
    });

    // Get the values from the controllers
    final name = _nameController.text.trim();
    final email = _emailController.text.trim();
    final gstNumber = _gstNumberController.text.trim();
    final businessName = _businessNameController.text.trim();

    // Client-side validation (the extension also validates, but we want to provide immediate feedback)
    if (name.isEmpty) {
      setState(() {
        _errorMessage = 'Name cannot be empty';
      });
      return;
    }

    // Basic email validation
    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+');
    if (!emailRegex.hasMatch(email)) {
      setState(() {
        _errorMessage = 'Please enter a valid email address';
      });
      return;
    }

    // Show loading state
    setState(() {
      _isLoading = true;
    });

    try {
      // Get userId securely from the token service
      // This is more secure than accepting a user ID as a parameter
      final tokenService = ServiceLocator().tokenService;
      final userId = await tokenService.getUserId();

      if (userId == null) {
        setState(() {
          _errorMessage =
              'Authentication error: User ID not found. Please log in again.';
          _isLoading = false;
        });
        return;
      }

      // Use the repository method with named parameters for clarity and safety
      final result = await _userRepository.updateUserProfile(
        userId: userId,
        name: name,
        email: email,
        gstNumber: gstNumber,
        businessName: businessName,
      );

      // Handle the result
      if (mounted) {
        setState(() {
          if (result.success) {
            _successMessage = 'Profile updated successfully!';

            // You could also show a snackbar for better visibility
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Profile updated successfully!'),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 3),
              ),
            );
          } else {
            _errorMessage = result.message;
          }
        });
      }
    } catch (e) {
      // Handle unexpected errors
      if (mounted) {
        setState(() {
          _errorMessage = 'An unexpected error occurred: ${e.toString()}';
        });
      }
    } finally {
      // Always reset loading state
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Update Profile'),
        elevation: 0,
      ),
      body: Stack(
        children: [
          // Main content
          SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Header section
                Center(
                  child: Column(
                    children: [
                      const SizedBox(height: 16),
                      Icon(
                        Icons.account_circle,
                        size: 80,
                        color: isDarkMode ? Colors.white70 : Colors.black54,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Edit Your Profile',
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Update your personal information',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color:
                                  isDarkMode ? Colors.white60 : Colors.black54,
                            ),
                      ),
                      const SizedBox(height: 32),
                    ],
                  ),
                ),

                // Error message
                if (_errorMessage != null)
                  Container(
                    margin: const EdgeInsets.only(bottom: 16.0),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.red.withAlpha(25), // 0.1 * 255 = ~25
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                          color: Colors.red.withAlpha(77)), // 0.3 * 255 = ~77
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.error_outline, color: Colors.red),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            _errorMessage!,
                            style: const TextStyle(color: Colors.red),
                          ),
                        ),
                      ],
                    ),
                  ),

                // Success message
                if (_successMessage != null)
                  Container(
                    margin: const EdgeInsets.only(bottom: 16.0),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.green.withAlpha(25), // 0.1 * 255 = ~25
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                          color: Colors.green.withAlpha(77)), // 0.3 * 255 = ~77
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.check_circle_outline,
                            color: Colors.green),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            _successMessage!,
                            style: const TextStyle(color: Colors.green),
                          ),
                        ),
                      ],
                    ),
                  ),

                // Name field
                TextField(
                  controller: _nameController,
                  decoration: InputDecoration(
                    labelText: 'Full Name',
                    hintText: 'Enter your full name',
                    prefixIcon: const Icon(Icons.person_outline),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Theme.of(context).primaryColor,
                        width: 2,
                      ),
                    ),
                  ),
                  textInputAction: TextInputAction.next,
                ),
                const SizedBox(height: 20),

                // Email field
                TextField(
                  controller: _emailController,
                  decoration: InputDecoration(
                    labelText: 'Email Address',
                    hintText: 'Enter your email address',
                    prefixIcon: const Icon(Icons.email_outlined),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Theme.of(context).primaryColor,
                        width: 2,
                      ),
                    ),
                  ),
                  keyboardType: TextInputType.emailAddress,
                  textInputAction: TextInputAction.done,
                  onSubmitted: (_) => _updateProfile(),
                ),
                const SizedBox(height: 20),

                // GST Details section
                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.receipt_long_outlined,
                                  color: Theme.of(context).primaryColor,
                                ),
                                const SizedBox(width: 8),
                                const Text(
                                  'GST Details',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            TextButton(
                              onPressed: () {
                                _showGstDetailsSheet(context);
                              },
                              child: Text(
                                _hasGstDetails ? 'Edit' : 'Add',
                                style: TextStyle(
                                  color: Theme.of(context).primaryColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        const Divider(),
                        const SizedBox(height: 8),
                        if (_hasGstDetails) ...[
                          _buildInfoRow(
                              'GST Number', _gstNumber ?? 'Not provided'),
                          const SizedBox(height: 8),
                          _buildInfoRow(
                              'Business Name', _businessName ?? 'Not provided'),
                        ] else ...[
                          const Text(
                            'No GST details added. Add your GST details for business transactions.',
                            style: TextStyle(color: Colors.grey),
                          ),
                          const SizedBox(height: 8),
                          OutlinedButton(
                            onPressed: () {
                              _showGstDetailsSheet(context);
                            },
                            style: OutlinedButton.styleFrom(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: const Text('Add GST Details'),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 32),

                // Update button
                SizedBox(
                  height: 50,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _updateProfile,
                    style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 2,
                    ),
                    child: const Text(
                      'Update Profile',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Loading overlay
          if (_isLoading)
            Container(
              color: Colors.black.withAlpha(77), // 0.3 * 255 = ~77
              child: const Center(
                child: Card(
                  elevation: 4,
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Updating profile...'),
                      ],
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Builds an information row with label and value
  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 120,
          child: Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  /// Shows a bottom sheet for entering GST details with animation effects
  void _showGstDetailsSheet(BuildContext context) {
    // Create a local animation controller for this bottom sheet
    final AnimationController animController = AnimationController(
      vsync: Navigator.of(context).overlay! as TickerProvider,
      duration: const Duration(milliseconds: 400),
    );

    // Create animations
    final Animation<double> fadeAnim = CurvedAnimation(
      parent: animController,
      curve: Curves.easeIn,
    );

    final Animation<Offset> slideAnim = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: animController,
      curve: Curves.easeOut,
    ));

    // Start animation when sheet is shown
    animController.forward();

    // Set controllers with current values
    _gstNumberController.text = _gstNumber ?? '';
    _businessNameController.text = _businessName ?? '';

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
          ),
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'GST Details',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () {
                          // Reverse animation before popping
                          animController.reverse().then((_) {
                            Navigator.pop(context);
                          });
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Animated GST Number field
                  FadeTransition(
                    opacity: fadeAnim,
                    child: SlideTransition(
                      position: slideAnim,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'GST Number',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 8),
                          TextField(
                            controller: _gstNumberController,
                            decoration: InputDecoration(
                              hintText: 'Enter GST Number',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 16,
                              ),
                              prefixIcon: const Icon(
                                  Icons.receipt_long_outlined,
                                  color: Color(0xFF67C44C)),
                            ),
                            textCapitalization: TextCapitalization.characters,
                            onChanged: (value) {
                              // Validate GST number format
                              final gstRegex = RegExp(
                                  r'^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$');
                              final isValid = gstRegex.hasMatch(value);

                              if (value.isNotEmpty && !isValid) {
                                // Show validation feedback
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Invalid GST number format'),
                                    duration: Duration(seconds: 2),
                                  ),
                                );
                              }
                            },
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Format: 2 digits + 5 chars + 4 digits + 1 char + 1 char + Z + 1 char',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Animated Business Name field with delayed animation
                  FadeTransition(
                    opacity: CurvedAnimation(
                      parent: animController,
                      curve: Interval(0.2, 1.0, curve: Curves.easeIn),
                    ),
                    child: SlideTransition(
                      position: Tween<Offset>(
                        begin: const Offset(0, 0.4),
                        end: Offset.zero,
                      ).animate(CurvedAnimation(
                        parent: animController,
                        curve: Interval(0.2, 1.0, curve: Curves.easeOut),
                      )),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Business Name',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 8),
                          TextField(
                            controller: _businessNameController,
                            decoration: InputDecoration(
                              hintText: 'Enter Business Name',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 16,
                              ),
                              prefixIcon: const Icon(Icons.business_outlined,
                                  color: Color(0xFF67C44C)),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Animated Save button
                  FadeTransition(
                    opacity: CurvedAnimation(
                      parent: animController,
                      curve: Interval(0.4, 1.0, curve: Curves.easeIn),
                    ),
                    child: SlideTransition(
                      position: Tween<Offset>(
                        begin: const Offset(0, 0.6),
                        end: Offset.zero,
                      ).animate(CurvedAnimation(
                        parent: animController,
                        curve: Interval(0.4, 1.0, curve: Curves.easeOut),
                      )),
                      child: SizedBox(
                        width: double.infinity,
                        height: 50,
                        child: ElevatedButton(
                          onPressed: () {
                            final gstNumber = _gstNumberController.text.trim();
                            final businessName =
                                _businessNameController.text.trim();

                            // Validate GST number format if provided
                            if (gstNumber.isNotEmpty) {
                              final gstRegex = RegExp(
                                  r'^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$');
                              if (!gstRegex.hasMatch(gstNumber)) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Invalid GST number format'),
                                  ),
                                );
                                return;
                              }
                            }

                            // Update state with new values
                            setState(() {
                              _gstNumber = gstNumber.isEmpty ? null : gstNumber;
                              _businessName =
                                  businessName.isEmpty ? null : businessName;
                              _hasGstDetails =
                                  _gstNumber != null && _gstNumber!.isNotEmpty;
                            });

                            // Reverse animation before popping
                            animController.reverse().then((_) {
                              Navigator.pop(context);
                            });
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF67C44C),
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text('Save GST Details'),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
        );
      },
    ).then((_) {
      // Dispose animation controller when sheet is closed
      animController.dispose();
    });
  }
}
