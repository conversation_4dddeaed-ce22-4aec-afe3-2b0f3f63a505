import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:ui';
import 'dart:math' as math;

import '../../widgets/navigation_bar.dart';
import '../../widgets/adaptive_logo.dart';
import '../../services/auth/auth_service.dart';
import '../../services/sync_service.dart';
import '../../services/auth_manager.dart';
import '../../utils/app_theme.dart';

class EnhancedUserOnboardingScreen extends StatefulWidget {
  final String userId;
  final String token;
  final String phoneNumber;

  const EnhancedUserOnboardingScreen({
    super.key,
    required this.userId,
    required this.token,
    required this.phoneNumber,
  });

  @override
  State<EnhancedUserOnboardingScreen> createState() =>
      _EnhancedUserOnboardingScreenState();
}

class _EnhancedUserOnboardingScreenState
    extends State<EnhancedUserOnboardingScreen>
    with SingleTickerProviderStateMixin {
  // Controllers
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  // Animation controllers
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;

  // State variables
  bool _isLoading = false;
  String? _errorMessage;
  bool _isNameFocused = false;
  bool _isEmailFocused = false;
  bool _isNameValid = true;
  bool _isEmailValid = true;

  // Focus nodes
  final FocusNode _nameFocusNode = FocusNode();
  final FocusNode _emailFocusNode = FocusNode();

  // Background animation values
  final List<Color> _bgColors = [
    AppTheme.primaryColor.withAlpha(13), // 0.05 * 255 = ~13
    AppTheme.primaryColor.withAlpha(20), // 0.08 * 255 = ~20
    AppTheme.primaryColor.withAlpha(31), // 0.12 * 255 = ~31
    AppTheme.secondaryColor.withAlpha(13), // 0.05 * 255 = ~13
    AppTheme.secondaryColor.withAlpha(20), // 0.08 * 255 = ~20
  ];

  // Services
  final AuthService _authService = AuthService();
  final AuthManager _authManager = AuthManager();

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );

    // Create animations
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 0.7, curve: Curves.easeOutQuint),
      ),
    );

    _scaleAnimation = Tween<double>(
      begin: 0.95,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 0.8, curve: Curves.easeOutQuint),
      ),
    );

    // Start the animation
    _animationController.forward();

    // Add focus listeners
    _nameFocusNode.addListener(_handleNameFocusChange);
    _emailFocusNode.addListener(_handleEmailFocusChange);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _animationController.dispose();
    _nameFocusNode.dispose();
    _emailFocusNode.dispose();
    super.dispose();
  }

  // Handle focus changes
  void _handleNameFocusChange() {
    setState(() {
      _isNameFocused = _nameFocusNode.hasFocus;
      if (!_nameFocusNode.hasFocus) {
        _validateName(_nameController.text);
      }
    });
  }

  void _handleEmailFocusChange() {
    setState(() {
      _isEmailFocused = _emailFocusNode.hasFocus;
      if (!_emailFocusNode.hasFocus) {
        _validateEmail(_emailController.text);
      }
    });
  }

  // Validation methods
  bool _validateName(String value) {
    final isValid = value.isNotEmpty && value.length >= 2;
    setState(() {
      _isNameValid = isValid;
    });
    return isValid;
  }

  bool _validateEmail(String value) {
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    final isValid = emailRegex.hasMatch(value);
    setState(() {
      _isEmailValid = isValid || value.isEmpty;
    });
    return isValid;
  }

  // Submit profile data
  Future<void> _submitProfile() async {
    // Validate form
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Validate fields manually
    if (!_validateName(_nameController.text) ||
        !_validateEmail(_emailController.text)) {
      return;
    }

    // Show loading state
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    // Haptic feedback
    HapticFeedback.mediumImpact();

    try {
      // Always save user data to shared preferences first to avoid data loss
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_name', _nameController.text);
      await prefs.setString('user_email', _emailController.text);
      await prefs.setString('user_id', widget.userId);
      await prefs.setString('user_token', widget.token);
      await prefs.setString('user_phone', widget.phoneNumber);

      // Queue the profile update for syncing
      final syncService = SyncService();
      await syncService.queueProfileUpdate(
        userId: widget.userId,
        name: _nameController.text,
        email: _emailController.text,
        phone: widget.phoneNumber,
      );

      // Use the unified auth service to update the profile
      debugPrint('\n=== SUBMITTING PROFILE DATA USING AUTH SERVICE ===');
      debugPrint('User ID: ${widget.userId}');
      debugPrint('Name: ${_nameController.text}');
      debugPrint('Email: ${_emailController.text}');

      // Prepare user data
      final userData = {
        'user_id': widget.userId,
        'name': _nameController.text,
        'email': _emailController.text,
      };

      // Call the auth service
      final response = await _authService.updateProfile(userData);

      // Process the response
      if (response['success'] == true) {
        // Save login state for persistent login
        final userData = {
          'id': widget.userId,
          'name': _nameController.text,
          'email': _emailController.text,
          'mobile_number': widget.phoneNumber,
          'token': widget.token,
        };

        await _authManager.saveLoginState(
          token: widget.token,
          userData: userData,
        );
        debugPrint('Login state saved for persistent login');
        // Navigate to home screen
        if (mounted) {
          Navigator.pushReplacement(
            context,
            PageRouteBuilder(
              pageBuilder: (context, animation, secondaryAnimation) =>
                  const MainNavigation(),
              transitionsBuilder:
                  (context, animation, secondaryAnimation, child) {
                const begin = Offset(1.0, 0.0);
                const end = Offset.zero;
                const curve = Curves.easeOutQuint;

                var tween = Tween(begin: begin, end: end)
                    .chain(CurveTween(curve: curve));
                var offsetAnimation = animation.drive(tween);

                return SlideTransition(
                  position: offsetAnimation,
                  child: child,
                );
              },
              transitionDuration: const Duration(milliseconds: 500),
            ),
          );
        }
      } else {
        // Show error message
        setState(() {
          _isLoading = false;
          _errorMessage = response['message'] ?? 'Failed to update profile';
        });
      }
    } catch (e) {
      // Handle error
      setState(() {
        _isLoading = false;
        _errorMessage = 'An unexpected error occurred. Please try again.';
      });
      debugPrint('Error updating profile: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;

    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          // Animated background elements
          ..._buildBackgroundElements(screenWidth, screenHeight),

          // Main content
          SafeArea(
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                constraints: BoxConstraints(
                  minHeight: screenHeight -
                      MediaQuery.of(context).padding.top -
                      MediaQuery.of(context).padding.bottom,
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 20),

                    // Header
                    FadeTransition(
                      opacity: _fadeAnimation,
                      child: ScaleTransition(
                        scale: _scaleAnimation,
                        child: Column(
                          children: [
                            // Company logo with adaptive theming
                            const AdaptiveLogo.medium(),
                            const SizedBox(height: 24),

                            // Title
                            const Text(
                              'Complete Your Profile',
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 16),

                            // Subtitle
                            Text(
                              'Welcome to Ecoplug!',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.w600,
                                color: AppTheme.primaryColor,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 12),

                            // Description
                            Text(
                              'Please provide your name and email to complete your profile.',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey.shade600,
                                height: 1.4,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 20),

                    // Form
                    SlideTransition(
                      position: _slideAnimation,
                      child: FadeTransition(
                        opacity: _fadeAnimation,
                        child: Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Name field
                              _buildAnimatedTextField(
                                controller: _nameController,
                                focusNode: _nameFocusNode,
                                label: 'Full Name',
                                hint: 'Enter your full name',
                                icon: Icons.person_outline,
                                isValid: _isNameValid,
                                isFocused: _isNameFocused,
                                errorText: 'Please enter a valid name',
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter your name';
                                  }
                                  return null;
                                },
                                textInputAction: TextInputAction.next,
                                onFieldSubmitted: (_) {
                                  FocusScope.of(context)
                                      .requestFocus(_emailFocusNode);
                                },
                              ),
                              const SizedBox(height: 24),

                              // Email field
                              _buildAnimatedTextField(
                                controller: _emailController,
                                focusNode: _emailFocusNode,
                                label: 'Email Address',
                                hint: 'Enter your email address',
                                icon: Icons.email_outlined,
                                isValid: _isEmailValid,
                                isFocused: _isEmailFocused,
                                errorText: 'Please enter a valid email address',
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter your email';
                                  }
                                  if (!RegExp(
                                          r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                                      .hasMatch(value)) {
                                    return 'Please enter a valid email address';
                                  }
                                  return null;
                                },
                                keyboardType: TextInputType.emailAddress,
                                textInputAction: TextInputAction.done,
                                onFieldSubmitted: (_) {
                                  _submitProfile();
                                },
                              ),

                              // Error message
                              if (_errorMessage != null)
                                Padding(
                                  padding: const EdgeInsets.only(top: 16),
                                  child: Container(
                                    width: double.infinity,
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: Colors.red.shade50,
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                          color: Colors.red.shade200),
                                    ),
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.error_outline,
                                          color: Colors.red.shade700,
                                          size: 20,
                                        ),
                                        const SizedBox(width: 8),
                                        Expanded(
                                          child: Text(
                                            _errorMessage!,
                                            style: TextStyle(
                                              color: Colors.red.shade700,
                                              fontSize: 14,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),

                              const SizedBox(height: 40),

                              // Submit button
                              _buildSubmitButton(),
                            ],
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 40),
                  ],
                ),
              ),
            ),
          ),

          // Loading overlay
          if (_isLoading) _buildLoadingOverlay(),
        ],
      ),
    );
  }

  // Build animated text field
  Widget _buildAnimatedTextField({
    required TextEditingController controller,
    required FocusNode focusNode,
    required String label,
    required String hint,
    required IconData icon,
    required bool isValid,
    required bool isFocused,
    required String errorText,
    required String? Function(String?) validator,
    TextInputType keyboardType = TextInputType.text,
    TextInputAction? textInputAction,
    Function(String)? onFieldSubmitted,
  }) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: !isValid
              ? Colors.red.shade400
              : isFocused
                  ? AppTheme.primaryColor
                  : Colors.grey.shade300,
          width: isFocused ? 2 : 1,
        ),
        boxShadow: isFocused
            ? [
                BoxShadow(
                  color: AppTheme.primaryColor.withAlpha(26), // 0.1 * 255 = ~26
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ]
            : [],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextFormField(
            controller: controller,
            focusNode: focusNode,
            decoration: InputDecoration(
              labelText: label,
              hintText: hint,
              hintStyle: TextStyle(
                color: Colors.grey.shade400,
                fontSize: 14,
              ),
              labelStyle: TextStyle(
                color: isFocused
                    ? AppTheme.primaryColor
                    : !isValid
                        ? Colors.red.shade400
                        : Colors.grey.shade600,
                fontWeight: isFocused ? FontWeight.w600 : FontWeight.normal,
              ),
              prefixIcon: Icon(
                icon,
                color: isFocused
                    ? AppTheme.primaryColor
                    : !isValid
                        ? Colors.red.shade400
                        : Colors.grey.shade600,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
              floatingLabelBehavior: FloatingLabelBehavior.auto,
            ),
            style: TextStyle(
              fontSize: 16,
              color: AppTheme.primaryColor,
            ),
            keyboardType: keyboardType,
            textInputAction: textInputAction,
            onFieldSubmitted: onFieldSubmitted,
            validator: validator,
            onChanged: (value) {
              if (focusNode == _nameFocusNode) {
                _validateName(value);
              } else if (focusNode == _emailFocusNode) {
                _validateEmail(value);
              }
            },
          ),
          if (!isValid)
            Padding(
              padding: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
              child: Text(
                errorText,
                style: TextStyle(
                  color: Colors.red.shade400,
                  fontSize: 12,
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Build submit button
  Widget _buildSubmitButton() {
    return SlideTransition(
      position: _slideAnimation,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Container(
          width: double.infinity,
          height: 56,
          decoration: BoxDecoration(
            gradient: AppTheme.primaryGradient,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: AppTheme.primaryColor.withAlpha(77), // 0.3 * 255 = ~77
                blurRadius: 12,
                offset: const Offset(0, 6),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(16),
              onTap: _isLoading ? null : _submitProfile,
              splashColor: Colors.white.withAlpha(51), // 0.2 * 255 = ~51
              highlightColor: Colors.white.withAlpha(26), // 0.1 * 255 = ~26
              child: Center(
                child: _isLoading
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : const Text(
                        'Continue',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 0.5,
                        ),
                      ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Build loading overlay
  Widget _buildLoadingOverlay() {
    return Positioned.fill(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 4, sigmaY: 4),
        child: Container(
          color: Colors.black.withAlpha(51), // 0.2 * 255 = ~51
          child: Center(
            child: Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(26), // 0.1 * 255 = ~26
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(
                    width: 50,
                    height: 50,
                    child: CircularProgressIndicator(
                      valueColor:
                          AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                      strokeWidth: 3,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'Updating your profile...',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey.shade800,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Please wait a moment',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Build background elements
  List<Widget> _buildBackgroundElements(
      double screenWidth, double screenHeight) {
    return [
      // Top left circle
      Positioned(
        top: -screenWidth * 0.3,
        left: -screenWidth * 0.3,
        child: Container(
          width: screenWidth * 0.6,
          height: screenWidth * 0.6,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _bgColors[0],
          ),
        ),
      ),

      // Top right circle
      Positioned(
        top: screenHeight * 0.05,
        right: -screenWidth * 0.2,
        child: Container(
          width: screenWidth * 0.4,
          height: screenWidth * 0.4,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _bgColors[1],
          ),
        ),
      ),

      // Bottom right circle
      Positioned(
        bottom: -screenWidth * 0.2,
        right: -screenWidth * 0.1,
        child: Container(
          width: screenWidth * 0.5,
          height: screenWidth * 0.5,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _bgColors[2],
          ),
        ),
      ),

      // Bottom left small circle
      Positioned(
        bottom: screenHeight * 0.15,
        left: -screenWidth * 0.1,
        child: Container(
          width: screenWidth * 0.3,
          height: screenWidth * 0.3,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _bgColors[3],
          ),
        ),
      ),

      // Middle right small circle
      Positioned(
        top: screenHeight * 0.45,
        right: -screenWidth * 0.05,
        child: Container(
          width: screenWidth * 0.2,
          height: screenWidth * 0.2,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _bgColors[4],
          ),
        ),
      ),

      // Animated floating elements
      ..._buildFloatingElements(screenWidth, screenHeight),
    ];
  }

  // Build floating elements
  List<Widget> _buildFloatingElements(double screenWidth, double screenHeight) {
    return List.generate(5, (index) {
      final random = math.Random(index);
      final size = 8.0 + random.nextDouble() * 12;
      final initialX = random.nextDouble() * screenWidth;
      final initialY = random.nextDouble() * screenHeight;

      return Positioned(
        left: initialX,
        top: initialY,
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            final time = _animationController.value;
            final dx = math.sin(time * math.pi * 2 + index) * 15;
            final dy = math.cos(time * math.pi * 2 + index + 1) * 15;

            return Transform.translate(
              offset: Offset(dx, dy),
              child: Container(
                width: size,
                height: size,
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withAlpha(77 +
                      (random.nextDouble() * 77).toInt()), // 0.3 * 255 = ~77
                  shape: BoxShape.circle,
                ),
              ),
            );
          },
        ),
      );
    });
  }
}
