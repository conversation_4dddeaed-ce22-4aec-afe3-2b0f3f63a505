import 'package:flutter/material.dart';
import '../../services/connectivity_error_service.dart';

/// Utility class for handling API errors
class ErrorHandler {
  /// Converts API error messages to user-friendly messages
  static String getUserFriendlyMessage(String apiErrorMessage) {
    // Log the actual error for debugging
    debugPrint('API Error: $apiErrorMessage');

    // Check for success messages that might be misinterpreted as errors
    if (apiErrorMessage.contains('OTP Verify') ||
        apiErrorMessage.contains('verified successfully') ||
        apiErrorMessage.contains('Verify') ||
        apiErrorMessage.contains('verification successful')) {
      debugPrint('\n=== SUCCESS MESSAGE DETECTED IN ERROR HANDLER ===');
      debugPrint('Message: $apiErrorMessage');
      debugPrint('Converting to success message');
      return "OTP verified successfully";
    }

    // Network errors
    if (apiErrorMessage.contains('network') ||
        apiErrorMessage.contains('connection') ||
        apiErrorMessage.contains('SocketException') ||
        apiErrorMessage.contains('Failed to send OTP')) {
      return "Network error. Please try again.";
    }

    // Rate limiting
    if (apiErrorMessage.contains('rate limit') ||
        apiErrorMessage.contains('too many requests') ||
        apiErrorMessage.contains('429')) {
      return "Too many requests. Please try again in a moment.";
    }

    // Authentication errors
    if (apiErrorMessage.contains('unauthorized') ||
        apiErrorMessage.contains('invalid token') ||
        apiErrorMessage.contains('401')) {
      return "Session expired. Please log in again.";
    }

    // Permission errors
    if (apiErrorMessage.contains('forbidden') ||
        apiErrorMessage.contains('403')) {
      return "You don't have permission to access this resource.";
    }

    // OTP errors
    if (apiErrorMessage.contains('OTP') || apiErrorMessage.contains('otp')) {
      if (apiErrorMessage.contains('invalid') ||
          apiErrorMessage.contains('incorrect') ||
          apiErrorMessage.contains('wrong')) {
        return "Invalid OTP. Please try again.";
      } else if (apiErrorMessage.contains('expired')) {
        return "OTP has expired. Please request a new one.";
      } else {
        return "Error with OTP verification. Please try again.";
      }
    }

    // Timeout errors
    if (apiErrorMessage.contains('timeout') ||
        apiErrorMessage.contains('timed out')) {
      return "Request timed out. Please try again.";
    }

    // Server errors
    if (apiErrorMessage.contains('server') || apiErrorMessage.contains('500')) {
      return "Server error. Please try again later.";
    }

    // Default message for unknown errors - context-aware based on common patterns
    if (apiErrorMessage.toLowerCase().contains('login') ||
        apiErrorMessage.toLowerCase().contains('auth')) {
      return "Authentication failed. Please try again.";
    } else if (apiErrorMessage.toLowerCase().contains('profile') ||
        apiErrorMessage.toLowerCase().contains('user')) {
      return "Unable to update profile. Please try again.";
    } else if (apiErrorMessage.toLowerCase().contains('station') ||
        apiErrorMessage.toLowerCase().contains('charger')) {
      return "Connection problem. Please check your internet and try again.";
    }

    // Generic fallback message
    return "Something went wrong. Please try again.";
  }

  /// Get error message by error code
  static String getErrorMessageByCode(String? errorCode) {
    if (errorCode == null) {
      return "An unknown error occurred.";
    }

    switch (errorCode.toUpperCase()) {
      case 'TIMEOUT':
      case 'CONNECTION_TIMEOUT':
        return "Request timed out. Please check your internet connection and try again.";
      case 'CONNECTION_ERROR':
      case 'CONNECTION_RESET':
        return "Connection error. Please check your internet connection and try again.";
      case 'UNAUTHORIZED':
        return "Session expired. Please log in again.";
      case 'FORBIDDEN':
        return "You don't have permission to access this resource.";
      case 'NOT_FOUND':
        return "The requested resource was not found.";
      case 'SERVER_ERROR':
        return "Server error. Please try again later.";
      case 'INVALID_OTP':
        return "Invalid OTP. Please try again.";
      case 'OTP_EXPIRED':
        return "OTP has expired. Please request a new one.";
      case 'WALLET_NOT_FOUND':
        return "Wallet not found. Please contact support.";
      case 'INSUFFICIENT_BALANCE':
        return "Insufficient balance. Please add money to your wallet.";
      case 'STATION_NOT_FOUND':
        return "Station not found. Please try again.";
      case 'CONNECTOR_NOT_AVAILABLE':
        return "Connector is not available. Please try another connector.";
      case 'TRANSACTION_FAILED':
        return "Transaction failed. Please try again.";
      default:
        return "An error occurred. Please try again.";
    }
  }

  /// Check if error is connectivity-related and handle appropriately
  static bool isConnectivityError(dynamic error) {
    return ConnectivityErrorService.isConnectivityError(error);
  }

  /// Handle error with automatic connectivity error page navigation
  static Future<void> handleError(
    BuildContext context,
    dynamic error, {
    VoidCallback? onRetry,
    String? customMessage,
    bool showAsSheet = false,
  }) async {
    if (isConnectivityError(error)) {
      final errorMessage = customMessage ??
          ConnectivityErrorService.getConnectivityErrorMessage(error);

      if (showAsSheet) {
        await ConnectivityErrorService.showConnectivityErrorSheet(
          context,
          customMessage: errorMessage,
          onRetry: onRetry,
        );
      } else {
        await ConnectivityErrorService.showConnectivityError(
          context,
          customMessage: errorMessage,
          onRetry: onRetry,
        );
      }
    } else {
      // Handle non-connectivity errors with existing patterns
      debugPrint('Non-connectivity error: $error');
      // You can add other error handling logic here
    }
  }
}
