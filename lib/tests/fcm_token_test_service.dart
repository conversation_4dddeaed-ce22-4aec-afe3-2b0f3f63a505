import 'package:flutter/foundation.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:ecoplug/services/firebase_config_verification_service.dart';

/// FCM Token Generation Test Service
/// Comprehensive testing for FCM token generation and validation
class FCMTokenTestService {
  static final FCMTokenTestService _instance = FCMTokenTestService._internal();
  factory FCMTokenTestService() => _instance;
  FCMTokenTestService._internal();

  final FirebaseConfigVerificationService _verificationService = 
      FirebaseConfigVerificationService();

  /// Run comprehensive FCM token generation tests
  Future<Map<String, dynamic>> runFCMTokenTests() async {
    final results = <String, dynamic>{};
    
    try {
      debugPrint('🔥 ===== RUNNING FCM TOKEN GENERATION TESTS =====');
      
      // Test 1: Firebase Core Status
      results['firebase_core_test'] = await _testFirebaseCore();
      
      // Test 2: FCM Service Availability
      results['fcm_service_test'] = await _testFCMService();
      
      // Test 3: FCM Token Generation
      results['token_generation_test'] = await _testTokenGeneration();
      
      // Test 4: Token Validation
      results['token_validation_test'] = await _testTokenValidation(results);
      
      // Test 5: Token Refresh
      results['token_refresh_test'] = await _testTokenRefresh();
      
      // Test 6: Permissions Check
      results['permissions_test'] = await _testNotificationPermissions();
      
      // Overall test summary
      results['test_summary'] = _generateTestSummary(results);
      results['test_timestamp'] = DateTime.now().toIso8601String();
      
      debugPrint('✅ FCM token generation tests completed');
      return results;
      
    } catch (e) {
      debugPrint('❌ Error during FCM token tests: $e');
      results['error'] = e.toString();
      results['test_summary'] = {'status': 'failed', 'error': e.toString()};
      return results;
    }
  }

  /// Test Firebase Core initialization
  Future<Map<String, dynamic>> _testFirebaseCore() async {
    try {
      debugPrint('🔥 Testing Firebase Core initialization...');
      
      final apps = Firebase.apps;
      final defaultApp = Firebase.app();
      
      final isInitialized = apps.isNotEmpty;
      final hasOptions = defaultApp.options != null;
      final projectId = defaultApp.options.projectId;
      final expectedProjectId = 'ecoplug-9ab21';
      
      return {
        'status': isInitialized && hasOptions && projectId == expectedProjectId ? 'passed' : 'failed',
        'firebase_initialized': isInitialized,
        'has_options': hasOptions,
        'project_id': projectId,
        'project_id_correct': projectId == expectedProjectId,
        'expected_project_id': expectedProjectId,
        'total_apps': apps.length,
        'app_name': defaultApp.name,
      };
    } catch (e) {
      debugPrint('❌ Firebase Core test failed: $e');
      return {
        'status': 'failed',
        'error': e.toString(),
        'firebase_initialized': false,
      };
    }
  }

  /// Test FCM service availability
  Future<Map<String, dynamic>> _testFCMService() async {
    try {
      debugPrint('🔥 Testing FCM service availability...');
      
      final messaging = FirebaseMessaging.instance;
      final isSupported = await messaging.isSupported();
      
      return {
        'status': isSupported ? 'passed' : 'failed',
        'fcm_supported': isSupported,
        'service_available': true,
        'messaging_instance_created': messaging != null,
      };
    } catch (e) {
      debugPrint('❌ FCM service test failed: $e');
      return {
        'status': 'failed',
        'error': e.toString(),
        'fcm_supported': false,
        'service_available': false,
      };
    }
  }

  /// Test FCM token generation
  Future<Map<String, dynamic>> _testTokenGeneration() async {
    try {
      debugPrint('🔥 Testing FCM token generation...');
      
      final messaging = FirebaseMessaging.instance;
      final startTime = DateTime.now();
      
      // Generate token
      final token = await messaging.getToken();
      
      final endTime = DateTime.now();
      final generationTime = endTime.difference(startTime).inMilliseconds;
      
      if (token != null && token.isNotEmpty) {
        debugPrint('🔥 ✅ FCM Token generated successfully!');
        debugPrint('🔥 Token length: ${token.length}');
        debugPrint('🔥 Token preview: ${token.substring(0, 30)}...');
        debugPrint('🔥 Generation time: ${generationTime}ms');
        
        return {
          'status': 'passed',
          'token_generated': true,
          'token': token,
          'token_length': token.length,
          'token_preview': '${token.substring(0, 30)}...',
          'generation_time_ms': generationTime,
          'token_valid_length': token.length > 100, // FCM tokens are typically 152+ characters
        };
      } else {
        debugPrint('❌ FCM token generation failed - token is null or empty');
        return {
          'status': 'failed',
          'token_generated': false,
          'error': 'Token is null or empty',
          'generation_time_ms': generationTime,
        };
      }
    } catch (e) {
      debugPrint('❌ FCM token generation test failed: $e');
      return {
        'status': 'failed',
        'error': e.toString(),
        'token_generated': false,
      };
    }
  }

  /// Test token validation
  Future<Map<String, dynamic>> _testTokenValidation(Map<String, dynamic> previousResults) async {
    try {
      debugPrint('🔥 Testing FCM token validation...');
      
      final tokenTest = previousResults['token_generation_test'] as Map<String, dynamic>?;
      if (tokenTest == null || tokenTest['status'] != 'passed') {
        return {
          'status': 'skipped',
          'reason': 'Token generation test failed',
        };
      }
      
      final token = tokenTest['token'] as String?;
      if (token == null) {
        return {
          'status': 'failed',
          'error': 'No token available for validation',
        };
      }
      
      // Validate token format and characteristics
      final validations = <String, dynamic>{};
      
      // Check token length (FCM tokens are typically 152+ characters)
      validations['length_valid'] = token.length >= 100;
      validations['actual_length'] = token.length;
      validations['expected_min_length'] = 100;
      
      // Check token format (should be base64-like)
      validations['format_valid'] = RegExp(r'^[A-Za-z0-9_-]+$').hasMatch(token);
      
      // Check for common FCM token patterns
      validations['has_colons'] = token.contains(':');
      validations['starts_with_valid_chars'] = RegExp(r'^[A-Za-z0-9]').hasMatch(token);
      
      // Overall validation
      final isValid = validations['length_valid'] == true && 
                     validations['format_valid'] == true;
      
      return {
        'status': isValid ? 'passed' : 'failed',
        'token_valid': isValid,
        'validations': validations,
        'token_preview': '${token.substring(0, 30)}...',
      };
    } catch (e) {
      debugPrint('❌ Token validation test failed: $e');
      return {
        'status': 'failed',
        'error': e.toString(),
      };
    }
  }

  /// Test token refresh functionality
  Future<Map<String, dynamic>> _testTokenRefresh() async {
    try {
      debugPrint('🔥 Testing FCM token refresh...');
      
      final messaging = FirebaseMessaging.instance;
      
      // Get current token
      final originalToken = await messaging.getToken();
      if (originalToken == null) {
        return {
          'status': 'failed',
          'error': 'Cannot test refresh - no original token available',
        };
      }
      
      debugPrint('🔥 Original token: ${originalToken.substring(0, 30)}...');
      
      // Delete current token
      await messaging.deleteToken();
      debugPrint('🔥 Token deleted');
      
      // Wait a moment
      await Future.delayed(const Duration(seconds: 2));
      
      // Get new token
      final newToken = await messaging.getToken();
      if (newToken == null) {
        return {
          'status': 'failed',
          'error': 'Failed to generate new token after refresh',
        };
      }
      
      debugPrint('🔥 New token: ${newToken.substring(0, 30)}...');
      
      final tokensAreDifferent = originalToken != newToken;
      
      return {
        'status': tokensAreDifferent ? 'passed' : 'warning',
        'refresh_successful': newToken.isNotEmpty,
        'tokens_different': tokensAreDifferent,
        'original_token_preview': '${originalToken.substring(0, 30)}...',
        'new_token_preview': '${newToken.substring(0, 30)}...',
        'new_token': newToken,
        'note': tokensAreDifferent ? 'Token refresh successful' : 'Same token returned (may be cached)',
      };
    } catch (e) {
      debugPrint('❌ Token refresh test failed: $e');
      return {
        'status': 'failed',
        'error': e.toString(),
      };
    }
  }

  /// Test notification permissions
  Future<Map<String, dynamic>> _testNotificationPermissions() async {
    try {
      debugPrint('🔥 Testing notification permissions...');
      
      final messaging = FirebaseMessaging.instance;
      final settings = await messaging.getNotificationSettings();
      
      final isAuthorized = settings.authorizationStatus == AuthorizationStatus.authorized;
      
      return {
        'status': isAuthorized ? 'passed' : 'warning',
        'permissions_granted': isAuthorized,
        'authorization_status': settings.authorizationStatus.toString(),
        'alert_setting': settings.alert.toString(),
        'badge_setting': settings.badge.toString(),
        'sound_setting': settings.sound.toString(),
        'announcement_setting': settings.announcement.toString(),
        'car_play_setting': settings.carPlay.toString(),
        'lock_screen_setting': settings.lockScreen.toString(),
        'notification_center_setting': settings.notificationCenter.toString(),
      };
    } catch (e) {
      debugPrint('❌ Permissions test failed: $e');
      return {
        'status': 'failed',
        'error': e.toString(),
        'permissions_granted': false,
      };
    }
  }

  /// Generate test summary
  Map<String, dynamic> _generateTestSummary(Map<String, dynamic> results) {
    final testResults = <String>[];
    var passedCount = 0;
    var failedCount = 0;
    var warningCount = 0;
    var skippedCount = 0;
    
    for (final key in results.keys) {
      if (key.endsWith('_test')) {
        final testResult = results[key] as Map<String, dynamic>?;
        if (testResult != null) {
          final status = testResult['status'] as String?;
          testResults.add(status ?? 'unknown');
          
          switch (status) {
            case 'passed':
              passedCount++;
              break;
            case 'failed':
              failedCount++;
              break;
            case 'warning':
              warningCount++;
              break;
            case 'skipped':
              skippedCount++;
              break;
          }
        }
      }
    }
    
    final totalTests = testResults.length;
    final overallStatus = failedCount > 0 ? 'failed' : 
                         warningCount > 0 ? 'warning' : 'passed';
    
    return {
      'overall_status': overallStatus,
      'total_tests': totalTests,
      'passed': passedCount,
      'failed': failedCount,
      'warnings': warningCount,
      'skipped': skippedCount,
      'success_rate': totalTests > 0 ? (passedCount / totalTests * 100).round() : 0,
      'fcm_token_ready': _isFCMTokenReady(results),
    };
  }

  /// Check if FCM token is ready for use
  bool _isFCMTokenReady(Map<String, dynamic> results) {
    final coreTest = results['firebase_core_test'] as Map<String, dynamic>?;
    final serviceTest = results['fcm_service_test'] as Map<String, dynamic>?;
    final tokenTest = results['token_generation_test'] as Map<String, dynamic>?;
    
    return coreTest?['status'] == 'passed' &&
           serviceTest?['status'] == 'passed' &&
           tokenTest?['status'] == 'passed';
  }

  /// Get current FCM token for external use
  Future<String?> getCurrentFCMToken() async {
    try {
      final messaging = FirebaseMessaging.instance;
      return await messaging.getToken();
    } catch (e) {
      debugPrint('❌ Error getting current FCM token: $e');
      return null;
    }
  }

  /// Quick token generation test
  Future<Map<String, dynamic>> quickTokenTest() async {
    try {
      debugPrint('🔥 Running quick FCM token test...');
      
      final messaging = FirebaseMessaging.instance;
      final token = await messaging.getToken();
      
      if (token != null && token.isNotEmpty) {
        return {
          'success': true,
          'token_generated': true,
          'token': token,
          'token_length': token.length,
          'token_preview': '${token.substring(0, 30)}...',
          'message': 'FCM token generated successfully!',
        };
      } else {
        return {
          'success': false,
          'token_generated': false,
          'error': 'Failed to generate FCM token',
          'message': 'FCM token generation failed',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'message': 'Error during token generation: $e',
      };
    }
  }
}
