import '../api/api_service.dart';
import '../../services/connectivity_service.dart';
import 'token_service.dart';
import '../../features/auth/repositories/auth_repository.dart';
import '../../features/auth/services/auth_service.dart';
import '../../repositories/station_repository.dart';
import '../../features/station/services/station_service.dart';
import '../../features/user/repositories/user_repository.dart';
import '../../features/user/services/user_service.dart';
import '../../repositories/wallet_repository.dart';
import '../../features/wallet/services/wallet_service.dart';

/// Service locator for dependency injection
/// This class provides a centralized way to access services and repositories
class ServiceLocator {
  // Singleton pattern
  static final ServiceLocator _instance = ServiceLocator._internal();
  factory ServiceLocator() => _instance;
  ServiceLocator._internal();

  // Core services
  final ApiService _apiService = ApiService();
  final TokenService _tokenService = TokenService();
  final ConnectivityService _connectivityService = ConnectivityService();

  // Repositories
  late final AuthRepository _authRepository = AuthRepository(_apiService);
  late final StationRepository _stationRepository = StationRepository();
  late final UserRepository _userRepository = UserRepository(_apiService);
  late final WalletRepository _walletRepository = WalletRepository();

  // Feature services
  late final AuthService _authService = AuthService(_authRepository);
  late final StationService _stationService =
      StationService(_connectivityService, _stationRepository, _apiService);
  late final UserService _userService =
      UserService(_userRepository, _connectivityService);
  late final WalletService _walletService = WalletService(
    walletRepository: _walletRepository,
    connectivityService: _connectivityService,
  );

  // Core service getters
  ApiService get apiService => _apiService;
  TokenService get tokenService => _tokenService;
  ConnectivityService get connectivityService => _connectivityService;

  // Feature service getters
  AuthService get authService => _authService;
  StationService get stationService => _stationService;
  UserService get userService => _userService;
  WalletService get walletService => _walletService;

  // Repository getters
  AuthRepository get authRepository => _authRepository;
  StationRepository get stationRepository => _stationRepository;
  UserRepository get userRepository => _userRepository;
  WalletRepository get walletRepository => _walletRepository;

  /// Initialize all services
  Future<void> initialize() async {
    // Initialize connectivity service
    _connectivityService.initialize();

    // Initialize token service
    await _tokenService.initialize();

    // Set up token refresh callback
    _tokenService.onTokenRefreshNeeded = _refreshToken;
  }

  /// Token refresh callback for TokenService
  Future<String?> _refreshToken() async {
    try {
      // Implement token refresh logic here
      // This would typically call an API endpoint to refresh the token
      return null;
    } catch (e) {
      return null;
    }
  }
}
