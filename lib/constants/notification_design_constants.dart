import 'package:flutter/material.dart';

/// Pixel-perfect design constants for Android notifications
/// Ensures visual consistency and exact design implementation
class NotificationDesignConstants {
  // Private constructor to prevent instantiation
  NotificationDesignConstants._();

  /// Color specifications matching Ecoplug brand and Material Design
  static const Color primaryGreen = Color(0xFF8cc051);
  static const Color primaryBlue = Color(0xFF2196F3);
  static const Color primaryOrange = Color(0xFFFF9800);
  static const Color primaryYellow = Color(0xFFFFC107);
  static const Color primaryRed = Color(0xFFF44336);

  // Status colors for charging states
  static const Color activeCharging = Color(0xFF8cc051);
  static const Color chargingComplete = Color(0xFF9E9E9E);
  static const Color chargingPaused = Color(0xFFFF9800);
  static const Color chargingError = Color(0xFFF44336);

  // Progress bar colors based on charge level
  static const Color progressHigh = Color(0xFF8cc051);    // 80%+
  static const Color progressMedium = Color(0xFFFF9800);  // 50-79%
  static const Color progressLow = Color(0xFFFFC107);     // 20-49%
  static const Color progressCritical = Color(0xFFF44336); // <20%
  static const Color progressEmpty = Color(0xFFE0E0E0);

  // Text colors for hierarchy and readability
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF666666);
  static const Color textHint = Color(0xFF999999);
  static const Color textOnPrimary = Color(0xFFFFFFFF);

  // Typography specifications for pixel-perfect text rendering
  // Font weights
  static const FontWeight fontBold = FontWeight.w700;
  static const FontWeight fontSemiBold = FontWeight.w600;
  static const FontWeight fontMedium = FontWeight.w500;
  static const FontWeight fontRegular = FontWeight.w400;
  static const FontWeight fontLight = FontWeight.w300;

  // Font sizes (in sp for Android compatibility)
  static const double titleLarge = 16.0;
  static const double titleMedium = 14.0;
  static const double titleSmall = 12.0;
  static const double bodyLarge = 14.0;
  static const double bodyMedium = 12.0;
  static const double bodySmall = 10.0;
  static const double caption = 10.0;

  // Line heights for optimal readability
  static const double lineHeightTight = 1.2;
  static const double lineHeightNormal = 1.4;
  static const double lineHeightLoose = 1.6;

  // Spacing and layout specifications for consistent positioning
  // Padding values (in dp)
  static const double paddingXSmall = 4.0;
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 12.0;
  static const double paddingLarge = 16.0;
  static const double paddingXLarge = 20.0;
  static const double paddingXXLarge = 24.0;

  // Margin values (in dp)
  static const double marginXSmall = 4.0;
  static const double marginSmall = 8.0;
  static const double marginMedium = 12.0;
  static const double marginLarge = 16.0;
  static const double marginXLarge = 20.0;

  // Element spacing
  static const double elementSpacingTight = 4.0;
  static const double elementSpacingNormal = 8.0;
  static const double elementSpacingLoose = 12.0;

  // Icon specifications for consistent visual elements
  // Icon sizes (in dp)
  static const double iconSmall = 16.0;
  static const double iconMedium = 20.0;
  static const double iconLarge = 24.0;
  static const double iconXLarge = 32.0;

  // Progress bar specifications
  static const int progressBarLength = 24;
  static const String filledBlock = '█';
  static const String emptyBlock = '░';

  // Icon specifications
  static const String activeChargingIcon = '🟢';
  static const String chargingCompleteIcon = '⚪';
  static const String batteryIcon = '🔋';
  static const String powerIcon = '⚡';
  static const String energyIcon = '🔌';
  static const String costIcon = '💰';
  static const String co2Icon = '🌱';
  static const String timerIcon = '⏱️';
  static const String appIcon = '📱';

  /// Helper methods for dynamic color calculation
  static Color getProgressColor(double percentage) {
    if (percentage >= 80) return progressHigh;
    if (percentage >= 50) return progressMedium;
    if (percentage >= 20) return progressLow;
    return progressCritical;
  }

  /// Helper method for status color
  static Color getStatusColor(bool isCharging, bool hasError) {
    if (hasError) return chargingError;
    if (isCharging) return activeCharging;
    return chargingComplete;
  }

  /// Helper method for formatted color hex strings
  static String getColorHex(Color color) {
    return '#${color.toARGB32().toRadixString(16).padLeft(8, '0').substring(2).toUpperCase()}';
  }
}
