import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../repositories/user_repository.dart';
import '../models/user_models.dart';
import '../../../core/models/api_response.dart';
import '../../../services/connectivity_service.dart';
import '../../../shared/models/user.dart';

/// Service for handling user-related operations
class UserService {
  final UserRepository _userRepository;
  final ConnectivityService _connectivityService;

  // Cache for user data
  User? _cachedUser;
  DateTime? _userLastFetched;

  List<Vehicle>? _cachedVehicles;
  DateTime? _vehiclesLastFetched;

  // Stream controller for user updates
  final _userUpdateController = StreamController<User>.broadcast();

  // Stream for user updates
  Stream<User> get userUpdates => _userUpdateController.stream;

  UserService(this._userRepository, this._connectivityService);

  /// Get user profile
  /// Uses caching to reduce API calls
  Future<ApiResponse<User>> getUserProfile({bool forceRefresh = false}) async {
    // Check if we have cached data and it's not too old (less than 5 minutes)
    final now = DateTime.now();
    final cacheValid = _cachedUser != null &&
        _userLastFetched != null &&
        now.difference(_userLastFetched!).inMinutes < 5;

    // Return cached data if valid and not forcing refresh
    if (cacheValid && !forceRefresh) {
      return ApiResponse<User>(
        success: true,
        message: 'User profile retrieved from cache',
        data: _cachedUser!,
      );
    }

    // Check connectivity before making API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      // Return cached data if available, even if old
      if (_cachedUser != null) {
        return ApiResponse<User>(
          success: true,
          message: 'Using cached user profile (offline)',
          data: _cachedUser!,
        );
      } else {
        return ApiResponse<User>(
          success: false,
          message: 'No internet connection and no cached data available',
        );
      }
    }

    // Make API call
    final response = await _userRepository.getUserProfile();

    // Update cache if successful
    if (response.success && response.data != null) {
      _cachedUser = response.data;
      _userLastFetched = now;

      // Save user data to SharedPreferences
      await _saveUserDataToPrefs(response.data!);

      // Notify listeners
      _userUpdateController.add(response.data!);
    }

    return response;
  }

  /// Update user profile
  Future<ApiResponse<User>> updateUserProfile(
      Map<String, dynamic> userData) async {
    // Check connectivity before making API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      return ApiResponse<User>(
        success: false,
        message: 'No internet connection',
      );
    }

    // Make API call
    final response = await _userRepository.updateUserProfile(userData);

    // Update cache if successful
    if (response.success && response.data != null) {
      _cachedUser = response.data;
      _userLastFetched = DateTime.now();

      // Save user data to SharedPreferences
      await _saveUserDataToPrefs(response.data!);

      // Notify listeners
      _userUpdateController.add(response.data!);
    }

    return response;
  }

  /// Get user vehicles
  /// Uses caching to reduce API calls
  Future<ApiResponse<List<Vehicle>>> getUserVehicles(
      {bool forceRefresh = false}) async {
    // Check if we have cached data and it's not too old (less than 5 minutes)
    final now = DateTime.now();
    final cacheValid = _cachedVehicles != null &&
        _vehiclesLastFetched != null &&
        now.difference(_vehiclesLastFetched!).inMinutes < 5;

    // Return cached data if valid and not forcing refresh
    if (cacheValid && !forceRefresh) {
      return ApiResponse<List<Vehicle>>(
        success: true,
        message: 'Vehicles retrieved from cache',
        data: _cachedVehicles!,
      );
    }

    // Check connectivity before making API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      // Return cached data if available, even if old
      if (_cachedVehicles != null) {
        return ApiResponse<List<Vehicle>>(
          success: true,
          message: 'Using cached vehicles (offline)',
          data: _cachedVehicles!,
        );
      } else {
        return ApiResponse<List<Vehicle>>(
          success: false,
          message: 'No internet connection and no cached data available',
        );
      }
    }

    // Make API call
    final response = await _userRepository.getUserVehicles();

    // Update cache if successful
    if (response.success && response.data != null) {
      _cachedVehicles = response.data;
      _vehiclesLastFetched = now;
    }

    return response;
  }

  /// Save vehicle
  Future<ApiResponse<Vehicle>> saveVehicle(
      Map<String, dynamic> vehicleData) async {
    // Check connectivity before making API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      return ApiResponse<Vehicle>(
        success: false,
        message: 'No internet connection',
      );
    }

    // Make API call
    final response = await _userRepository.saveVehicle(vehicleData);

    // Invalidate vehicles cache if successful
    if (response.success) {
      _cachedVehicles = null;
      _vehiclesLastFetched = null;
    }

    return response;
  }

  /// Set default vehicle
  Future<ApiResponse<void>> setDefaultVehicle(String vehicleId) async {
    // Check connectivity before making API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      return ApiResponse<void>(
        success: false,
        message: 'No internet connection',
      );
    }

    // Make API call
    final response = await _userRepository.setDefaultVehicle(vehicleId);

    // Invalidate vehicles cache if successful
    if (response.success) {
      _cachedVehicles = null;
      _vehiclesLastFetched = null;
    }

    return response;
  }

  /// Get promocodes
  Future<ApiResponse<List<Promocode>>> getPromocodes() async {
    // Check connectivity before making API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      return ApiResponse<List<Promocode>>(
        success: false,
        message: 'No internet connection',
      );
    }

    return await _userRepository.getPromocodes();
  }

  /// Verify promocode
  Future<ApiResponse<Promocode>> verifyPromocode(String code) async {
    // Check connectivity before making API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      return ApiResponse<Promocode>(
        success: false,
        message: 'No internet connection',
      );
    }

    return await _userRepository.verifyPromocode(code);
  }

  /// Save user data to SharedPreferences
  Future<void> _saveUserDataToPrefs(User user) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Save user data
      if (user.name != null) {
        await prefs.setString('user_name', user.name!);
      }

      if (user.email != null) {
        await prefs.setString('user_email', user.email!);
      }

      if (user.mobileNumber != null) {
        await prefs.setString('user_phone', user.mobileNumber!);
      }

      await prefs.setInt('user_id', user.id);

      debugPrint('User data saved to SharedPreferences');
    } catch (e) {
      debugPrint('Error saving user data to SharedPreferences: $e');
    }
  }

  /// Clear user cache
  void clearCache() {
    _cachedUser = null;
    _userLastFetched = null;
    _cachedVehicles = null;
    _vehiclesLastFetched = null;
    debugPrint('User cache cleared');
  }

  /// Dispose resources
  void dispose() {
    _userUpdateController.close();
  }
}
