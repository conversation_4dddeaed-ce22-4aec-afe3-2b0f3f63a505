import 'package:flutter/foundation.dart';
import '../../../core/api/api_service.dart';
import '../../../core/api/api_config.dart';
import '../../../core/api/api_exception.dart';
import '../../../core/models/api_response.dart';
import '../models/user_models.dart';
import '../../../shared/models/user.dart';

/// Repository for user-related operations
class UserRepository {
  final ApiService _apiService;

  UserRepository(this._apiService);

  /// Get user profile
  Future<ApiResponse<User>> getUserProfile() async {
    try {
      final response = await _apiService.get(ApiConfig.userProfile);

      if (response['success'] == true && response['user'] != null) {
        final userData = response['user'];

        // Convert to User model
        final user = User.fromJson({
          'id': userData['id'] ?? 0,
          'uid': userData['uid'],
          'mobileNumber': userData['mobile_number'],
          'name': userData['name'],
          'email': userData['email'],
          'domain': userData['domain'],
          'token': userData['token'],
        });

        return ApiResponse<User>(
          success: true,
          message: response['message'] ?? 'User profile retrieved successfully',
          data: user,
        );
      } else {
        return ApiResponse<User>(
          success: false,
          message: response['message'] ?? 'Failed to get user profile',
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during get user profile: ${e.message}');
      return ApiResponse<User>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error during get user profile: $e');
      return ApiResponse<User>(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Update user profile
  Future<ApiResponse<User>> updateUserProfile(
      Map<String, dynamic> userData) async {
    try {
      final response = await _apiService.post(
        ApiConfig.updateProfile,
        data: userData,
      );

      if (response['success'] == true && response['user'] != null) {
        final updatedUserData = response['user'];

        // Convert to User model
        final user = User.fromJson({
          'id': updatedUserData['id'] ?? 0,
          'uid': updatedUserData['uid'],
          'mobileNumber': updatedUserData['mobile_number'],
          'name': updatedUserData['name'],
          'email': updatedUserData['email'],
          'domain': updatedUserData['domain'],
          'token': updatedUserData['token'],
        });

        return ApiResponse<User>(
          success: true,
          message: response['message'] ?? 'User profile updated successfully',
          data: user,
        );
      } else {
        return ApiResponse<User>(
          success: false,
          message: response['message'] ?? 'Failed to update user profile',
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during update user profile: ${e.message}');
      return ApiResponse<User>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error during update user profile: $e');
      return ApiResponse<User>(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Get user vehicles
  Future<ApiResponse<List<Vehicle>>> getUserVehicles() async {
    try {
      final response = await _apiService.get('/vehicles');

      if (response['success'] == true && response['data'] != null) {
        final List<dynamic> vehiclesData = response['data'];
        final vehicles = vehiclesData
            .map((vehicleJson) => Vehicle.fromJson(vehicleJson))
            .toList();

        return ApiResponse<List<Vehicle>>(
          success: true,
          message: response['message'] ?? 'Vehicles retrieved successfully',
          data: vehicles,
        );
      } else {
        return ApiResponse<List<Vehicle>>(
          success: false,
          message: response['message'] ?? 'Failed to get vehicles',
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during get user vehicles: ${e.message}');
      return ApiResponse<List<Vehicle>>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error during get user vehicles: $e');
      return ApiResponse<List<Vehicle>>(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Save vehicle
  Future<ApiResponse<Vehicle>> saveVehicle(
      Map<String, dynamic> vehicleData) async {
    try {
      final response = await _apiService.post(
        '/vehicles/save',
        data: vehicleData,
      );

      if (response['success'] == true && response['data'] != null) {
        final vehicle = Vehicle.fromJson(response['data']);

        return ApiResponse<Vehicle>(
          success: true,
          message: response['message'] ?? 'Vehicle saved successfully',
          data: vehicle,
        );
      } else {
        return ApiResponse<Vehicle>(
          success: false,
          message: response['message'] ?? 'Failed to save vehicle',
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during save vehicle: ${e.message}');
      return ApiResponse<Vehicle>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error during save vehicle: $e');
      return ApiResponse<Vehicle>(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Set default vehicle
  Future<ApiResponse<void>> setDefaultVehicle(String vehicleId) async {
    try {
      final response = await _apiService.post(
        '/vehicles/default',
        data: {'vehicle_id': vehicleId},
      );

      if (response['success'] == true) {
        return ApiResponse<void>(
          success: true,
          message: response['message'] ?? 'Default vehicle set successfully',
        );
      } else {
        return ApiResponse<void>(
          success: false,
          message: response['message'] ?? 'Failed to set default vehicle',
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during set default vehicle: ${e.message}');
      return ApiResponse<void>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error during set default vehicle: $e');
      return ApiResponse<void>(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Get promocodes
  Future<ApiResponse<List<Promocode>>> getPromocodes() async {
    try {
      final response = await _apiService.get('/promocodes');

      if (response['success'] == true && response['data'] != null) {
        final List<dynamic> promocodesData = response['data'];
        final promocodes = promocodesData
            .map((promocodeJson) => Promocode.fromJson(promocodeJson))
            .toList();

        return ApiResponse<List<Promocode>>(
          success: true,
          message: response['message'] ?? 'Promocodes retrieved successfully',
          data: promocodes,
        );
      } else {
        return ApiResponse<List<Promocode>>(
          success: false,
          message: response['message'] ?? 'Failed to get promocodes',
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during get promocodes: ${e.message}');
      return ApiResponse<List<Promocode>>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error during get promocodes: $e');
      return ApiResponse<List<Promocode>>(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Verify promocode
  Future<ApiResponse<Promocode>> verifyPromocode(String code) async {
    try {
      final response = await _apiService.post(
        '/user/promocodes/verify?promo=$code',
        data: {},
      );

      if (response['success'] == true && response['data'] != null) {
        final promocode = Promocode.fromJson(response['data']);

        return ApiResponse<Promocode>(
          success: true,
          message: response['message'] ?? 'Promocode verified successfully',
          data: promocode,
        );
      } else {
        return ApiResponse<Promocode>(
          success: false,
          message: response['message'] ?? 'Invalid promocode',
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during verify promocode: ${e.message}');
      return ApiResponse<Promocode>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error during verify promocode: $e');
      return ApiResponse<Promocode>(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }
}
