import 'package:flutter/foundation.dart';
import '../../../core/api/api_service.dart';
import '../../../core/api/api_config.dart';
import '../../../core/api/api_exception.dart';
import '../../../core/models/api_response.dart';
import '../models/auth_models.dart';
import '../../../shared/models/user.dart';

/// Repository for authentication-related operations
class AuthRepository {
  final ApiService _apiService;

  AuthRepository(this._apiService);

  /// Send OTP to the provided phone number
  Future<ApiResponse<LoginResponse>> login(String phoneNumber) async {
    try {
      // Format the phone number
      final formattedNumber = phoneNumber.startsWith('+91')
          ? phoneNumber.substring(3)
          : phoneNumber;

      // Create login request
      final loginRequest = LoginRequest(phoneNumber: formattedNumber);

      // Make API call
      final response = await _apiService.publicPost(
        '/user/login',
        loginRequest.toJson(),
      );

      debugPrint('\n=== LOGIN RESPONSE ===');
      debugPrint('Response: $response');

      if (response['success'] == true) {
        // Store the phone number as requestId for OTP verification
        return ApiResponse<LoginResponse>(
          success: true,
          message: response['message'] ?? 'OTP sent successfully',
          data: LoginResponse(
            requestId: formattedNumber, // Use phone number as requestId
            message:
                response['data']?['message'] ?? 'OTP sent to your phone number',
          ),
        );
      } else {
        return ApiResponse<LoginResponse>(
          success: false,
          message: response['message'] ?? 'Failed to send OTP',
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during login: ${e.message}');
      return ApiResponse<LoginResponse>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error during login: $e');
      return ApiResponse<LoginResponse>(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Verify OTP
  Future<ApiResponse<VerifyOtpResponse>> verifyOtp(
      String phoneNumber, String otp) async {
    try {
      // Create OTP verification request
      final verifyRequest = VerifyOtpRequest(
        requestId: phoneNumber,
        otp: otp,
      );

      // Make API call
      final response = await _apiService.publicPost(
        '/user/verify-otp',
        verifyRequest.toJson(),
      );

      debugPrint('\n=== VERIFY OTP RESPONSE ===');
      debugPrint('Response: $response');

      if (response['success'] == true) {
        // Save token if present
        if (response['user'] != null && response['user']['token'] != null) {
          await _apiService.saveToken(response['user']['token']);
        }

        return ApiResponse<VerifyOtpResponse>(
          success: true,
          message: response['message'] ?? 'OTP verified successfully',
          data: VerifyOtpResponse.fromJson(response),
        );
      } else {
        return ApiResponse<VerifyOtpResponse>(
          success: false,
          message: response['message'] ?? 'Failed to verify OTP',
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during OTP verification: ${e.message}');
      return ApiResponse<VerifyOtpResponse>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error during OTP verification: $e');
      return ApiResponse<VerifyOtpResponse>(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Get user profile
  Future<ApiResponse<User>> getUserProfile() async {
    try {
      final response = await _apiService.get(ApiConfig.userProfile);

      if (response['success'] == true && response['user'] != null) {
        return ApiResponse<User>(
          success: true,
          message: 'User profile retrieved successfully',
          data: User.fromJson(response['user']),
        );
      } else {
        return ApiResponse<User>(
          success: false,
          message: response['message'] ?? 'Failed to get user profile',
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during get user profile: ${e.message}');
      return ApiResponse<User>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error during get user profile: $e');
      return ApiResponse<User>(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Update user profile
  Future<ApiResponse<User>> updateUserProfile(
      Map<String, dynamic> userData) async {
    try {
      final response = await _apiService.post(
        ApiConfig.updateProfile,
        data: userData,
      );

      if (response['success'] == true && response['user'] != null) {
        return ApiResponse<User>(
          success: true,
          message: response['message'] ?? 'Profile updated successfully',
          data: User.fromJson(response['user']),
        );
      } else {
        return ApiResponse<User>(
          success: false,
          message: response['message'] ?? 'Failed to update profile',
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during update profile: ${e.message}');
      return ApiResponse<User>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error during update profile: $e');
      return ApiResponse<User>(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Logout user
  Future<ApiResponse<void>> logout() async {
    try {
      await _apiService.clearToken();
      return ApiResponse<void>(
        success: true,
        message: 'Logged out successfully',
      );
    } catch (e) {
      debugPrint('Error during logout: $e');
      return ApiResponse<void>(
        success: false,
        message: 'An error occurred during logout',
      );
    }
  }

  /// Check if user is logged in
  Future<bool> isLoggedIn() async {
    return await _apiService.isLoggedIn();
  }
}
