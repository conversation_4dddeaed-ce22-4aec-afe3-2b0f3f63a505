import '../../../shared/models/user.dart';

/// Authentication state type
enum AuthStateType { initial, loading, authenticated, unauthenticated, error }

/// Authentication state class
class AuthState {
  final bool isLoading;
  final bool isAuthenticated;
  final User? user;
  final String? errorMessage;
  final AuthStateType type;

  const AuthState({
    this.isLoading = false,
    this.isAuthenticated = false,
    this.user,
    this.errorMessage,
    this.type = AuthStateType.initial,
  });

  // Initial state
  factory AuthState.initial() => const AuthState(
        type: AuthStateType.initial,
      );

  // Loading state
  factory AuthState.loading() => const AuthState(
        isLoading: true,
        type: AuthStateType.loading,
      );

  // Authenticated state
  factory AuthState.authenticated(User user) => AuthState(
        isAuthenticated: true,
        user: user,
        type: AuthStateType.authenticated,
      );

  // Unauthenticated state
  factory AuthState.unauthenticated() => const AuthState(
        isAuthenticated: false,
        type: AuthStateType.unauthenticated,
      );

  // Error state
  factory AuthState.error(String message) => AuthState(
        errorMessage: message,
        isAuthenticated: false,
        type: AuthStateType.error,
      );

  // Copy with method
  AuthState copyWith({
    bool? isLoading,
    bool? isAuthenticated,
    User? user,
    String? errorMessage,
    AuthStateType? type,
  }) {
    return AuthState(
      isLoading: isLoading ?? this.isLoading,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      user: user ?? this.user,
      errorMessage: errorMessage,
      type: type ?? this.type,
    );
  }
}
