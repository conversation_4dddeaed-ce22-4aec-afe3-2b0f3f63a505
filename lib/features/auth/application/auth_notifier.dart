import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import '../../../shared/models/user.dart';
import '../../../models/api_response.dart';
import '../../../models/auth/auth_models.dart';
import '../domain/auth_state.dart';
import '../../../services/auth/auth_service.dart';
import '../../../services/auth_manager.dart';
import '../../../providers/core_providers.dart';

/// Provider for authentication state management
final authProvider =
    StateNotifierProvider<AuthNotifier, AsyncValue<AuthState>>((ref) {
  final authService = ref.watch(authServiceProvider);
  return AuthNotifier(authService);
});

/// Authentication state notifier
class AuthNotifier extends StateNotifier<AsyncValue<AuthState>> {
  final AuthService _authService;
  final AuthManager _authManager = AuthManager();

  AuthNotifier(this._authService) : super(const AsyncValue.loading()) {
    // Initialize the auth state
    _initializeAuthState();
  }

  /// Initialize the authentication state with optimized parallel validation
  Future<void> _initializeAuthState() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final stopwatch = Stopwatch()..start();
      debugPrint(
          '\n🔐 ===== INITIALIZING AUTH STATE WITH PARALLEL VALIDATION =====');

      try {
        // PERFORMANCE OPTIMIZATION: Execute auth checks in parallel
        final authValidationTasks = [
          // Task 1: Check login status
          () async {
            final isLoggedIn = await _authManager.isLoggedIn();
            debugPrint('🔐 Login status check completed: $isLoggedIn');
            return {'type': 'login_status', 'data': isLoggedIn};
          }(),

          // Task 2: Get token (if exists)
          () async {
            final token = await _authManager.getToken();
            debugPrint(
                '🔐 Token retrieval completed: ${token != null ? "Available" : "Missing"}');
            return {'type': 'token', 'data': token};
          }(),

          // Task 3: Get user data (if exists)
          () async {
            final userData = await _authManager.getUserData();
            debugPrint(
                '🔐 User data retrieval completed: ${userData != null ? "Available" : "Missing"}');
            return {'type': 'user_data', 'data': userData};
          }(),
        ];

        // Wait for all auth validation tasks to complete in parallel
        final authResults = await Future.wait(authValidationTasks);

        // Process parallel results
        bool isLoggedIn = false;
        String? token;
        Map<String, dynamic>? userData;

        for (final result in authResults) {
          final resultMap = result as Map<String, dynamic>;
          switch (resultMap['type']) {
            case 'login_status':
              isLoggedIn = resultMap['data'] as bool;
              break;
            case 'token':
              token = resultMap['data'] as String?;
              break;
            case 'user_data':
              userData = resultMap['data'] as Map<String, dynamic>?;
              break;
          }
        }

        debugPrint(
            '🔐 Parallel validation results - LoggedIn: $isLoggedIn, Token: ${token != null}, UserData: ${userData != null}');

        if (isLoggedIn) {
          // Validate token format if it exists
          if (token != null) {
            final isTokenValid = await _authManager.isTokenValid();
            debugPrint('🔐 Token format validation result: $isTokenValid');

            if (!isTokenValid) {
              debugPrint('🔐 ❌ Token format is invalid, clearing auth state');
              await _authManager.logout();
              stopwatch.stop();
              debugPrint(
                  '🔐 ===== AUTH INITIALIZATION COMPLETED IN ${stopwatch.elapsedMilliseconds}ms (UNAUTHENTICATED) =====\n');
              return AuthState.unauthenticated();
            }
          }

          // Create user profile from cached data or fetch if needed
          User? userProfile;
          if (userData != null) {
            try {
              userProfile = User.fromJson(userData);
              debugPrint(
                  '🔐 ✅ User profile created from cached data: ${userProfile.name}');
            } catch (e) {
              debugPrint('🔐 ❌ Error creating user from cached data: $e');
              userProfile = await _getUserProfile();
            }
          } else {
            userProfile = await _getUserProfile();
          }

          if (userProfile != null) {
            stopwatch.stop();
            debugPrint(
                '🔐 ✅ User authenticated with profile: ${userProfile.name}');
            debugPrint('🔐 ✅ User ID: ${userProfile.id}');
            debugPrint('🔐 ✅ User Email: ${userProfile.email}');
            debugPrint(
                '🔐 ===== AUTH INITIALIZATION COMPLETED IN ${stopwatch.elapsedMilliseconds}ms (AUTHENTICATED) =====\n');
            return AuthState.authenticated(userProfile);
          } else {
            debugPrint(
                '🔐 ❌ User logged in but no profile data found, clearing auth state');
            await _authManager.logout();
          }
        } else {
          debugPrint('🔐 ❌ User not logged in');
        }
      } catch (e) {
        debugPrint('🔐 ❌ Error during auth state initialization: $e');
        // Clear any corrupted auth state
        try {
          await _authManager.logout();
        } catch (logoutError) {
          debugPrint('🔐 ❌ Error during cleanup logout: $logoutError');
        }
      }

      stopwatch.stop();
      debugPrint(
          '🔐 ===== AUTH INITIALIZATION COMPLETED IN ${stopwatch.elapsedMilliseconds}ms (UNAUTHENTICATED) =====\n');
      return AuthState.unauthenticated();
    });
  }

  /// Sign in with phone number and OTP
  Future<void> signInWithOtp({
    required String phoneNumber,
    required String otp,
  }) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      try {
        debugPrint('🔐 ===== VERIFYING OTP =====');
        // Verify OTP using the fixed auth service
        final response = await _authService.verifyOtp(phoneNumber, otp);

        if (response['success'] == true) {
          // Get user data from the response
          final userData = response['data'] ?? response['user'];

          debugPrint('🔐 OTP verification successful');
          debugPrint('🔐 User data: $userData');

          if (userData != null) {
            // Extract token from user data (token is inside user object)
            final token = userData['token'];
            debugPrint(
                '🔐 Token from user data: ${token != null ? "Available" : "Missing"}');

            if (token != null && token.isNotEmpty) {
              // CRITICAL: Save login state using AuthManager
              await _authManager.saveLoginState(
                token: token,
                userData: userData,
              );
              debugPrint('🔐 ✅ Login state saved via AuthManager');
            } else {
              // Even without token, save user data for session persistence
              await _authManager.saveLoginState(
                token:
                    'session_${DateTime.now().millisecondsSinceEpoch}', // Generate session ID
                userData: userData,
              );
              debugPrint('🔐 ✅ Session state saved (no token provided by API)');
            }

            // Convert to User model
            final user = User.fromJson(userData);
            debugPrint('🔐 ✅ User authenticated: ${user.name}');
            return AuthState.authenticated(user);
          }
          return AuthState.error(
              'Failed to get user profile from API response');
        }
        return AuthState.error(
            response['message'] ?? 'OTP verification failed');
      } catch (e) {
        debugPrint('🔐 ❌ OTP verification error: $e');
        return AuthState.error(e.toString());
      }
    });
  }

  /// Send OTP to phone number
  Future<ApiResponse<LoginResponse>> sendOtp(String phoneNumber) async {
    try {
      // Send OTP using the fixed auth service
      final response = await _authService.sendOtp(phoneNumber);

      // Convert the response to the expected format
      return ApiResponse<LoginResponse>(
        success: response['success'] ?? false,
        message: response['message'] ?? 'Unknown error',
        data: response['success'] == true
            ? LoginResponse(
                requestId: phoneNumber,
                message: response['message'] ?? 'OTP sent successfully',
              )
            : null,
      );
    } catch (e) {
      debugPrint('Error sending OTP: $e');
      return ApiResponse<LoginResponse>(
        success: false,
        message: 'Failed to send OTP: $e',
      );
    }
  }

  /// Sign out the current user
  Future<void> signOut() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      debugPrint('🔐 ===== SIGNING OUT USER =====');
      // Use AuthManager for comprehensive logout
      await _authManager.logout();
      debugPrint('🔐 ===== USER SIGNED OUT =====');
      return AuthState.unauthenticated();
    });
  }

  /// Refresh authentication state (useful for checking after app resume)
  Future<void> refreshAuthState() async {
    debugPrint('🔐 ===== REFRESHING AUTH STATE =====');
    await _initializeAuthState();
  }

  /// Validate token with server and update auth state accordingly
  Future<bool> validateTokenWithServer() async {
    debugPrint('🔐 ===== VALIDATING TOKEN WITH SERVER =====');

    try {
      final isValid = await _authManager.validateTokenWithServer();

      if (!isValid) {
        debugPrint(
            '🔐 ❌ Server validation failed, updating auth state to unauthenticated');
        state = AsyncValue.data(AuthState.unauthenticated());
      } else {
        debugPrint('🔐 ✅ Server validation successful');
      }

      return isValid;
    } catch (e) {
      debugPrint('🔐 ❌ Error during server validation: $e');
      // On error, assume token is invalid and clear auth state
      state = AsyncValue.data(AuthState.unauthenticated());
      return false;
    }
  }

  /// Check if current auth state is valid
  bool get isAuthenticated {
    return state.when(
      data: (authState) => authState.isAuthenticated,
      loading: () => false,
      error: (_, __) => false,
    );
  }

  /// Get current user if authenticated
  User? get currentUser {
    return state.when(
      data: (authState) => authState.user,
      loading: () => null,
      error: (_, __) => null,
    );
  }

  /// Helper method to get user profile
  Future<User?> _getUserProfile() async {
    try {
      // Use AuthManager to get user data (more reliable)
      final userData = await _authManager.getUserData();
      debugPrint('🔐 Retrieved user data: $userData');

      if (userData != null) {
        return User.fromJson(userData);
      }
      return null;
    } catch (e) {
      debugPrint('🔐 Error getting user profile in AuthNotifier: $e');
      return null;
    }
  }
}
