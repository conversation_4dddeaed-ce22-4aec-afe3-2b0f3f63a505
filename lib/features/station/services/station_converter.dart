import 'package:flutter/material.dart';
import 'package:ecoplug/models/station.dart';
import 'package:ecoplug/models/evse.dart';
import 'package:ecoplug/models/station/station_details_response.dart'
    as station_details;
import 'package:ecoplug/models/station/paginated_stations_response.dart'
    as paginated;
import 'package:ecoplug/models/nearest_station_response.dart' as nearest;

/// Utility class for converting station data between different formats
class StationConverter {
  /// Convert station details response to a Station model
  static Station convertToStation(
      station_details.StationDetailsData data, String uid) {
    // Debug log the data
    debugPrint('Converting station data to Station model for UID: $uid');

    // Validate required fields
    if (data.name == null || data.name!.isEmpty) {
      throw Exception('Station name is missing in API response');
    }

    if (data.address == null || data.address!.isEmpty) {
      throw Exception('Station address is missing in API response');
    }

    if (data.latitude == null) {
      throw Exception('Station latitude is missing in API response');
    }

    if (data.longitude == null) {
      throw Exception('Station longitude is missing in API response');
    }

    // Extract connectors from EVSEs
    List<station_details.Connector> connectors = [];
    List<Evse> evsesList = [];

    if (data.evses != null && data.evses!.isNotEmpty) {
      // Process EVSEs
      data.evses!.forEach((evsesUid, evse) {
        // Skip EVSEs with no connectors
        if (evse.connectors == null || evse.connectors!.isEmpty) {
          return;
        }

        // Add EVSE to the list
        evsesList.add(Evse(
          id: evsesUid,
          name: evse.name ?? '',
          status: evse.connectors?.firstOrNull?.status ?? '',
          // Use real API data only - if maxPower is null, use 0.0 only for model compatibility
          power: evse.maxPower?.toDouble() ??
              0.0, // Model requires non-null, use 0.0 when API provides no data
          connectorType: evse.connectors?.firstOrNull?.type ?? '',
          availableConnectors:
              evse.connectors?.where((c) => c.status == 'Available').length ??
                  0,
        ));

        // Process connectors
        for (var connector in evse.connectors!) {
          try {
            // Improved power value extraction
            // Improved power value extraction
            int? maxElectricPower;

            // Log the raw value for debugging
            debugPrint(
                'Connector maxElectricPower: ${connector.maxElectricPower}');

            if (connector.maxElectricPower != null) {
              // Direct value from API - this is the primary source of truth
              maxElectricPower = connector.maxElectricPower;
              debugPrint(
                  'Using maxElectricPower directly from API: $maxElectricPower');
            }

            // Create a new connector with the correct data
            connectors.add(station_details.Connector(
                connectorId: connector.connectorId,
                type: connector.type,
                status: connector.status,
                label: connector.label,
                maxElectricPower: maxElectricPower,
                powerOutput: connector
                    .powerOutput, // Pass connector-specific power output
                standard: connector.standard,
                price: connector.price,
                priceLabel: connector.priceLabel,
                pricePerUnit: connector.pricePerUnit,
                icon: connector.icon,
                evsesUid: connector.evsesUid,
                soc: connector.soc));
          } catch (e) {
            debugPrint('Error processing connector: $e');
          }
        }
      });
    }

    // Verify we have connectors
    if (connectors.isEmpty) {
      debugPrint('Warning: No connectors found in station details response');
    }

    // Get station image - ONLY use real API images
    List<String> stationImages = [];
    if (data.images != null &&
        data.images!.isNotEmpty &&
        data.images!.contains('http')) {
      stationImages = [data.images!];
    }
    // DELETED: No default images - empty list when no real images

    // Extract connector types with their icons if available in the data
    // We don't need to extract connector types with icons as the StationDetailsData
    // doesn't have a types field. Instead, we'll use the connector data directly.
    List<dynamic>? typesWithIcons;

    // Validate that we have a real UID
    if (uid.isEmpty) {
      throw Exception(
          'Station UID is missing. Cannot create Station object without a valid UID.');
    }

    // Create and return the Station object
    return Station(
      id: uid, // Always use the real UID as the ID
      uid: uid, // Always use the real UID
      name: data.name!, // Required field - already validated above
      address: data.address!, // Required field - already validated above
      city: data.city, // Real API data, null if not provided
      state: data.state, // Real API data, null if not provided
      images: stationImages,
      latitude:
          data.latitude!.toDouble(), // Required field - already validated above
      longitude: data.longitude!
          .toDouble(), // Required field - already validated above
      distance: 0.0,
      status: data.openStatus == true ? 'Available' : 'Unavailable',
      rating: data.rate?.toDouble() ?? 0.0,
      reviews: data.rateTotal ?? 0,
      openingTimes:
          data.openingTimes, // Use real API data, null if not provided
      openStatus: data.openStatus,
      evses: evsesList,
      connectors: connectors.map((c) {
        // Enhance connector with icon if available
        var mainConnector = c.toMainConnector();

        // No need to look for icons in typesWithIcons as it's not available
        // We'll use the connector's own icon if available

        // If connector has its own icon, use that instead
        if (c.icon != null && c.icon!.isNotEmpty) {
          mainConnector.iconUrl = c.icon;
          debugPrint('Using connector-specific icon: ${c.icon}');
        }

        return mainConnector;
      }).toList(),
      mapPinUrl: null,
      focusedMapPinUrl: null,
      types: typesWithIcons,
    );
  }

  /// Convert a PaginatedStation to a Station model
  static Station fromPaginatedStation(
      paginated.PaginatedStation paginatedStation) {
    // Debug logging for UID extraction
    debugPrint('Converting PaginatedStation to Station model');
    debugPrint('Station ID: ${paginatedStation.stationId}');
    debugPrint('Station UID: ${paginatedStation.uid}');

    // Accept any UID from the API
    final String currentUid = paginatedStation.uid ?? '';
    if (currentUid.isEmpty) {
      debugPrint('INFO: PaginatedStation has no UID');
    } else {
      debugPrint('PaginatedStation UID: ${paginatedStation.uid}');
    }

    // Extract connector types
    List<paginated.ConnectorType> connectorTypes =
        paginatedStation.getConnectorTypes();
    debugPrint('Found ${connectorTypes.length} connector types');

    // Convert connector types to Connector objects
    List<Connector> connectors = connectorTypes.map((connectorType) {
      // Create a unique ID for the connector
      final String connectorId =
          'conn-${paginatedStation.stationId}-${connectorType.name ?? "unknown"}';

      // Extract power value ONLY from direct API data - no parsing from names
      String? powerValue = '';
      int? maxElectricPower;

      // CRITICAL: Only use direct power values from API, no extraction from names or patterns
      if (connectorType.power?.isNotEmpty ?? false) {
        powerValue = connectorType.power;
        debugPrint('🔍 Using direct power value from API: $powerValue');
      } else {
        debugPrint(
            '❌ No power value available from API for connector type: ${connectorType.name}');
        powerValue = ''; // Keep empty when no API data available
      }

      // Only use maxElectricPower if directly provided by API
      if (connectorType.maxElectricPower != null &&
          connectorType.maxElectricPower! > 0) {
        maxElectricPower = connectorType.maxElectricPower;
        debugPrint(
            '✅ Using direct maxElectricPower from API: $maxElectricPower');
      } else {
        debugPrint(
            '❌ No maxElectricPower available from API for connector type: ${connectorType.name}');
        maxElectricPower = null; // Keep null when no API data available
      }

      // Log connector details for debugging
      debugPrint(
          'Connector type: ${connectorType.name ?? "unknown"}, power: $powerValue, maxPower: $maxElectricPower');
      debugPrint('Connector icon URL: ${connectorType.icon ?? "none"}');

      return Connector(
        id: connectorId,
        name: connectorType.name ?? '',
        type: connectorType.name ?? '',
        price: 0.0,
        power: powerValue,
        totalGuns: connectorType.guns ?? 1,
        availableGuns: connectorType.availableGuns ?? 0,
        icon: connectorType.icon,
        iconUrl: connectorType.icon,
        maxElectricPower: maxElectricPower,
        status: paginatedStation.status,
      );
    }).toList();

    // Create Station object
    return Station(
      id: paginatedStation.stationId?.toString() ?? '',
      name: paginatedStation.name ?? '',
      address: paginatedStation.address ?? '',
      city: paginatedStation.city,
      latitude: paginatedStation.latitude ?? 0.0,
      longitude: paginatedStation.longitude ?? 0.0,
      distance: paginatedStation.distance ?? 0.0,
      status: paginatedStation.status ?? '',
      rating: paginatedStation.rating ?? 0.0,
      reviews: paginatedStation.reviewCount ?? 0,
      connectors: connectors,
      images:
          paginatedStation.imageUrl != null ? [paginatedStation.imageUrl!] : [],
      evses: [],
      uid: paginatedStation.uid ?? '',
      types: paginatedStation.types,
    );
  }

  /// Convert a NearestStation to a Station model
  static Station fromNearestStation(nearest.NearestStation nearestStation) {
    // Debug logging for UID extraction
    debugPrint('Converting NearestStation to Station model');
    debugPrint('Station ID: ${nearestStation.stationId}');
    debugPrint('Station UID: ${nearestStation.uid}');

    // Accept any UID from the API
    final String currentUid = nearestStation.uid;
    if (currentUid.isEmpty) {
      debugPrint('INFO: NearestStation has no UID');
    } else {
      debugPrint('NearestStation UID: ${nearestStation.uid}');
    }

    // Extract connector types
    List<Connector> connectors = [];

    if (nearestStation.types != null) {
      // Handle both array and object formats
      if (nearestStation.types is List) {
        final typesList = nearestStation.types as List;
        for (var type in typesList) {
          if (type is Map<String, dynamic>) {
            connectors.add(_createConnectorFromType(
                type, nearestStation.stationId?.toString()));
          }
        }
      } else if (nearestStation.types is Map) {
        final typesMap = nearestStation.types as Map;
        for (var entry in typesMap.entries) {
          if (entry.value is Map<String, dynamic>) {
            connectors.add(_createConnectorFromType(
                entry.value, nearestStation.stationId?.toString()));
          }
        }
      }
    }

    // Create Station object - skip validation to allow API data through
    return Station(
      id: nearestStation.stationId?.toString() ?? '',
      name: nearestStation.name ?? '',
      address: nearestStation.address ?? '',
      city: nearestStation.city,
      latitude: nearestStation.latitude ?? 0.0,
      longitude: nearestStation.longitude ?? 0.0,
      distance: nearestStation.distance ?? 0.0,
      status: nearestStation.status ?? '',
      rating: 0.0,
      reviews: 0,
      connectors: connectors,
      images: [],
      evses: [],
      uid: nearestStation.uid,
      types: nearestStation.types,
    );
  }

  /// Helper method to create a Connector from a type object
  static Connector _createConnectorFromType(
      Map<String, dynamic> type, String? stationId) {
    final String connectorId =
        'conn-${stationId ?? "unknown"}-${type['name'] ?? "unknown"}';

    // Extract power value ONLY from direct API data - no parsing from names
    String powerValue = '';
    int? maxElectricPower;

    // CRITICAL: Only use direct power values from API, no extraction from names or patterns
    if (type['power']?.toString().isNotEmpty ?? false) {
      powerValue = type['power'].toString();
      debugPrint('🔍 Using direct power value from API: $powerValue');
    } else {
      debugPrint(
          '❌ No power value available from API for connector type: ${type['name']}');
      powerValue = ''; // Keep empty when no API data available
    }

    // Only use maxElectricPower if directly provided by API
    if (type['maxElectricPower'] != null && type['maxElectricPower'] > 0) {
      maxElectricPower = type['maxElectricPower'];
      debugPrint('✅ Using direct maxElectricPower from API: $maxElectricPower');
    } else {
      debugPrint(
          '❌ No maxElectricPower available from API for connector type: ${type['name']}');
      maxElectricPower = null; // Keep null when no API data available
    }

    // IMPROVED: Handle missing connector name/type with fallback
    String connectorName = type['name']?.toString() ?? 'Standard';
    if (connectorName.isEmpty) {
      connectorName = 'Standard';
      debugPrint('Empty connector name, using fallback: $connectorName');
    }

    return Connector(
      id: connectorId,
      name: connectorName,
      type: connectorName,
      price: 0.0, // Price not available in this API response
      power: powerValue,
      totalGuns: type['guns'] ?? 1,
      availableGuns: type['available_guns'] ?? 0,
      icon: type['icon'],
      iconUrl: type['icon'],
      maxElectricPower: maxElectricPower,
      // IMPROVED: Provide fallback status instead of throwing error
      status: type['status']?.toString() ?? 'Available',
    );
  }

  /// Format the connector availability status for display
  static String formatConnectorStatus(String? status) {
    if (status == null || status.isEmpty) {
      return 'Unknown';
    }

    // Check for exact match with 'Available' (case-insensitive)
    if (status.toLowerCase() == 'available') {
      return 'Available';
    }

    // Check for other status values
    if (status.toLowerCase().contains('charging')) {
      return 'In Use';
    }

    if (status.toLowerCase().contains('fault') ||
        status.toLowerCase().contains('error')) {
      return 'Fault';
    }

    if (status.toLowerCase().contains('unavailable') ||
        status.toLowerCase().contains('offline')) {
      return 'Unavailable';
    }

    // Default fallback
    return status;
  }

  /// Format the full address from station details
  static String formatFullAddress(station_details.StationDetailsData data) {
    List<String> addressParts = [];

    if (data.address != null && data.address!.isNotEmpty) {
      addressParts.add(data.address!);
    }

    if (data.city != null && data.city!.isNotEmpty) {
      addressParts.add(data.city!);
    }

    if (data.state != null && data.state!.isNotEmpty) {
      addressParts.add(data.state!);
    }

    if (addressParts.isEmpty) {
      throw FormatException(
          'StationDetailsData address, city, and state are all null or empty');
    }

    return addressParts.join(', ');
  }

  /// Helper method to format power values consistently - ONLY REAL API DATA
  static String formatPower(dynamic rawValue) {
    // CRITICAL: Only use real API data, no defaults or fallbacks
    if (rawValue == null) {
      debugPrint(
          'formatPower: No power value from API - returning empty string');
      return ''; // Return empty string instead of 'N/A'
    }

    if (rawValue is int || rawValue is double) {
      if (rawValue <= 0) {
        debugPrint(
            'formatPower: Zero or negative power value from API: $rawValue - returning empty string');
        return ''; // Return empty string for invalid values
      }
      debugPrint('formatPower: Using real API power value: $rawValue kW');
      return '$rawValue kW';
    } else if (rawValue is String) {
      // Check if string already contains kW
      if (rawValue.toLowerCase().contains('kw')) {
        debugPrint('formatPower: Using real API power string: $rawValue');
        return rawValue;
      }

      // Try to parse numeric value
      final num? parsedValue = double.tryParse(rawValue);
      if (parsedValue != null && parsedValue > 0) {
        debugPrint('formatPower: Parsed real API power value: $parsedValue kW');
        return '$parsedValue kW';
      }

      // If string is not parseable as power, return empty
      debugPrint(
          'formatPower: Cannot parse power string from API: $rawValue - returning empty string');
      return '';
    }

    debugPrint(
        'formatPower: Unknown power value type from API: ${rawValue.runtimeType} - returning empty string');
    return ''; // Return empty string instead of 'N/A'
  }
}
