import '../../../models/station.dart';
import 'station_list_mode.dart';

/// Station state class
class StationState {
  final bool isLoading;
  final List<Station> stations;
  final String? errorMessage;
  final bool isEmpty;
  final StationListMode mode;
  final int currentPage;
  final int totalPages;
  final String? currentSearchQuery;

  StationState({
    this.isLoading = false,
    List<Station>? stations,
    this.errorMessage,
    this.isEmpty = false,
    this.mode = StationListMode.list,
    this.currentPage = 1,
    this.totalPages = 1,
    this.currentSearchQuery,
  }) : stations = stations ?? <Station>[];

  // Initial state
  factory StationState.initial() => StationState();

  // Loading state
  factory StationState.loading() => StationState(isLoading: true);

  // Loaded state
  factory StationState.loaded(List<Station> stations) => StationState(
        stations: List<Station>.from(stations),
      );

  // Empty state
  factory StationState.empty() => StationState(isEmpty: true);

  // Error state
  factory StationState.error(String message) => StationState(
        errorMessage: message,
      );

  // Copy with method
  StationState copyWith({
    bool? isLoading,
    List<Station>? stations,
    String? errorMessage,
    bool? isEmpty,
    StationListMode? mode,
    int? currentPage,
    int? totalPages,
    String? currentSearchQuery,
  }) {
    return StationState(
      isLoading: isLoading ?? this.isLoading,
      stations: stations != null
          ? List<Station>.from(stations)
          : List<Station>.from(this.stations),
      errorMessage: errorMessage,
      isEmpty: isEmpty ?? this.isEmpty,
      mode: mode ?? this.mode,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      currentSearchQuery: currentSearchQuery ?? this.currentSearchQuery,
    );
  }
}
