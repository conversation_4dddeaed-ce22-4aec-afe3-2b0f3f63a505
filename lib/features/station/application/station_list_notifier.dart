import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ecoplug/models/station.dart';
import 'package:ecoplug/providers/providers.dart';

class StationsState {
  final List<Station> stations;
  final bool isLoading;
  final bool isLoadingMore;
  final bool hasMore;
  final int currentPage;
  final String? errorMessage;
  final String searchQuery;
  final String selectedFilter;

  StationsState({
    List<Station>? stations,
    this.isLoading = false,
    this.isLoadingMore = false,
    this.hasMore = true,
    this.currentPage = 1,
    this.errorMessage,
    this.searchQuery = '',
    this.selectedFilter = 'All',
  }) : stations = stations ?? <Station>[];

  StationsState copyWith({
    List<Station>? stations,
    bool? isLoading,
    bool? isLoadingMore,
    bool? hasMore,
    int? currentPage,
    String? errorMessage,
    String? searchQuery,
    String? selectedFilter,
  }) {
    return StationsState(
      stations: stations != null
          ? List<Station>.from(stations)
          : List<Station>.from(this.stations),
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      hasMore: hasMore ?? this.hasMore,
      currentPage: currentPage ?? this.currentPage,
      errorMessage: errorMessage,
      searchQuery: searchQuery ?? this.searchQuery,
      selectedFilter: selectedFilter ?? this.selectedFilter,
    );
  }
}

class StationsNotifier extends StateNotifier<StationsState> {
  final Ref ref;
  final int limit;

  StationsNotifier(this.ref, {this.limit = 20}) : super(StationsState());

  Future<void> loadStations({bool isInitialLoad = false}) async {
    if (isInitialLoad) {
      state = state.copyWith(isLoading: true, errorMessage: null);
    } else if (state.isLoadingMore || !state.hasMore) {
      return;
    } else {
      state = state.copyWith(isLoadingMore: true);
    }

    try {
      final stationService = ref.read(stationServiceProvider);
      final page = isInitialLoad ? 1 : state.currentPage + 1;

      List<Station> newStations;
      bool hasMorePages = false;

      if (state.searchQuery.isNotEmpty) {
        // Search returns ApiResponse<List<StationDetail>>
        final searchResponse =
            await stationService.searchStations(state.searchQuery);
        if (searchResponse.success && searchResponse.data != null) {
          // CRITICAL: Only convert stations with complete data - no defaults
          newStations = searchResponse.data!.where((stationDetail) {
            // Validate essential data before conversion
            bool isValid = stationDetail.uid.isNotEmpty &&
                stationDetail.name.isNotEmpty &&
                stationDetail.address.isNotEmpty &&
                stationDetail.coordinates.latitude != 0.0 &&
                stationDetail.coordinates.longitude != 0.0;

            if (!isValid) {
              debugPrint(
                  '❌ Excluding search result with missing data: ${stationDetail.name}');
            }
            return isValid;
          }).map((stationDetail) {
            return Station(
              id: stationDetail.uid,
              name: stationDetail.name,
              address: stationDetail.address,
              latitude: stationDetail.coordinates.latitude,
              longitude: stationDetail.coordinates.longitude,
              distance: 0.0, // Distance not available in search results
              // Use status directly since it's non-nullable
              status: stationDetail.status,
              // Use rating directly, fallback to 0.0 if null
              rating: stationDetail.rating ?? 0.0,
              // Use review count directly, fallback to 0 if null
              reviews: stationDetail.totalReviews ?? 0,
              images: stationDetail.images ?? [],
              evses: [],
              connectors: [], // Will be populated from station details
              uid: stationDetail.uid,
            );
          }).toList();
          hasMorePages =
              newStations.length >= limit; // Assume more if we got a full page
        } else {
          newStations = [];
          hasMorePages = false;
        }
      } else {
        // Paginated fetch returns PaginatedStationsResponse
        final response = await stationService.fetchStationsPaginated(
          page: page,
          limit: limit,
          filters: state.selectedFilter != 'All'
              ? {'status': state.selectedFilter.toLowerCase()}
              : null,
        );

        // Convert PaginatedStation to Station - IMPROVED validation with fallbacks
        newStations = response.data
                ?.where((paginatedStation) {
                  // IMPROVED: More lenient validation - only exclude stations with critical missing data
                  bool isValid = paginatedStation.name != null &&
                      paginatedStation.name!.isNotEmpty &&
                      paginatedStation.latitude != null &&
                      paginatedStation.longitude != null;
                  // REMOVED: Don't require UID, address, or connector types - these can have fallbacks

                  if (!isValid) {
                    debugPrint(
                        '❌ Excluding station with critical missing data: ${paginatedStation.name}');
                  } else if (paginatedStation.uid == null ||
                      paginatedStation.uid!.isEmpty) {
                    debugPrint(
                        '⚠️ Station ${paginatedStation.name} has no UID - will use fallback');
                  }
                  return isValid;
                })
                .map((paginatedStation) => Station(
                      id: paginatedStation.stationId?.toString() ?? '',
                      name: paginatedStation
                          .name!, // Required field - validated above
                      address: paginatedStation
                          .address!, // Required field - validated above
                      city: paginatedStation.city, // Real city or null
                      state: null,
                      latitude: paginatedStation
                          .latitude!, // Required field - validated above
                      longitude: paginatedStation
                          .longitude!, // Required field - validated above
                      distance: paginatedStation.distance ?? 0.0,
                      // REMOVED: No default status - use empty string if not provided
                      status: paginatedStation.status ?? '',
                      // REMOVED: No default rating - use 0.0 only when API provides null
                      rating: paginatedStation.rating ?? 0.0,
                      // REMOVED: No default review count - use 0 only when API provides null
                      reviews: paginatedStation.reviewCount ?? 0,
                      connectors: [], // Will be populated from types if needed
                      images: paginatedStation.imageUrl != null
                          ? [paginatedStation.imageUrl!]
                          : [], // Real images or empty list
                      evses: [], // Will be populated from types if needed
                      uid: paginatedStation
                          .uid!, // Required field - validated above
                      types: paginatedStation.types, // Real types data from API
                    ))
                .toList() ??
            [];

        hasMorePages = response.currentPage < response.totalPages;
      }

      state = state.copyWith(
        stations: isInitialLoad
            ? List<Station>.from(newStations)
            : List<Station>.from([...state.stations, ...newStations]),
        currentPage: page,
        hasMore: hasMorePages,
        isLoading: false,
        isLoadingMore: false,
        errorMessage: null,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        isLoadingMore: false,
        errorMessage: 'Failed to load stations: ${e.toString()}',
      );
    }
  }

  void setSearchQuery(String query) {
    if (state.searchQuery == query) return;

    state = state.copyWith(
      searchQuery: query,
      stations: <Station>[],
      currentPage: 0,
      hasMore: true,
    );

    loadStations(isInitialLoad: true);
  }

  void setFilter(String filter) {
    if (state.selectedFilter == filter) return;

    state = state.copyWith(
      selectedFilter: filter,
      stations: <Station>[],
      currentPage: 0,
      hasMore: true,
    );

    loadStations(isInitialLoad: true);
  }

  Future<void> refresh() async {
    state = state.copyWith(
      stations: <Station>[],
      currentPage: 0,
      hasMore: true,
      errorMessage: null,
    );

    await loadStations(isInitialLoad: true);
  }
}

final stationsProvider =
    StateNotifierProvider<StationsNotifier, StationsState>((ref) {
  return StationsNotifier(ref);
});
