/// Station marker model for map display
class StationMarker {
  final String id;
  final String name;
  final double latitude;
  final double longitude;
  final String status; // 'available', 'busy', 'offline'
  final int availableConnectors;
  final int totalConnectors;
  final String? mapPinUrl;
  final String? focusedMapPinUrl;

  StationMarker({
    required this.id,
    required this.name,
    required this.latitude,
    required this.longitude,
    required this.status,
    required this.availableConnectors,
    required this.totalConnectors,
    this.mapPinUrl,
    this.focusedMapPinUrl,
  });

  factory StationMarker.fromJson(Map<String, dynamic> json) {
    return StationMarker(
      id: json['id']?.toString() ?? json['station_id']?.toString() ?? '',
      name: json['name']?.toString() ?? json['station_name']?.toString() ?? '',
      latitude: (json['latitude'] as num?)?.toDouble() ?? 0.0,
      longitude: (json['longitude'] as num?)?.toDouble() ?? 0.0,
      status: json['status']?.toString() ?? '',
      availableConnectors: json['availableConnectors'] ?? 0,
      totalConnectors: json['totalConnectors'] ?? 0,
      mapPinUrl: json['map_pin_url'],
      focusedMapPinUrl: json['focused_map_pin_url'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'latitude': latitude,
      'longitude': longitude,
      'status': status,
      'availableConnectors': availableConnectors,
      'totalConnectors': totalConnectors,
      'map_pin_url': mapPinUrl,
      'focused_map_pin_url': focusedMapPinUrl,
    };
  }
}

/// Station detail model
class StationDetail {
  final String id;
  final String name;
  final String address;
  final double latitude;
  final double longitude;
  final String status;
  final double rating;
  final int reviewCount;
  final String operatingHours;
  final String imageUrl;
  final List<ConnectorDetail> connectors;
  final List<Amenity> amenities;
  final bool isBookmarked;
  final String? uid;

  StationDetail({
    required this.id,
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.status,
    required this.rating,
    required this.reviewCount,
    required this.operatingHours,
    required this.imageUrl,
    required this.connectors,
    required this.amenities,
    required this.isBookmarked,
    this.uid,
  });

  factory StationDetail.fromJson(Map<String, dynamic> json) {
    // CRITICAL: Validate required fields - throw exceptions for missing data
    // This prevents showing default/fallback data when API fails

    if (json['id'] == null || (json['id'] as String).isEmpty) {
      throw FormatException(
          'CRITICAL: Station ID is missing from API response');
    }

    if (json['name'] == null || (json['name'] as String).isEmpty) {
      throw FormatException(
          'CRITICAL: Station name is missing from API response');
    }

    if (json['address'] == null || (json['address'] as String).isEmpty) {
      throw FormatException(
          'CRITICAL: Station address is missing from API response');
    }

    final latitude = (json['latitude'] as num?)?.toDouble();
    final longitude = (json['longitude'] as num?)?.toDouble();

    if (latitude == null ||
        longitude == null ||
        latitude == 0.0 ||
        longitude == 0.0) {
      throw FormatException(
          'CRITICAL: Invalid station coordinates from API response');
    }

    return StationDetail(
      id: json['id'] as String,
      name: json['name'] as String,
      address: json['address'] as String,
      latitude: latitude,
      longitude: longitude,
      // CRITICAL: No default status - throw error if missing
      status: json['status'] as String? ??
          (throw FormatException(
              'Station status is missing from API response')),
      // CRITICAL: No default rating - throw error if missing
      rating: (json['rating'] as num?)?.toDouble() ??
          (throw FormatException(
              'Station rating is missing from API response')),
      // CRITICAL: No default review count - throw error if missing
      reviewCount: json['reviewCount'] ??
          (throw FormatException(
              'Station review count is missing from API response')),
      operatingHours: json['operatingHours'] as String? ?? '',
      imageUrl: json['imageUrl'] as String? ?? '',
      uid: json['uid']?.toString(),
      connectors: (json['connectors'] as List<dynamic>?)
              ?.map((e) => ConnectorDetail.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      amenities: (json['amenities'] as List<dynamic>?)
              ?.map((e) => Amenity.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      isBookmarked: json['isBookmarked'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'status': status,
      'rating': rating,
      'reviewCount': reviewCount,
      'operatingHours': operatingHours,
      'imageUrl': imageUrl,
      'connectors': connectors.map((e) => e.toJson()).toList(),
      'amenities': amenities.map((e) => e.toJson()).toList(),
      'isBookmarked': isBookmarked,
    };
  }
}

/// Connector detail model
class ConnectorDetail {
  final String id;
  final String type;
  final double? powerOutput; // Nullable numeric type from API
  final double pricePerKwh;
  final String status;
  final String imageUrl;

  ConnectorDetail({
    required this.id,
    required this.type,
    required this.powerOutput,
    required this.pricePerKwh,
    required this.status,
    required this.imageUrl,
  });

  factory ConnectorDetail.fromJson(Map<String, dynamic> json) {
    // CRITICAL: Validate essential connector data
    if (json['id'] == null || json['id'].toString().isEmpty) {
      throw FormatException('Connector ID is missing from API response');
    }
    if (json['type'] == null || json['type'].toString().isEmpty) {
      throw FormatException('Connector type is missing from API response');
    }

    return ConnectorDetail(
      id: json['id'].toString(),
      type: json['type'].toString(),
      powerOutput: json['powerOutput'],
      pricePerKwh: (json['pricePerKwh'] as num?)?.toDouble() ?? 0.0,
      // CRITICAL: No default status - throw error if missing
      status: json['status']?.toString() ??
          (throw FormatException(
              'Connector status is missing from API response')),
      imageUrl: json['imageUrl']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'powerOutput': powerOutput,
      'pricePerKwh': pricePerKwh,
      'status': status,
      'imageUrl': imageUrl,
    };
  }
}

/// Amenity model
class Amenity {
  final String name;
  final String iconUrl;

  Amenity({
    required this.name,
    required this.iconUrl,
  });

  factory Amenity.fromJson(Map<String, dynamic> json) {
    return Amenity(
      name: json['name'] ?? '',
      iconUrl: json['iconUrl'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'iconUrl': iconUrl,
    };
  }
}

/// Review model
class Review {
  final String id;
  final String userId;
  final String userName;
  final String userImageUrl;
  final double rating;
  final String comment;
  final DateTime createdAt;

  Review({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userImageUrl,
    required this.rating,
    required this.comment,
    required this.createdAt,
  });

  factory Review.fromJson(Map<String, dynamic> json) {
    return Review(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      userName: json['userName'] ?? '',
      userImageUrl: json['userImageUrl'] ?? '',
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      comment: json['comment'] ?? '',
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'userImageUrl': userImageUrl,
      'rating': rating,
      'comment': comment,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}

/// Station marker response model
class StationMarkerResponse {
  final List<StationMarker> data;
  final String message;
  final bool success;

  StationMarkerResponse({
    required this.data,
    required this.message,
    required this.success,
  });

  factory StationMarkerResponse.fromJson(Map<String, dynamic> json) {
    return StationMarkerResponse(
      data: (json['data'] as List<dynamic>?)
              ?.map((e) => StationMarker.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      message: json['message'] ?? '',
      success: json['success'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data.map((e) => e.toJson()).toList(),
      'message': message,
      'success': success,
    };
  }
}

/// Station detail response model
class StationDetailResponse {
  final StationDetail data;
  final String message;
  final bool success;

  StationDetailResponse({
    required this.data,
    required this.message,
    required this.success,
  });

  factory StationDetailResponse.fromJson(Map<String, dynamic> json) {
    return StationDetailResponse(
      data: StationDetail.fromJson(json['data'] ?? {}),
      message: json['message'] ?? '',
      success: json['success'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data.toJson(),
      'message': message,
      'success': success,
    };
  }
}

/// Nearest station model
class NearestStation {
  final String id;
  final String name;
  final String address;
  final double latitude;
  final double longitude;
  final double distance;
  final String status;
  final List<ConnectorType> connectorTypes;

  NearestStation({
    required this.id,
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.distance,
    required this.status,
    required this.connectorTypes,
  });

  factory NearestStation.fromJson(Map<String, dynamic> json) {
    // Handle both Map and List formats for connector types
    List<ConnectorType> connectorTypes = [];
    final dynamic types = json['types'];

    if (types != null) {
      if (types is List) {
        // Handle list format
        connectorTypes = types
            .whereType<Map<String, dynamic>>()
            .map((e) => ConnectorType.fromJson(e))
            .toList();
      } else if (types is Map) {
        // Handle map format (e.g., {"0": {"name": "CCS2", "icon": "url"}, ...})
        connectorTypes = types.values
            .whereType<Map<String, dynamic>>()
            .map((e) => ConnectorType.fromJson(e))
            .toList();
      }
    }

    return NearestStation(
      id: json['id'] ?? json['station_id']?.toString() ?? '',
      name: json['name'] ?? '',
      address: json['address'] ?? '',
      latitude: (json['latitude'] as num?)?.toDouble() ?? 0.0,
      longitude: (json['longitude'] as num?)?.toDouble() ?? 0.0,
      distance: (json['distance'] as num?)?.toDouble() ?? 0.0,
      status: json['status'] ?? 'offline',
      connectorTypes: connectorTypes,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'distance': distance,
      'status': status,
      'types': connectorTypes.map((e) => e.toJson()).toList(),
    };
  }
}

/// Connector type model
class ConnectorType {
  final String name;
  final String iconUrl;

  ConnectorType({
    required this.name,
    required this.iconUrl,
  });

  factory ConnectorType.fromJson(Map<String, dynamic> json) {
    return ConnectorType(
      name: json['name'] ?? '',
      iconUrl: json['icon'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'icon': iconUrl,
    };
  }
}
