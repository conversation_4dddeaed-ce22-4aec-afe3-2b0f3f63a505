import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../services/auth_manager.dart';
import '../../../services/sync_service.dart';
import '../../../services/service_locator.dart';

/// Profile state class
class ProfileState {
  final bool isLoading;
  final String userId;
  final String userName;
  final String userEmail;
  final String userPhone;
  final double walletBalance;
  final String? errorMessage;
  final bool isInstantChargingOn;
  final bool isAutoChargeOn;

  ProfileState({
    this.isLoading = false,
    this.userId = '',
    this.userName = 'Loading...',
    this.userEmail = 'Loading...',
    this.userPhone = 'Loading...',
    this.walletBalance = 0.0,
    this.errorMessage,
    this.isInstantChargingOn = false,
    this.isAutoChargeOn = false,
  });

  ProfileState copyWith({
    bool? isLoading,
    String? userId,
    String? userName,
    String? userEmail,
    String? userPhone,
    double? walletBalance,
    String? errorMessage,
    bool? isInstantChargingOn,
    bool? isAutoChargeOn,
  }) {
    return ProfileState(
      isLoading: isLoading ?? this.isLoading,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userEmail: userEmail ?? this.userEmail,
      userPhone: userPhone ?? this.userPhone,
      walletBalance: walletBalance ?? this.walletBalance,
      errorMessage: errorMessage,
      isInstantChargingOn: isInstantChargingOn ?? this.isInstantChargingOn,
      isAutoChargeOn: isAutoChargeOn ?? this.isAutoChargeOn,
    );
  }
}

/// Profile provider
final profileProvider =
    StateNotifierProvider<ProfileNotifier, ProfileState>((ref) {
  return ProfileNotifier();
});

/// Profile notifier
class ProfileNotifier extends StateNotifier<ProfileState> {
  // Auth manager for getting user data
  final AuthManager _authManager = AuthManager();

  // Cache for profile data - CRITICAL: Made non-static to prevent cross-session persistence
  Map<String, dynamic>? _cachedProfileData;
  double? _cachedWalletBalance;

  // Track if profile has been loaded already - CRITICAL: Made non-static
  bool _hasLoadedProfileBefore = false;

  /// CRITICAL: Clear all cached data on logout
  static void clearAllCaches() {
    debugPrint('🧹 CLEARING ALL PROFILE CACHES (StateNotifier)');
  }

  /// CRITICAL: Clear instance caches and force refresh
  void clearInstanceCaches() {
    debugPrint('🧹 CLEARING INSTANCE PROFILE CACHES (StateNotifier)');
    _cachedProfileData = null;
    _cachedWalletBalance = null;
    _hasLoadedProfileBefore = false;
  }

  /// CRITICAL: Force refresh profile data (called after login)
  void forceRefreshProfile() {
    debugPrint('🔄 FORCE REFRESHING PROFILE DATA (StateNotifier)');
    clearInstanceCaches();
    loadUserData();
  }

  ProfileNotifier() : super(ProfileState()) {
    // Use cached data if available, otherwise load from network
    if (_hasLoadedProfileBefore && _cachedProfileData != null) {
      _applyProfileDataFromCache();
    } else {
      loadUserData();
    }
  }

  // Apply cached profile data to avoid unnecessary API calls
  void _applyProfileDataFromCache() {
    if (_cachedProfileData != null) {
      state = state.copyWith(
        isLoading: false,
        userId: _cachedProfileData!['id']?.toString() ?? '',
        userName: _cachedProfileData!['name']?.toString() ?? 'User',
        userEmail: _cachedProfileData!['email']?.toString() ?? '',
        userPhone: _cachedProfileData!['mobile_number']?.toString() ?? '',
        walletBalance: _cachedWalletBalance,
      );

      debugPrint('\n=== USING CACHED PROFILE DATA ===');
      debugPrint('Name: ${state.userName}');
      debugPrint('Email: ${state.userEmail}');
      debugPrint('Phone: ${state.userPhone}');
      debugPrint('ID: ${state.userId}');
      debugPrint('Wallet Balance: ${state.walletBalance}');
    }
  }

  // Load user data from AuthManager and SyncService
  Future<void> loadUserData() async {
    try {
      state = state.copyWith(isLoading: true);

      // First try to get user data from AuthManager
      final userData = await _authManager.getUserData();

      if (userData != null) {
        // Get user ID from userData
        final id = userData['id']?.toString() ?? '';
        final name = userData['name'] as String? ?? '';
        final email = userData['email'] as String? ?? '';
        final phone = userData['mobile_number'] as String? ?? '';

        // Update state with user data from AuthManager
        state = state.copyWith(
          userId: id,
          userName: name.isNotEmpty ? name : state.userName,
          userEmail: email.isNotEmpty ? email : state.userEmail,
          userPhone: phone.isNotEmpty ? phone : state.userPhone,
        );

        // If we have a user ID, try to get more detailed profile from SyncService
        if (id.isNotEmpty) {
          final syncService = SyncService();
          final profileData = await syncService.getUserProfile(id);

          if (profileData != null) {
            // Cache the profile data for future use
            _cachedProfileData = Map<String, dynamic>.from(profileData);

            // Update state with more detailed profile data if available
            state = state.copyWith(
              userName: profileData['name'] != null &&
                      profileData['name'].toString().isNotEmpty
                  ? profileData['name'].toString()
                  : state.userName,
              userEmail: profileData['email'] != null &&
                      profileData['email'].toString().isNotEmpty
                  ? profileData['email'].toString()
                  : state.userEmail,
              userPhone: profileData['mobile_number'] != null &&
                      profileData['mobile_number'].toString().isNotEmpty
                  ? profileData['mobile_number'].toString()
                  : state.userPhone,
            );
          }
        }
      } else {
        // If no user data from AuthManager, try SharedPreferences as fallback
        final prefs = await SharedPreferences.getInstance();
        final name = prefs.getString('user_name');
        final email = prefs.getString('user_email');
        final phone = prefs.getString('user_phone');
        final id = prefs.getString('user_id');

        // Update state if data exists
        state = state.copyWith(
          userName: name != null && name.isNotEmpty ? name : state.userName,
          userEmail:
              email != null && email.isNotEmpty ? email : state.userEmail,
          userPhone:
              phone != null && phone.isNotEmpty ? phone : state.userPhone,
          userId: id ?? state.userId,
        );

        // Create a cached profile data object from SharedPreferences
        if (id != null) {
          _cachedProfileData = {
            'id': id,
            'name': name ?? '',
            'email': email ?? '',
            'mobile_number': phone ?? '',
          };
        }
      }

      // Fetch wallet balance
      await _fetchWalletBalance();

      // Mark that we've loaded the profile
      _hasLoadedProfileBefore = true;

      debugPrint('\n=== LOADED USER DATA ===');
      debugPrint('Name: ${state.userName}');
      debugPrint('Email: ${state.userEmail}');
      debugPrint('Phone: ${state.userPhone}');
      debugPrint('ID: ${state.userId}');
      debugPrint('Wallet Balance: ${state.walletBalance}');
    } catch (e) {
      debugPrint('Error loading user data: $e');
      state = state.copyWith(
        errorMessage: 'Error loading profile: $e',
      );
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }

  // Fetch wallet balance
  Future<void> _fetchWalletBalance() async {
    try {
      final walletResponse =
          await ServiceLocator().walletRepositoryImpl.getWalletInfo();
      if (walletResponse.success &&
          walletResponse.data != null &&
          walletResponse.data!.wallet != null) {
        final balance = walletResponse.data!.wallet!.balance ?? 0.0;

        // Cache the wallet balance
        _cachedWalletBalance = balance;

        state = state.copyWith(walletBalance: balance);
      }
    } catch (e) {
      debugPrint('Error fetching wallet balance: $e');
    }
  }

  // Update profile data
  Future<void> updateProfile(Map<String, dynamic> updatedData) async {
    try {
      // Update the cached data
      if (_cachedProfileData != null) {
        _cachedProfileData!.addAll(updatedData);
      } else {
        _cachedProfileData = Map<String, dynamic>.from(updatedData);
      }

      // Update the state
      state = state.copyWith(
        userName: updatedData.containsKey('name')
            ? updatedData['name'] as String? ?? state.userName
            : state.userName,
        userEmail: updatedData.containsKey('email')
            ? updatedData['email'] as String? ?? state.userEmail
            : state.userEmail,
        userPhone: updatedData.containsKey('mobile_number')
            ? updatedData['mobile_number'] as String? ?? state.userPhone
            : state.userPhone,
      );

      debugPrint('\n=== UPDATED PROFILE DATA ===');
      debugPrint('Name: ${state.userName}');
      debugPrint('Email: ${state.userEmail}');
      debugPrint('Phone: ${state.userPhone}');
    } catch (e) {
      debugPrint('Error updating profile: $e');
      state = state.copyWith(
        errorMessage: 'Error updating profile: $e',
      );
    }
  }

  // Toggle instant charging
  void toggleInstantCharging(bool value) {
    state = state.copyWith(isInstantChargingOn: value);
    // TODO: Persist the state (e.g., using SharedPreferences or API)
  }

  // Toggle auto charge
  void toggleAutoCharge(bool value) {
    state = state.copyWith(isAutoChargeOn: value);
    // TODO: Persist the state (e.g., using SharedPreferences or API)
  }
}
