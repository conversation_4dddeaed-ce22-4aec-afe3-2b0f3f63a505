import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../features/auth/application/auth_notifier.dart';
import '../domain/profile_state.dart';
import '../../../providers/core_providers.dart' as core;

/// Provider for profile state management
final StateNotifierProvider<ProfileNotifier, AsyncValue<ProfileState>>
    profileProvider =
    StateNotifierProvider<ProfileNotifier, AsyncValue<ProfileState>>((ref) {
  final authService = ref.watch(core.authServiceProvider);

  // Listen to auth state changes to update profile
  ref.listen(authProvider, (previous, next) {
    next.whenData((state) {
      if (state.isAuthenticated) {
        // Refresh profile when user authenticates
        ref.read(profileProvider.notifier).fetchProfile();
      }
    });
  });

  return ProfileNotifier(authService);
});

/// Profile state notifier
class ProfileNotifier extends StateNotifier<AsyncValue<ProfileState>> {
  final dynamic _authRepository; // Use dynamic to accept either repository type

  ProfileNotifier(this._authRepository) : super(const AsyncValue.loading()) {
    // Initialize the profile state
    fetchProfile();
  }

  /// Fetch user profile
  Future<void> fetchProfile() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      try {
        final user = await _authRepository.getCurrentUser();
        if (user != null) {
          return ProfileState.loaded(user);
        }
        return ProfileState.error('User not found');
      } catch (e) {
        debugPrint('Error fetching profile: $e');
        return ProfileState.error(e.toString());
      }
    });
  }

  /// Update user profile
  Future<void> updateProfile({String? name, String? email}) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      try {
        final response = await _authRepository.updateUserProfile(
          name: name,
          email: email,
        );

        if (response.success && response.data != null) {
          return ProfileState.loaded(response.data!);
        }

        return ProfileState.error(response.message);
      } catch (e) {
        debugPrint('Error updating profile: $e');
        return ProfileState.error(e.toString());
      }
    });
  }
}
