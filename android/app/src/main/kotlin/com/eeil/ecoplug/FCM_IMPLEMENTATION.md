# EcoPlug Firebase Messaging Service Implementation

## Overview
This implementation provides a custom Firebase Messaging Service for the EcoPlug app that handles FCM notifications with proper branding and notification channel management.

## Files Created/Modified

### 1. EcoPlugFirebaseMessagingService.kt
**Purpose**: Custom FCM service extending `FirebaseMessagingService`
**Features**:
- Handles incoming FCM messages with `onMessageReceived()`
- Uses proper EcoPlug branding (app launcher icon + logo)
- Respects `charging_session_custom` channel or creates default `ecoplug_fcm` channel
- Supports both charging-related and general notifications
- Handles FCM token generation with `onNewToken()`
- Includes data payload processing for custom actions

### 2. NotificationHelper.kt
**Purpose**: Common notification building logic to avoid code duplication
**Features**:
- Creates and manages notification channels (`charging_session_custom` and `ecoplug_fcm`)
- Builds notifications with consistent EcoPlug branding
- Uses `R.mipmap.ic_launcher` for small icon (better recognition)
- Uses `R.mipmap.ic_ecoplug_logo` for large icon
- Handles channel existence checks and fallbacks

### 3. Updated Files
- **AndroidManifest.xml**: Registered `EcoPlugFirebaseMessagingService` 
- **CustomChargingNotificationHandler.kt**: Updated to use launcher icon
- **ChargingBackgroundService.kt**: Updated to use launcher icon

## Icon Usage

### Before (Issue)
- Used custom vector drawable `R.drawable.ic_ecoplug_notification`
- Resulted in generic green circle icon in notifications
- Poor brand recognition

### After (Fixed)
- **Small Icon**: `R.mipmap.ic_launcher` - Official app launcher icon
- **Large Icon**: `R.mipmap.ic_ecoplug_logo` - EcoPlug logo
- Consistent branding across all notifications

## Notification Channels

### 1. charging_session_custom
- **Purpose**: Charging session notifications
- **Importance**: High
- **Features**: Lights enabled, no vibration, silent updates
- **Usage**: Used when `useChargingChannel = true` or for charging-related FCM messages

### 2. ecoplug_fcm
- **Purpose**: General FCM notifications  
- **Importance**: High
- **Features**: Lights and vibration enabled
- **Usage**: Default fallback for FCM messages

## FCM Message Handling

### Message Types
1. **Charging Notifications**: Detected by:
   - `data["type"] == "charging"`
   - `data["category"] == "charging"`
   - Title/message contains "charging"

2. **General Notifications**: All other FCM messages

### Data Payload Support
- Custom channel selection via `data["channel_id"]`
- Deep linking actions via `data["action"]`
- Custom notification ID via `data["notification_id"]`

### Example FCM Message
```json
{
  "notification": {
    "title": "Charging Update",
    "body": "Your vehicle is 80% charged"
  },
  "data": {
    "type": "charging",
    "channel_id": "charging_session_custom",
    "action": "open_charging_session",
    "notification_id": "123"
  }
}
```

## Integration

### Service Registration
Service is registered in `AndroidManifest.xml`:
```xml
<service
    android:name=".EcoPlugFirebaseMessagingService"
    android:exported="false">
    <intent-filter>
        <action android:name="com.google.firebase.MESSAGING_EVENT" />
    </intent-filter>
</service>
```

### Usage Examples

#### From Flutter/Dart
```dart
// No direct integration needed - service handles FCM automatically
// Token updates and message handling are automatic
```

#### Server-side FCM
```json
// Send to topic
{
  "to": "/topics/charging_12345",
  "notification": {
    "title": "Charging Complete",
    "body": "Your vehicle is fully charged!"
  },
  "data": {
    "type": "charging",
    "action": "open_charging_session"
  }
}

// Send to specific device
{
  "to": "FCM_TOKEN_HERE",
  "notification": {
    "title": "EcoPlug Alert",
    "body": "Station maintenance scheduled"
  },
  "data": {
    "channel_id": "ecoplug_fcm"
  }
}
```

## Benefits

1. **Consistent Branding**: All notifications use official EcoPlug icons
2. **Better Recognition**: Launcher icon improves notification visibility
3. **Code Reuse**: Common notification logic shared across services
4. **Channel Management**: Proper channel creation and fallback handling
5. **Flexibility**: Supports both charging and general notifications
6. **Future-Proof**: Easy to extend with new notification types

## Testing

### Test FCM Notifications
1. Use Firebase Console to send test messages
2. Include data payload to test channel selection
3. Test both charging and general notification types
4. Verify proper icon display in notification panel

### Verify Channels
1. Go to Android Settings > Apps > EcoPlug > Notifications
2. Verify both channels are created properly
3. Test channel-specific settings (vibration, lights, etc.)

## Notes

- Service automatically handles FCM token refresh
- Notification IDs are generated to prevent conflicts
- Proper error handling and logging throughout
- Compatible with Android 8.0+ notification channels
- Falls back gracefully on older Android versions
