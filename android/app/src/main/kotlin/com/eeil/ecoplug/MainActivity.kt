package com.eeil.ecoplug

import android.content.Intent
import android.os.Build
import android.util.Log
import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterFragmentActivity() {

    private val CUSTOM_NOTIFICATION_CHANNEL = "custom_charging_notification"
    private val NOTIFICATION_PERMISSION_CHANNEL = "notification_permission_service"
    private val DEEP_LINK_CHANNEL = "deep_link"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // Custom charging notification handler removed - using standard notifications instead

        // Register the notification permission service handler
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, NOTIFICATION_PERMISSION_CHANNEL)
            .setMethodCallHandler { call, result ->
                when (call.method) {
                    "getApiLevel" -> {
                        result.success(Build.VERSION.SDK_INT)
                    }
                    else -> {
                        result.notImplemented()
                    }
                }
            }

        // Handle deep linking for charging session restoration
        handleChargingSessionDeepLink(flutterEngine)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
        if (flutterEngine != null) {
            handleChargingSessionDeepLink(flutterEngine!!)
        }
    }

    private fun handleChargingSessionDeepLink(flutterEngine: FlutterEngine) {
        val intent = getIntent()
        val openChargingSession = intent.getBooleanExtra("open_charging_session", false)

        if (openChargingSession) {
            Log.d("MainActivity", "🔋 Deep link detected: opening charging session screen")

            // Send deep link event to Flutter
            val channel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, DEEP_LINK_CHANNEL)
            channel.invokeMethod("openChargingSession", mapOf(
                "source" to "notification_tap",
                "timestamp" to System.currentTimeMillis()
            ))
        }
    }
}
