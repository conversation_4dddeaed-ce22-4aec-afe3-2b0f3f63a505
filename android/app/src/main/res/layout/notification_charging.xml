<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/charging_notification_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@drawable/notification_background">

    <!-- Top Section: Status and indicator -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <!-- Pulse indicator (static) -->
        <View
            android:id="@+id/charging_indicator"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:layout_marginEnd="8dp"
            android:backgroundTint="@color/green"
            android:background="@drawable/circle_shape" />

        <!-- Charge percentage -->
        <TextView
            android:id="@+id/charging_percentage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0%"
            android:textSize="18sp"
            android:textColor="@color/black"
            android:textStyle="bold" />

        <!-- Status text -->
        <TextView
            android:id="@+id/charging_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:text="ACTIVE CHARGING"
            android:textSize="14sp"
            android:textColor="@color/dark_gray" />
    </LinearLayout>

    <!-- Progress bar -->
    <ProgressBar
        style="?android:attr/progressBarStyleHorizontal"
        android:id="@+id/charging_progressBar"
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:layout_marginTop="12dp"
        android:progressTint="@color/green"
        android:progress="0"
        android:max="100"
        android:indeterminate="false" />

    <!-- Metrics Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="12dp"
        android:weightSum="3">

        <!-- Current Power -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">
            <TextView
                android:id="@+id/label_power"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Power"
                android:textSize="12sp"
                android:textColor="@color/dark_gray" />
            <TextView
                android:id="@+id/value_power"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0 kW"
                android:textSize="14sp"
                android:textColor="@color/black"
                android:textStyle="bold" />
        </LinearLayout>

        <!-- Energy Delivered -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginStart="8dp">
            <TextView
                android:id="@+id/label_energy"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Energy"
                android:textSize="12sp"
                android:textColor="@color/dark_gray" />
            <TextView
                android:id="@+id/value_energy"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0 kWh"
                android:textSize="14sp"
                android:textColor="@color/black"
                android:textStyle="bold" />
        </LinearLayout>

        <!-- Current Price -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginStart="8dp">
            <TextView
                android:id="@+id/label_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Price"
                android:textSize="12sp"
                android:textColor="@color/dark_gray" />
            <TextView
                android:id="@+id/value_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="₹0.00"
                android:textSize="14sp"
                android:textColor="@color/black"
                android:textStyle="bold" />
        </LinearLayout>
    </LinearLayout>

    <!-- Environmental Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="12dp"
        android:weightSum="2">

        <!-- Carbon Savings -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">
            <TextView
                android:id="@+id/label_carbon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Carbon"
                android:textSize="12sp"
                android:textColor="@color/dark_gray" />
            <TextView
                android:id="@+id/value_carbon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0.0 kg"
                android:textSize="14sp"
                android:textColor="@color/black"
                android:textStyle="bold" />
        </LinearLayout>

        <!-- Timer -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginStart="8dp">
            <TextView
                android:id="@+id/label_timer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Time"
                android:textSize="12sp"
                android:textColor="@color/dark_gray" />
            <TextView
                android:id="@+id/value_timer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="00:00:00"
                android:textSize="14sp"
                android:textColor="@color/black"
                android:textStyle="bold" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
