PODS:
  - CashfreeAnalyticsSDK (2.2.0):
    - CFNetworkSDK (= 2.2.0)
  - CashfreePG (2.2.4):
    - CashfreePGUISDK (= 2.2.4)
  - CashfreePGCoreSDK (2.2.4):
    - CashfreeAnalyticsSDK (= 2.2.0)
  - CashfreePGUISDK (2.2.4):
    - CashfreePGCoreSDK (= 2.2.4)
  - CFNetworkSDK (2.2.0)
  - connectivity_plus (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - Firebase/CoreOnly (11.15.0):
    - FirebaseCore (~> 11.15.0)
  - Firebase/Messaging (11.15.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.15.0)
  - firebase_core (3.15.2):
    - Firebase/CoreOnly (= 11.15.0)
    - Flutter
  - firebase_messaging (15.2.10):
    - Firebase/Messaging (= 11.15.0)
    - firebase_core
    - Flutter
  - FirebaseCore (11.15.0):
    - FirebaseCoreInternal (~> 11.15.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreInternal (11.15.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseInstallations (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Reachability (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
  - Flutter (1.0.0)
  - flutter_cashfree_pg_sdk (0.0.1):
    - CashfreePG (= 2.2.4)
    - Flutter
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - Google-Maps-iOS-Utils (5.0.0):
    - GoogleMaps (~> 8.0)
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - Google-Maps-iOS-Utils (< 7.0, >= 5.0)
    - GoogleMaps (< 10.0, >= 8.4)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMaps (8.4.0):
    - GoogleMaps/Maps (= 8.4.0)
  - GoogleMaps/Base (8.4.0)
  - GoogleMaps/Maps (8.4.0):
    - GoogleMaps/Base
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - image_picker_ios (0.0.1):
    - Flutter
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - open_file_ios (0.0.1):
    - Flutter
  - OrderedSet (6.0.3)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - payu_checkoutpro_flutter (1.2.0):
    - Flutter
    - PayUIndia-CheckoutPro (~> 8.10.0)
  - PayUIndia-Analytics (4.0.2):
    - PayUIndia-NetworkReachability (~> 2.1)
  - PayUIndia-AssetLibrary (4.0.1)
  - PayUIndia-CardScanner (2.1.0)
  - PayUIndia-CheckoutPro (8.10.0):
    - PayUIndia-AssetLibrary (~> 4.0)
    - PayUIndia-CardScanner (~> 2.1)
    - PayUIndia-CheckoutProBase (~> 8.10)
    - PayUIndia-CommonUI (~> 2.3)
    - PayUIndia-CrashReporter (~> 4.0)
    - PayUIndia-Custom-Browser (~> 11.3)
    - PayUIndia-NativeOtpAssist (~> 4.2)
    - PayUIndia-NetworkReachability (~> 2.0)
    - PayUIndia-PayUParams (~> 6.8)
    - PayUIndia-PG-SDK (~> 11.9)
    - PayUIndia-UPICore (~> 10.6)
  - PayUIndia-CheckoutProBase (8.11.0):
    - PayUIndia-PayUParams (~> 6.8)
    - PayUIndia-PG-SDK (~> 11.9)
  - PayUIndia-CommonUI (2.3.0):
    - PayUIndia-Analytics (~> 4.0)
    - PayUIndia-CrashReporter (~> 4.0)
  - PayUIndia-CrashReporter (4.0.2)
  - PayUIndia-Custom-Browser (11.3.0):
    - PayUIndia-CommonUI (~> 2.3)
  - PayUIndia-Logger (5.0.1)
  - PayUIndia-NativeOtpAssist (4.2.0):
    - PayUIndia-Analytics (~> 4.0)
    - PayUIndia-CommonUI (~> 2.2)
    - PayUIndia-CrashReporter (~> 4.0)
    - PayUIndia-NetworkReachability (~> 2.1)
    - PayUIndia-PayUParams (~> 6.6)
  - PayUIndia-Networking (5.0.1):
    - PayUIndia-Logger (~> 5.0)
  - PayUIndia-NetworkReachability (2.1.1)
  - PayUIndia-PayUParams (6.8.0)
  - PayUIndia-PG-SDK (11.9.0):
    - PayUIndia-CrashReporter (~> 4.0)
    - PayUIndia-NetworkReachability (~> 2.1)
    - PayUIndia-PayUParams (~> 6.8)
  - PayUIndia-UPICore (10.6.0):
    - PayUIndia-CommonUI (~> 2.2)
    - PayUIndia-Networking (~> 5.0)
    - PayUIndia-PayUParams (~> 6.8)
  - permission_handler_apple (9.3.0):
    - Flutter
  - phonepe_payment_sdk (3.0.0):
    - Flutter
    - PhonePePayment (= 4.0.0)
  - PhonePePayment (4.0.0)
  - PromisesObjC (2.4.0)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sms_autofill (0.0.1):
    - Flutter
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_cashfree_pg_sdk (from `.symlinks/plugins/flutter_cashfree_pg_sdk/ios`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/darwin`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - open_file_ios (from `.symlinks/plugins/open_file_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - payu_checkoutpro_flutter (from `.symlinks/plugins/payu_checkoutpro_flutter/ios`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - phonepe_payment_sdk (from `.symlinks/plugins/phonepe_payment_sdk/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sms_autofill (from `.symlinks/plugins/sms_autofill/ios`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - CashfreeAnalyticsSDK
    - CashfreePG
    - CashfreePGCoreSDK
    - CashfreePGUISDK
    - CFNetworkSDK
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - Google-Maps-iOS-Utils
    - GoogleDataTransport
    - GoogleMaps
    - GoogleUtilities
    - nanopb
    - OrderedSet
    - PayUIndia-Analytics
    - PayUIndia-AssetLibrary
    - PayUIndia-CardScanner
    - PayUIndia-CheckoutPro
    - PayUIndia-CheckoutProBase
    - PayUIndia-CommonUI
    - PayUIndia-CrashReporter
    - PayUIndia-Custom-Browser
    - PayUIndia-Logger
    - PayUIndia-NativeOtpAssist
    - PayUIndia-Networking
    - PayUIndia-NetworkReachability
    - PayUIndia-PayUParams
    - PayUIndia-PG-SDK
    - PayUIndia-UPICore
    - PhonePePayment
    - PromisesObjC

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_cashfree_pg_sdk:
    :path: ".symlinks/plugins/flutter_cashfree_pg_sdk/ios"
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/darwin"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  open_file_ios:
    :path: ".symlinks/plugins/open_file_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  payu_checkoutpro_flutter:
    :path: ".symlinks/plugins/payu_checkoutpro_flutter/ios"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  phonepe_payment_sdk:
    :path: ".symlinks/plugins/phonepe_payment_sdk/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sms_autofill:
    :path: ".symlinks/plugins/sms_autofill/ios"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  CashfreeAnalyticsSDK: ae25b71a54607c920d519103d154d9727ca2c15a
  CashfreePG: 6554c215b8eabc9fa13e59c847262b8ba6f29ff6
  CashfreePGCoreSDK: 133e1ba5aeb8a89bf2188eb3312c88fcad084c75
  CashfreePGUISDK: 5e943fcca20c45b66c9837bbb87ebbfb75c013a5
  CFNetworkSDK: 4e431b55f607544f5ca45bc1a36a13e602f51841
  connectivity_plus: 2a701ffec2c0ae28a48cf7540e279787e77c447d
  device_info_plus: c6fb39579d0f423935b0c9ce7ee2f44b71b9fce6
  Firebase: d99ac19b909cd2c548339c2241ecd0d1599ab02e
  firebase_core: 99a37263b3c27536063a7b601d9e2a49400a433c
  firebase_messaging: bf6697c61f31c7cc0f654131212ff04c0115c2c7
  FirebaseCore: efb3893e5b94f32b86e331e3bd6dadf18b66568e
  FirebaseCoreInternal: 9afa45b1159304c963da48addb78275ef701c6b4
  FirebaseInstallations: 317270fec08a5d418fdbc8429282238cab3ac843
  FirebaseMessaging: 3b26e2cee503815e01c3701236b020aa9b576f09
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_cashfree_pg_sdk: acd52fb06332c1d2e58fc1df08435ce34b46fb25
  flutter_inappwebview_ios: 6f63631e2c62a7c350263b13fa5427aedefe81d4
  flutter_local_notifications: 4cde75091f6327eb8517fa068a0a5950212d2086
  flutter_secure_storage: d33dac7ae2ea08509be337e775f6b59f1ff45f12
  geolocator_apple: 66b711889fd333205763b83c9dcf0a57a28c7afd
  Google-Maps-iOS-Utils: 66d6de12be1ce6d3742a54661e7a79cb317a9321
  google_maps_flutter_ios: e31555a04d1986ab130f2b9f24b6cdc861acc6d3
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMaps: 8939898920281c649150e0af74aa291c60f2e77d
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  open_file_ios: 461db5853723763573e140de3193656f91990d9e
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  payu_checkoutpro_flutter: 60222a712e4db6297066459375109c856ff0ed86
  PayUIndia-Analytics: 90a4c1a9d3f7a79422a4f4b0c3d9f5a8362b89fb
  PayUIndia-AssetLibrary: e40d7697c37aea55f184e29564e2f8b63a63b472
  PayUIndia-CardScanner: d6d024645e7fab58326e0d00b22ed42ca0ad5dc1
  PayUIndia-CheckoutPro: 10dd3897212e0b7fcb03520027438be6974da19b
  PayUIndia-CheckoutProBase: 0c767eeb027d9f205678fd676c01b0ff0adb71a5
  PayUIndia-CommonUI: 7b251eda3932e69dd44fccd10e0c865c984a32c0
  PayUIndia-CrashReporter: 913f79270aef2281e1b01af6b41d8465d8fb1b18
  PayUIndia-Custom-Browser: cea86601cfea2f1878e2896d37c8ab31215adb45
  PayUIndia-Logger: 50866e18a275b0726ed59afaea67b6529ab42774
  PayUIndia-NativeOtpAssist: 4afca30e0c2ecfc132c8e42077c5c4ac22cd5600
  PayUIndia-Networking: 0709588fd96b6b42c1398d07471cf87ef2c0db71
  PayUIndia-NetworkReachability: 581c04b9ea3a8aaa78faef179ad819759942f381
  PayUIndia-PayUParams: c4acd8c19dbd939cfd0409505525edec59caeced
  PayUIndia-PG-SDK: 97e0a565413029be96d8912421de546246d5a17d
  PayUIndia-UPICore: 2b9e0db5403f7cb96bb7467a1fb551e54efe1c3e
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  phonepe_payment_sdk: e4ec01c211cb5db9b98391597d58b30b6e5bb8c6
  PhonePePayment: c720ada91c0976d6888a44571ced348ed8cd7d83
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  share_plus: 8b6f8b3447e494cca5317c8c3073de39b3600d1f
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sms_autofill: c461043483362c3f1709ee76eaae6eb570b31686
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  webview_flutter_wkwebview: a4af96a051138e28e29f60101d094683b9f82188

PODFILE CHECKSUM: 5d41ae735e99a788e810ab8d26a72b8ebb899db2

COCOAPODS: 1.16.2
