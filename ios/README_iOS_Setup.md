# iOS Setup Guide for Ecoplug

## ✅ Completed iOS Configuration

### 1. Bundle Identifier & App Information
- **Bundle ID**: `com.eeil.ecoplug`
- **App Name**: `Ecoplug`
- **Display Name**: `Ecoplug`
- **Version**: Synced with Flutter build version

### 2. iOS Deployment Target
- **Minimum iOS Version**: 12.0
- **Target Devices**: iPhone and iPad
- **Architecture**: arm64 (excludes i386 for simulator)

### 3. App Icons
- ✅ Generated using `flutter_launcher_icons`
- ✅ All required iOS icon sizes created
- ✅ App Store compliant icons

### 4. Privacy Permissions (Info.plist)
- ✅ Location (When in Use & Always)
- ✅ Camera access for QR scanning
- ✅ Photo Library access
- ✅ Local Network access
- ✅ Contacts access
- ✅ Microphone access
- ✅ Face ID/Touch ID
- ✅ Background modes for location and notifications

### 5. Firebase Configuration
- ✅ GoogleService-Info.plist configured
- ⚠️ **ACTION REQUIRED**: Replace placeholder values with actual Firebase iOS credentials

### 6. Payment Gateway Support
- ✅ URL Schemes configured for payment callbacks
- ✅ LSApplicationQueriesSchemes for UPI apps
- ✅ Deep linking support

### 7. Build Optimization
- ✅ Release build optimizations
- ✅ Debug build configurations
- ✅ Swift 5.0 compatibility
- ✅ Bitcode disabled (required for Flutter)

### 8. CocoaPods Configuration
- ✅ Podfile created with proper iOS 12.0 target
- ✅ Flutter plugin integration
- ✅ Build settings optimization

## 🔧 Required Actions for iOS Development

### 1. macOS Development Environment
To build and test iOS apps, you need:
- **macOS** (Monterey 12.0 or later recommended)
- **Xcode** (14.0 or later)
- **CocoaPods** (`sudo gem install cocoapods`)

### 2. Firebase iOS Setup
Update `ios/Runner/GoogleService-Info.plist`:
```xml
<key>CLIENT_ID</key>
<string>YOUR_IOS_CLIENT_ID.apps.googleusercontent.com</string>
<key>REVERSED_CLIENT_ID</key>
<string>com.googleusercontent.apps.YOUR_REVERSED_CLIENT_ID</string>
<key>API_KEY</key>
<string>YOUR_IOS_API_KEY</string>
```

### 3. Apple Developer Account
- Sign up for Apple Developer Program ($99/year)
- Create App ID: `com.eeil.ecoplug`
- Configure capabilities: Push Notifications, Background App Refresh, etc.

### 4. Code Signing
- Create development and distribution certificates
- Create provisioning profiles
- Configure in Xcode project settings

## 🚀 Build Commands (on macOS)

### Install Dependencies
```bash
cd ios
pod install
```

### Debug Build
```bash
flutter build ios --debug
```

### Release Build
```bash
flutter build ios --release
```

### Run on Simulator
```bash
flutter run -d ios
```

### Build for App Store
```bash
flutter build ipa
```

## 📱 Testing on iOS

### Simulator Testing
1. Open Xcode
2. Open `ios/Runner.xcworkspace`
3. Select iOS Simulator
4. Run the app

### Device Testing
1. Connect iOS device
2. Trust developer certificate on device
3. Run: `flutter run -d [device-id]`

## 🔐 App Store Submission

### Pre-submission Checklist
- [ ] App icons generated and optimized
- [ ] Privacy policy URL configured
- [ ] App Store screenshots prepared
- [ ] App description and metadata ready
- [ ] In-app purchases configured (if applicable)
- [ ] TestFlight beta testing completed

### Submission Process
1. Archive app in Xcode
2. Upload to App Store Connect
3. Fill app metadata
4. Submit for review

## 🛠 Troubleshooting

### Common Issues
1. **CocoaPods not found**: Install with `sudo gem install cocoapods`
2. **Build errors**: Run `pod install` in ios directory
3. **Signing issues**: Check certificates and provisioning profiles
4. **Firebase errors**: Verify GoogleService-Info.plist configuration

### Support
- Flutter iOS documentation: https://docs.flutter.dev/deployment/ios
- Apple Developer documentation: https://developer.apple.com/documentation/
