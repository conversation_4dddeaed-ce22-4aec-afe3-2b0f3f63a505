<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Ecoplug</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Ecoplug</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>io.flutter.embedded_views_preview</key>
	<true/>
	<!-- Location Permissions -->
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Ecoplug needs access to your location to find nearby charging stations and provide accurate directions.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Ecoplug needs location access to track charging sessions and provide notifications when you're near charging stations.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Ecoplug uses your location to find nearby charging stations, provide navigation, and track charging sessions.</string>

	<!-- Camera and Photo Library Permissions -->
	<key>NSCameraUsageDescription</key>
	<string>Ecoplug needs camera access to scan QR codes at charging stations and capture photos for support tickets.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Ecoplug needs photo library access to upload images for profile pictures and support documentation.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Ecoplug needs permission to save charging session receipts and QR codes to your photo library.</string>

	<!-- Network and Connectivity -->
	<key>NSLocalNetworkUsageDescription</key>
	<string>Ecoplug needs local network access to communicate with charging stations and payment terminals.</string>

	<!-- Contacts (if needed for sharing) -->
	<key>NSContactsUsageDescription</key>
	<string>Ecoplug needs contacts access to share charging station information with your friends and family.</string>

	<!-- Microphone (if needed for voice features) -->
	<key>NSMicrophoneUsageDescription</key>
	<string>Ecoplug needs microphone access for voice commands and customer support calls.</string>

	<!-- Face ID / Touch ID -->
	<key>NSFaceIDUsageDescription</key>
	<string>Ecoplug uses Face ID for secure authentication and payment authorization.</string>

	<!-- Google Maps API Key -->
	<key>com.google.android.geo.API_KEY</key>
	<string>AIzaSyBCU1EjZJ8ZRmWpYxaK55nAKKcehh-cTwM</string>

	<!-- Notification permissions -->
	<key>NSUserNotificationAlertStyle</key>
	<string>alert</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>background-processing</string>
		<string>background-fetch</string>
		<string>remote-notification</string>
		<string>location</string>
	</array>

	<!-- PhonePe SDK Configuration -->
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>ppemerchantsdkv1</string>
		<string>ppemerchantsdkv2</string>
		<string>ppemerchantsdkv3</string>
		<string>paytmmp</string>
		<string>gpay</string>
		<!-- Cashfree SDK UPI App Schemes -->
		<string>phonepe</string>
		<string>tez</string>
		<string>bhim</string>
		<string>credpay</string>
	</array>

	<!-- URL Scheme for PhonePe callback -->
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>ecoplugapp</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>ecoplugapp</string>
			</array>
		</dict>
	</array>
</dict>
</plist>
